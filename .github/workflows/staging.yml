name: Staging Workflow 🛍️

on:
    pull_request:
        branches:
            - main
    push:
        branches:
            - main

jobs:
    check-lint:
        runs-on: ubuntu-latest
        timeout-minutes: 1
        steps:
            - name: Checkout pull request branch
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4.1.0
              with:
                  node-version: "18"

            - name: Install Prettier
              run: npm install --save-dev prettier

            - name: Check Prettier
              run: npx prettier --ignore-path .prettierignore --check .

    check-typescript:
        runs-on: ubuntu-latest
        timeout-minutes: 1
        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4.1.0
              with:
                  node-version: "18"

            - name: Install dependencies
              run: |
                  npm install

            - name: Check TypeScript
              run: |
                  npm run check
