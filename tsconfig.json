{
    "extends": "expo/tsconfig.base",
    "compilerOptions": {
        // "jsx": "react-native",
        "paths": {
            "@assets/*": ["./assets/*"],
            "@app/*": ["./app/*"],
            "@components/*": ["./components/*"],
            "@api/*": ["./api/*"],
            "@context/*": ["./context/*"],
            "@styles/*": ["./styles/*"]
        }
    },
    "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"]
}
