import { createContext, useContext } from "react";

interface OrdersContextType {
    modalMode: "edit" | "delete" | "send" | "restore";
    selectedOrdersId: string[];
    selectionMode: boolean;
    setDeleteId: (id: string) => void;
    setModalMode: (mode: "edit" | "delete" | "send" | "restore") => void;
    setSelectedOrdersId: (ids: string[]) => void;
    setVisible: (visible: boolean) => void;
    visible: boolean;
}

export const OrdersContext = createContext<OrdersContextType | undefined>(undefined);

export const useOrdersContext = () => {
    const context = useContext(OrdersContext);
    if (!context) {
        throw new Error("useOrdersContext must be used within an OrdersProvider");
    }
    return context;
};
