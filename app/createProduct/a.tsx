// import colors from "@styles/colors";
// import React, { useState } from "react";
// import { View, Text, TextInput, StyleSheet, ScrollView, TouchableOpacity, Image } from "react-native";
// import Svg, { Path } from "react-native-svg";

// import DetailsContent from "@components/createOrder/detailsContent";
// import OptionsBundlesContent from "@components/createOrder/optionsBundleContent";
// import Reviews from "@components/createOrder/reviews";
// import RelatedProducts from "@components/createOrder/relatedProducts";

// const ProductForm = () => {
//     const [selectedTab, setSelectedTab] = useState("Details");

//     const handleTabPress = (tabName: string) => {
//         setSelectedTab(tabName);
//     };

//     const renderTabContent = () => {
//         switch (selectedTab) {
//             case "Details":
//                 return <DetailsContent />;
//             case "Options/Bundles":
//                 return <OptionsBundlesContent />;
//             case "Related Products":
//                 return <RelatedProducts />;
//             case "Reviews":
//                 return <Reviews />;
//             default:
//                 return null;
//         }
//     };

//     return (
//         <ScrollView style={styles.container}>
//             <ScrollView style={styles.tabContainer} showsHorizontalScrollIndicator={false} horizontal>
//                 {["Details", "Options/Bundles", "Related Products", "Reviews", "Theme"].map((tabName) => (
//                     <TouchableOpacity
//                         key={tabName}
//                         style={[styles.tab, selectedTab === tabName && styles.selectedTab]}
//                         onPress={() => handleTabPress(tabName)}
//                     >
//                         <Text style={[styles.tabText, selectedTab === tabName && styles.selectedTabText]}>
//                             {tabName}
//                         </Text>
//                     </TouchableOpacity>
//                 ))}
//             </ScrollView>

//             {renderTabContent()}
//         </ScrollView>
//     );
// };

// const styles = StyleSheet.create({
//     container: {
//         backgroundColor: colors.gray[50],
//     },
//     label: {
//         fontSize: 16,
//         paddingBottom: 5,
//         fontWeight: "bold",
//     },
//     input: {
//         borderWidth: 0.5,
//         borderColor: colors.gray[500],
//         borderRadius: 5,
//         flex: 1,
//         paddingBottom: 2,
//     },
//     row: {
//         flexDirection: "row",
//         justifyContent: "space-between",
//         gap: 10,
//         paddingVertical: 10,
//     },
//     column: {
//         flex: 1,
//         paddingTop: 10,
//         paddingBottom: 10,
//     },

//     tabContainer: {
//         borderTopWidth: 1,
//         borderColor: "#ccc",
//         paddingBottom: 10,
//     },
//     tab: {
//         flex: 1,
//         padding: 16,
//         alignItems: "center",
//         justifyContent: "center",
//         borderBottomWidth: 1,
//         borderBottomColor: colors.primary[600],
//         paddingTop: 10,
//     },
//     selectedTab: {
//         backgroundColor: colors.primary[600],
//         borderBottomColor: colors.primary[600],
//     },
//     tabText: {
//         fontSize: 14,
//         color: "black",
//     },
//     selectedTabText: {
//         fontWeight: "bold",
//         color: "white",
//     },
// });

// export default ProductForm;
import { View, Text } from "react-native";
import React from "react";

const a = () => {
    return (
        <View>
            <Text>a</Text>
        </View>
    );
};

export default a;
