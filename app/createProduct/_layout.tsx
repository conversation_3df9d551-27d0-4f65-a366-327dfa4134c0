import React from "react";
import { router, Stack } from "expo-router";

import {
    createMaterialTopTabNavigator,
    MaterialTopTabNavigationOptions,
    MaterialTopTabNavigationEventMap,
} from "@react-navigation/material-top-tabs";

import { ParamListBase, TabNavigationState } from "@react-navigation/native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { withLayoutContext } from "expo-router";
import ModalHeader from "@components/Navigation/ModalHeader";
const { Navigator } = createMaterialTopTabNavigator();

export const MaterialTopTabs = withLayoutContext<
    MaterialTopTabNavigationOptions,
    typeof Navigator,
    TabNavigationState<ParamListBase>,
    MaterialTopTabNavigationEventMap
>(Navigator);

const _layout = () => {
    // const { addOrder } = useOrderStore();
    return (
        <>
            <Stack.Screen
                options={{
                    headerTitle: "Create Product",
                    header: (props) => {
                        return (
                            <ModalHeader
                                backAction={() => {
                                    router.navigate("/products");
                                }}
                                modalButtonLabel="save"
                                modalButtonAction={() => {
                                    console.log("save");
                                }}
                                {...props}
                            />
                        );
                    },
                }}
            />
            {/* <Stack screenOptions={{ headerShown: false }}>
                <Stack.Screen
                    name="index"
                    options={{
                        headerTitle: "Create Product",
                        headerShown: true,
                        header: ({ navigation, options, route, back }) => {
                            return (
                                <ModalHeader
                                    navigation={navigation}
                                    options={options}
                                    route={route}
                                    back={back}
                                    modalButtonLabel="save"
                                    // modalButtonAction={() => {
                                    //     if (!route.params["newOrder"]) {
                                    //         Toast.show({
                                    //             type: "error",
                                    //             text1: "No product to save",
                                    //         });
                                    //         return;
                                    //     }

                                    //     const newOrder = JSON.parse(route.params["newOrder"]);

                                    //     if (newOrder.cart.length === 0) {
                                    //         Toast.show({
                                    //             type: "error",
                                    //             text1: "Cart is empty",
                                    //         });
                                    //         return;
                                    //     }
                                    //     // addOrder(newOrder);
                                    //     navigation.goBack();
                                    // }}
                                />
                            );
                        },
                    }}
                />
            </Stack> */}
            <MaterialTopTabs
                id={undefined}
                initialRouteName="detailsContent"
                screenOptions={{
                    tabBarStyle: { backgroundColor: colors.gray[50] },
                    tabBarLabelStyle: [
                        typography.baseText,
                        { textTransform: "capitalize" },
                        typography.sm,
                        typography.fontSemibold,
                        typography.lineHeight5,
                    ],
                    swipeEnabled: false,
                    tabBarScrollEnabled: true,
                    tabBarActiveTintColor: colors.primary[500],
                    tabBarInactiveTintColor: colors.gray[500],
                    tabBarIndicatorStyle: { backgroundColor: colors.primary[500] },
                    tabBarAndroidRipple: {
                        borderless: true,
                        color: colors.primary[100],
                    },
                }}
            >
                <MaterialTopTabs.Screen name="detailsContent" options={{ tabBarLabel: "Details" }} />
                <MaterialTopTabs.Screen name="optionsBundleContent" options={{ tabBarLabel: "Options/Bundle" }} />
                <MaterialTopTabs.Screen name="relatedProducts" options={{ tabBarLabel: "Related Products" }} />
                <MaterialTopTabs.Screen name="reviews" options={{ tabBarLabel: "Reviews" }} />
            </MaterialTopTabs>
        </>
    );
};

export default _layout;
