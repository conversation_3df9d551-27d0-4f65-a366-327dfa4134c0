import React, { useEffect, useLayoutEffect, useState } from "react";
import { ScrollView, StyleSheet, TextStyle } from "react-native";
import { typography } from "@styles/typography";
import colors from "@styles/colors";
import { Order } from "@components/orders/types";
import { useOrderStore } from "../../store/orders";
import { useStoreStore } from "../../store/storeStore";
import { router } from "expo-router";
import { EditOrderDetails } from "@components/editOrders/EditOrderDetails";
import { EditCustomerDetails } from "@components/editOrders/EditCustomerDetails";
import { EditDeliveryDetails } from "@components/editOrders/EditDeliveryDetails";
import { capitalizeFirstLetter } from "@components/editOrders/utils";

const index = () => {
    const { order, getProducts, products } = useOrderStore();
    const { store, auth } = useStoreStore();

    const [updatedOrderValues, setUpdatedOrderValues] = useState<Partial<Order>>({
        customer: {
            name: "",
            phone: "",
            address: "",
            email: "",
            city: "",
            town: "",
            stopdesk: "",
            country: "",
            postalCode: 0,
        },
        cart: [],
        status: "pending",
        total: {
            deliveryPrice: 0,
            deliveryCost: 0,
            totalPrice: 0,
        },
        deliveryCompany: "none",
        note: "",
    });

    const [integratedCompanies, setIntegratedCompanies] = useState<
        {
            value: string;
            label: string;
        }[]
    >([]);

    useEffect(() => {
        getProducts();
    }, []);

    useEffect(() => {
        if (store && auth === "loaded") {
            const _integrations = store.integrations
                .filter(
                    (integration) =>
                        !["microsoft-clarity", "google-analytics", "facebook", "tiktok"].includes(integration.ref) &&
                        integration.integrated === true
                )
                .map((integration) => ({
                    value: integration.ref,
                    label: capitalizeFirstLetter(integration.ref),
                }));
            setIntegratedCompanies(_integrations);
        }
    }, [store, auth]);

    useEffect(() => {
        router.setParams({ newOrder: JSON.stringify(updatedOrderValues) });
    }, [updatedOrderValues]);

    return (
        <ScrollView contentContainerStyle={styles.container}>
            <EditOrderDetails
                pageType="create"
                integratedCompanies={integratedCompanies}
                order={order}
                setUpdatedOrderValues={setUpdatedOrderValues}
                updatedOrderValues={updatedOrderValues}
            />
            <EditCustomerDetails
                order={order}
                setUpdatedOrderValues={setUpdatedOrderValues}
                updatedOrderValues={updatedOrderValues}
            />
            <EditDeliveryDetails
                order={order}
                products={products}
                setUpdatedOrderValues={setUpdatedOrderValues}
                store={store}
                updatedOrderValues={updatedOrderValues}
            />
        </ScrollView>
    );
};

export default index;

export const inputStyles: TextStyle = {
    width: "100%",
    height: 50,
    textAlignVertical: "top",
    padding: 10,
    borderWidth: 1,
    borderColor: colors.gray[400],
    borderRadius: 5,
    // backgroundColor: colors.white,
};

const emailStyles = {
    width: "100%",
    height: 50,
    textAlignVertical: "top",
    padding: 10,
    borderWidth: 1,
    borderColor: colors.gray[400],
    borderRadius: 5,
    backgroundColor: colors.gray[100],
};

export const styles = StyleSheet.create({
    container: {
        paddingVertical: 20,
        paddingHorizontal: 10,
        gap: 20,
        backgroundColor: colors.gray[100],
    },
    orderContainer: {
        backgroundColor: colors.white,
        borderRadius: 13,
        padding: 15,
        gap: 10,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 5,
        elevation: 3,
        paddingVertical: 20,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: colors.primary[500],
        textAlign: "center",
    },
    label: {
        fontSize: 14,
        fontWeight: "bold",
        color: colors.gray[800],
    },
    element: {
        width: "100%",
        backgroundColor: colors.white,
        gap: 5,
        borderRadius: 5,
        paddingTop: 5,
    },
    iconContainer: {
        height: "100%",
        justifyContent: "center",
        paddingRight: 10,
    },
    inputAndroid: {
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.black,
        borderWidth: 1,
        borderColor: colors.gray[400],
        borderRadius: 5,
        padding: 10,
        backgroundColor: colors.white,
    },
    actionsContainer: {
        flexDirection: "column",
        justifyContent: "space-between",
    },
    actionButton: {
        flex: 1,
    },
    productContainer: {
        backgroundColor: colors.white,

        alignItems: "center",
        borderWidth: 1,
        borderColor: colors.gray[300],
        borderRadius: 10,
        overflow: "hidden",
        width: 140,
    },
    productImage: {
        width: 140,
        height: 90,
    },
    productInfo: {
        paddingVertical: 5,
        paddingHorizontal: 10,
        justifyContent: "space-between",
        alignItems: "stretch",
        alignSelf: "stretch",
        gap: 5,
    },
    productName: {
        fontSize: 14,
        fontWeight: "bold",
        color: colors.black,
    },
    productDetails: {
        fontSize: 12,
        color: colors.gray[600],
    },
    deleteButton: {
        opacity: 0.8,
        borderRadius: 100,
        backgroundColor: colors.white,
        position: "absolute",
        top: 5,
        right: 5,
        padding: 5,
    },
    totalPriceCard: {
        backgroundColor: colors.white,
        padding: 10,
        borderRadius: 5,
        flex: 1,
        flexDirection: "row",
        justifyContent: "space-between",
    },
    totalPriceTextLeft: {
        fontSize: 18,
        fontWeight: "bold",
        color: colors.black,
        alignItems: "flex-start",
    },
    totalPriceTextRight: {
        fontSize: 18,
        fontWeight: "bold",
        color: colors.black,
        alignItems: "flex-end",
    },
});
