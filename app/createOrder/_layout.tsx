import React from "react";
import { Stack, useRouter } from "expo-router";
import { useOrderStore } from "../../store/orders";
import ModalHeader from "@components/Navigation/ModalHeader";
import Toast from "react-native-toast-message";
import { GestureHandlerRootView } from "react-native-gesture-handler";

const _layout = () => {
    const { addOrder } = useOrderStore();
    const router = useRouter();
    return (
        <>
            <GestureHandlerRootView>
                <Stack.Screen options={{ headerShown: false }} />
                <Stack screenOptions={{ headerShown: false }}>
                    <Stack.Screen
                        name="index"
                        options={{
                            headerTitle: "Create Order",
                            headerShown: true,
                            header: ({ navigation, options, route, back }) => {
                                return (
                                    <ModalHeader
                                        navigation={navigation}
                                        options={options}
                                        route={route}
                                        back={back}
                                        modalButtonLabel="save"
                                        modalButtonAction={() => {
                                            if (!route.params["newOrder"]) {
                                                Toast.show({
                                                    type: "error",
                                                    text1: "No order to save",
                                                });
                                                return;
                                            }

                                            const newOrder = JSON.parse(route.params["newOrder"]);

                                            if (newOrder.cart.length === 0) {
                                                Toast.show({
                                                    type: "error",
                                                    text1: "Cart is empty",
                                                });
                                                return;
                                            }

                                            if (newOrder.status === "exchange" && !newOrder.deliveryCompany) {
                                                Toast.show({
                                                    type: "error",
                                                    text1: "Select a Delivery Company",
                                                });
                                                return;
                                            }

                                            addOrder(newOrder);
                                            navigation.goBack();
                                            // router.replace({
                                            //     pathname: "/(mainNav)/orders",
                                            //     // params: { refresh: "true" }
                                            // });
                                        }}
                                    />
                                );
                            },
                        }}
                    />
                </Stack>
            </GestureHandlerRootView>
        </>
    );
};

export default _layout;
