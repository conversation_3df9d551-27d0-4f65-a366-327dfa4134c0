import { View, Text, StyleSheet, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-native";
import React, { useCallback, useEffect, useState } from "react";
import { router, Stack, useFocusEffect, useLocalSearchParams } from "expo-router";
import Header from "@components/Navigation/Header";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import colors from "@styles/colors";
import Input from "@components/inputs/textInputs/Input";
import { AddIcon } from "@components/icons/BudgetManagerIcons";
import Divider from "@components/dividers/Divider";
import { useStoreStore } from "../../store/storeStore";
import { Preset, PresetsRecord } from "@app/presets";
import api from "@api/api";
import { Store } from "../../types/Store";

export const names = [
    "",
    "Adrar",
    "Chlef",
    "Laghouat",
    "Oum El Bouaghi",
    "Batna",
    "Bejaia",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Tlemcen",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>izi Ouzou",
    "Al<PERSON>",
    "<PERSON><PERSON>lfa",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>a",
    "<PERSON>kda",
    "Sidi Bel A<PERSON>s",
    "<PERSON><PERSON>",
    "G<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON>dea",
    "<PERSON>aganem",
    "<PERSON>'<PERSON><PERSON>",
    "<PERSON>scara",
    "<PERSON>uarg<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON> <PERSON><PERSON><PERSON>",
    "<PERSON>li<PERSON>",
    "<PERSON><PERSON>j <PERSON>u <PERSON><PERSON><PERSON>j",
    "<PERSON><PERSON><PERSON>",
    "<PERSON> Tarf",
    "<PERSON>do<PERSON>",
    "Tissemsilt",
    "El Oued",
    "Khenchela",
    "Souk Ahras",
    "Tipaza",
    "Mila",
    "Ain Defla",
    "Naama",
    "Ain Temouchent",
    "Ghardaia",
    "Relizane",
    "Timimoun",
    "Bordj Badji Mokhtar",
    "Ouled Djellal",
    "Beni Abbes",
    "In Salah",
    "In Guezzam",
    "Touggourt",
    "Djanet",
    "El M ghaier",
    "El Meniaa",
];
export const Item = ({
    name,
    text,
    stopdesk,
    delivery,
    setApplyValues,
    setapplyToAll,
    updateData,
    index,
    setPresetName,
}: {
    setPresetName: React.Dispatch<React.SetStateAction<string>>;
    index: number;
    name: string;
    text: string;
    stopdesk: number;
    delivery: number;
    setApplyValues?: React.Dispatch<
        React.SetStateAction<{
            stopdesk: number;
            delivery: number;
        }>
    >;
    setapplyToAll?: React.Dispatch<React.SetStateAction<boolean>>;
    updateData: (
        index: number,
        value: {
            stopdesk: number;
            delivery: number;
        }
    ) => void;
}) => {
    const [_name, setName] = useState(name);
    const [_stopdesk, setStopDesk] = useState(stopdesk);
    const [_delivery, setDelivery] = useState(delivery);

    useEffect(() => {
        if (index !== 0) updateData(index, { delivery: _delivery, stopdesk: _stopdesk });
    }, [_delivery, _stopdesk]);

    if (text === "")
        return (
            <View style={{ gap: 10 }}>
                <View>
                    <Text style={{ ...styles?.label }}>Preset Name</Text>
                    <Input
                        noBottomPadding
                        placeholder="Preset name"
                        inputProps={{
                            defaultValue: name,
                            onChangeText: (e) => {
                                setPresetName(e);
                            },
                        }}
                    />
                </View>
                <View style={styles.row}>
                    <View style={styles.column}>
                        <Text style={styles?.label}>Stopdesk</Text>
                        <Input
                            noBottomPadding
                            placeholder="0"
                            inputProps={{
                                defaultValue: "",
                                inputMode: "numeric",
                            }}
                            selectTextOnFocus
                            onChange={(value) => {
                                setStopDesk(parseInt(value));
                            }}
                        />
                    </View>
                    <View style={styles.column}>
                        <Text style={styles?.label}>Delivery</Text>
                        <Input
                            noBottomPadding
                            placeholder="0"
                            inputProps={{
                                defaultValue: "",
                                inputMode: "numeric",
                            }}
                            selectTextOnFocus
                            onChange={(value) => {
                                // setFormValues({ ...formValues, deliveryCost: parseInt(value) });
                                setDelivery(parseInt(value));
                            }}
                        />
                    </View>
                </View>
                <TextButton
                    label="Apply to all"
                    onPress={() => {
                        setApplyValues({ delivery: _delivery, stopdesk: _stopdesk });
                        setapplyToAll(true);
                    }}
                    leftIcon={<AddIcon color="white" size="18" />}
                />
            </View>
        );
    return (
        <>
            <View key={text} style={{ gap: 2 }}>
                <Text style={{ ...styles?.label, fontSize: 16 }}>{text}</Text>
                <View style={styles.row}>
                    <View style={styles.column}>
                        <Text style={styles?.label}>Stopdesk</Text>
                        <Input
                            noBottomPadding
                            placeholder="0"
                            inputProps={{
                                value: stopdesk.toString(),
                                inputMode: "numeric",
                            }}
                            selectTextOnFocus
                            onChange={(value) => {
                                setStopDesk(parseInt(value));
                            }}
                        />
                    </View>
                    <View style={styles.column}>
                        <Text style={styles?.label}>Delivery</Text>
                        <Input
                            selectTextOnFocus
                            noBottomPadding
                            placeholder="0"
                            inputProps={{
                                value: delivery.toString(),
                                inputMode: "numeric",
                            }}
                            onChange={(value) => {
                                setDelivery(parseInt(value));
                            }}
                        />
                    </View>
                </View>
            </View>
        </>
    );
};

const index = () => {
    const { name }: { name: string } = useLocalSearchParams();

    const { store, updateStore } = useStoreStore();

    const [presetName, setPresetName] = useState(name);
    const [preset, setPreset] = useState<Preset>();
    const [applyToAll, setapplyToAll] = useState<boolean>(false);
    const [applyValues, setApplyValues] = useState<{ stopdesk: number; delivery: number }>();

    useFocusEffect(
        useCallback(() => {
            const presets = store?.integrations?.find((integration) => {
                return integration?.ref === "dz-wilaya-presets";
            }).fields?.presets;

            const _presets: PresetsRecord = presets ? presets : {};
            _presets[name] && setPreset(_presets[name]);
        }, [])
    );

    useEffect(() => {
        if (applyToAll) {
            setData(
                data.map(([city]) => {
                    return [city, { ...applyValues }];
                })
            );
            setapplyToAll(false);
        }
    }, [applyToAll]);

    const [data, setData] = useState<
        [
            string,
            {
                stopdesk: number;
                delivery: number;
            }
        ][]
    >([]);

    useEffect(() => {
        const _data = [...Object.entries(preset ? preset : {})];
        _data.unshift(["", { delivery: 0, stopdesk: 0 }]);
        setData([..._data]);
    }, [preset]);

    const retransformData = () => {
        const _data = [...data];
        _data.shift();
        return Object.fromEntries(_data);
    };

    const updateData = (index: number, value: { stopdesk: number; delivery: number }) => {
        data[index] = [data[index][0], value];
        setData([...data]);
    };

    const update = async () => {
        let integrations = [...store.integrations];

        let _index = 0;
        const presets: Record<string, PresetsRecord> = integrations.find((integration, index) => {
            _index = index;
            return integration.ref === "dz-wilaya-presets";
        }).fields?.presets;
        if (name === presetName) {
            integrations[_index].fields.presets[name] = retransformData();
        } else {
            const _presets = Object.entries(presets).map((preset) => {
                if (preset[0] === name) {
                    preset[0] = presetName;
                    preset[1];
                }
                return preset;
            });
            integrations[_index].fields.presets = Object.fromEntries(_presets);
            integrations[_index].fields.presets[presetName] = retransformData();
        }

        updateStore({ integrations, slug: store.slug });
    };

    return (
        <>
            <Stack.Screen
                options={{
                    headerTitle: "Edit Preset",
                    header: (props) => {
                        return (
                            <Header
                                {...props}
                                backPath={"/presets"}
                                modalButtonLabel="Save"
                                modalButtonAction={() => {
                                    update();
                                }}
                            />
                        );
                    },
                }}
            />

            <FlatList
                style={{ backgroundColor: colors.gray[50] }}
                contentContainerStyle={{ paddingHorizontal: 15, gap: 20 }}
                renderItem={({ item, index }) => {
                    return (
                        <Item
                            delivery={item[1].delivery}
                            index={index}
                            key={index}
                            name={presetName}
                            setPresetName={setPresetName}
                            stopdesk={item[1].stopdesk}
                            text={item[0]}
                            updateData={updateData}
                            setApplyValues={setApplyValues}
                            setapplyToAll={setapplyToAll}
                        />
                    );
                }}
                data={data}
            />
        </>
    );
};
const styles = StyleSheet.create({
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        gap: 10,
    },
    column: {
        flex: 1,
    },
    label: {
        fontSize: 14,
        fontWeight: "bold",
        color: colors.primary[800],
        paddingBottom: 5,
    },
    container: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
});

export default index;
