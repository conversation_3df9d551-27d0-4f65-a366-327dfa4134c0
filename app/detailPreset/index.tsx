import { View, Text, StyleSheet, FlatList } from "react-native";
import React, { useCallback, useEffect, useState } from "react";
import { router, Stack, useFocusEffect } from "expo-router";
import Header from "@components/Navigation/Header";
import { useLocalSearchParams } from "expo-router";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import colors from "@styles/colors";
import Divider from "@components/dividers/Divider";
import { typography } from "@styles/typography";
import { useStoreStore } from "../../store/storeStore";
import { Preset, PresetsRecord } from "@app/presets";
import ModalBase from "@components/Navigation/ModalView/ModalBase";

export const names = [
    "Adrar",
    "Chlef",
    "Laghouat",
    "<PERSON>um El Bouaghi",
    "Batna",
    "Bejaia",
    "Biskra",
    "<PERSON><PERSON>r",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>i Bel A<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "G<PERSON><PERSON>",
    "<PERSON>",
    "Medea",
    "Mostaganem",
    "<PERSON>'<PERSON><PERSON>",
    "<PERSON>scara",
    "<PERSON>uarg<PERSON>",
    "<PERSON><PERSON>",
    "El <PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>j <PERSON><PERSON> <PERSON><PERSON><PERSON>j",
    "Bo<PERSON><PERSON>",
    "<PERSON> <PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "El <PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON>uk <PERSON><PERSON>",
    "Tipaza",
    "Mila",
    "Ain Defla",
    "Naama",
    "Ain Temouchent",
    "Ghardaia",
    "Relizane",
    "Timimoun",
    "Bordj Badji Mokhtar",
    "Ouled Djellal",
    "Beni Abbes",
    "In Salah",
    "In Guezzam",
    "Touggourt",
    "Djanet",
    "El M ghaier",
    "El Meniaa",
];

const Item = ({ text, stopdesk, delivery }: { text: string; stopdesk: number; delivery: number }) => {
    return (
        <View key={text} style={{ gap: 10, paddingBottom: 10 }}>
            <Text style={{ ...styles.title, fontSize: 16 }}>{text}</Text>

            <View style={{ ...styles.container }}>
                <Text style={{ ...styles?.label, fontSize: 14 }}>StopDesk</Text>
                <Text
                    style={{
                        ...styles?.label,
                    }}
                >
                    {stopdesk}
                </Text>
            </View>
            <View style={styles.container}>
                <Text style={{ ...styles?.label, fontSize: 14 }}>Delivery</Text>
                <Text
                    style={{
                        ...styles?.label,
                    }}
                >
                    {delivery}
                </Text>
            </View>
        </View>
    );
};

const index = () => {
    const { name }: { name: string } = useLocalSearchParams();
    const { store, updateStore } = useStoreStore();

    const [preset, setPreset] = useState<Preset>();

    useFocusEffect(
        useCallback(() => {
            const presets = store?.integrations?.find((integration) => {
                return integration?.ref === "dz-wilaya-presets";
            }).fields?.presets;

            const _presets: PresetsRecord = presets ? presets : {};
            _presets[name] && setPreset(_presets[name]);
        }, [store])
    );
    useEffect(() => {
        const presets = store?.integrations?.find((integration) => {
            return integration?.ref === "dz-wilaya-presets";
        }).fields?.presets;

        const _presets: PresetsRecord = presets ? presets : {};
        _presets[name] && setPreset(_presets[name]);
    }, [store]);

    const [visible, setVisible] = useState(false);

    const deletePreset = () => {
        let _index = 0;
        const presets = store?.integrations?.find((integration, index) => {
            _index = index;
            return integration?.ref === "dz-wilaya-presets";
        }).fields?.presets;

        delete presets[name];

        const { integrations } = store;

        integrations[_index].fields.presets = { ...presets };

        updateStore({ integrations: integrations, slug: store.slug });
        router.navigate("/presets");
    };

    return (
        <>
            <Stack.Screen
                options={{
                    headerTitle: name,
                    header: (props) => {
                        return (
                            <Header
                                {...props}
                                modalButtonLabel="Delete"
                                modalButtonTextColor="red"
                                style={{ paddingBottom: 0 }}
                                modalButtonAction={() => {
                                    setVisible(true);
                                }}
                            />
                        );
                    },
                }}
            />

            <View
                style={{
                    backgroundColor: colors.gray[50],
                    gap: 10,
                    paddingHorizontal: 10,
                    paddingBottom: 5,
                }}
            >
                <ModalBase visible={visible} setVisible={setVisible}>
                    <View style={{ width: "100%", gap: 20 }}>
                        <Text style={[typography.fontSemibold, typography.md, { textAlign: "center" }]}>
                            Delete "{name}"
                        </Text>
                        <Text style={[typography.fontNormal, typography.sm]}>
                            {`Are you sure you want to delete this preset?\nAll Values will be lost`}
                        </Text>
                        <View style={{ flexDirection: "row", gap: 5 }}>
                            <TextButton
                                color={colors.gray[600]}
                                label="Cancel"
                                variant="text"
                                onPress={() => {
                                    setVisible(false);
                                }}
                                style={{ flex: 1 }}
                            />
                            <TextButton
                                label="Delete"
                                color={colors.red[500]}
                                variant="contained"
                                style={{ flex: 1 }}
                                onPress={() => {
                                    deletePreset();
                                    setVisible(false);
                                }}
                            />
                        </View>
                    </View>
                </ModalBase>
                <View style={{ ...styles.row }}>
                    <View style={styles.column}>
                        <TextButton
                            label="Duplicate"
                            variant="outlined"
                            onPress={() => {
                                router.navigate({
                                    pathname: "/createPreset",
                                    params: { preset: JSON.stringify(preset), name },
                                });
                            }}
                        />
                    </View>
                    <View style={styles.column}>
                        <TextButton
                            label="Edit"
                            onPress={() => {
                                router.navigate({ pathname: "/editPreset", params: { name } });
                            }}
                        />
                    </View>
                </View>
            </View>
            <FlatList
                style={{ backgroundColor: colors.gray[50] }}
                contentContainerStyle={{ paddingHorizontal: 15, gap: 10, paddingBottom: 50 }}
                renderItem={({ item: [city, { stopdesk, delivery }], index }) => {
                    return <Item text={city} stopdesk={stopdesk} delivery={delivery} key={index} />;
                }}
                data={Object.entries(preset ? preset : {})}
                ItemSeparatorComponent={Divider}
            />
        </>
    );
};

export default index;
const styles = StyleSheet.create({
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        paddingTop: 10,
        gap: 10,
    },
    column: {
        flex: 1,
    },
    title: {
        fontSize: 16,

        fontFamily: typography.fontBold.fontFamily,
        color: colors.primary[800],
    },
    label: {
        fontSize: 16,
        fontFamily: typography.fontNormal.fontFamily,
        color: colors.primary[800],
    },
    container: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
});
