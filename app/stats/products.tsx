import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import BottomSheet, { BottomSheetScrollView, BottomSheetView } from "@gorhom/bottom-sheet";
import {
    getDaysSinceStartOfWeek,
    getDaysSinceStartOfMonth,
    getDaysSinceNovember13,
    fetchStats,
    getPeriod,
} from "../../utils/statsUtils";
import { FormatedProductProps } from "../../store/statsStoreProps";
import { GestureHandlerRootView, ScrollView } from "react-native-gesture-handler";

import { ActivityIndicator, RefreshControl, Text, View, ScrollView as NativeScrollView } from "react-native";

import StatsCard from "@components/stats/StatsCard";
import DurationChart from "@components/stats/DurationChart";
import { typography } from "@styles/typography";
import colors from "@styles/colors";
import HorizontalPercentageCharte from "@components/stats/VerticalPercentageCharte";
import HomeChartV2 from "@components/charts/HomeChart/HomeChartV2";
import DeliveryCard from "@components/stats/DeliveryCard";
import StatsPicker from "@components/inputs/select/StatsPicker";
import FilterChip from "@components/stats/FilterChip";
import { Path, Svg } from "react-native-svg";
import {
    TotalIcon,
    ProfitGainIcon,
    MoneyIcon,
    DepositIcon,
    TransitIcon,
    DeliveredIcon,
    ReturendIcon,
    ProfitLossIcon,
} from "@components/icons/StatsIcons";
import { handleStatus } from "@components/inputs/chips/Tag/Tag";
import { useOrderStore } from "../../store/orders";
import { useStoreStore } from "../../store/storeStore";

const products = () => {
    const bottomSheetRef = useRef<BottomSheet>(null);

    const { store } = useStoreStore();

    const initialFilters = [
        { label: "this week", value: getDaysSinceStartOfWeek() },
        { label: "last 7 days", value: 7 },
        { label: "this month", value: getDaysSinceStartOfMonth() },
        { label: "last 30 days", value: 30 },
        { label: "last three months", value: getDaysSinceStartOfMonth() + 61 },
        { label: "all time", value: getDaysSinceNovember13() },
    ];

    const initialActiveFilter = { label: "this week", value: getDaysSinceStartOfWeek() };

    const [loading, setLoading] = useState<boolean>(false);
    const [filters, setFilters] = useState<{ label: string; value: number }[]>(initialFilters);
    const [activeFilter, setActiveFilter] = useState(initialActiveFilter);

    const [formatedData, setFormatedData] = useState<Partial<ReturnType<typeof formatData>>>({});
    const [products, setProducts] = useState<{ label: string; value: string }[]>([]); // Remove default "all" option
    const [activeProduct, setActiveProduct] = useState<string>("store");

    // const [storeProductsList, setStoreProductsList] = use

    const fetchData = async () => {
        setLoading(true);
        fetchStats(getPeriod(activeFilter.value))
            .then((data) => {
                setProducts(
                    Object.keys(data.products)
                        .filter((slug) => slug !== "store") // Filter out "store"
                        .map((slug) => {
                            const product = storeProducts?.find((p) => p.slug === slug);
                            return {
                                label: product?.name || slug, // Remove store condition since it's filtered out
                                value: slug,
                            };
                        })
                );
                setFormatedData((prev) => formatData(data.products, activeProduct));
            })
            .catch((e) => {
                console.log("network error");
                console.error(e);
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const formatData = (data: FormatedProductProps, product: string) => {
        let result = {
            delivered: 0,
            deliveredProfit: 0,
            deliveredValue: 0,
            deposit: 0,
            depositProfit: 0,
            depositValue: 0,
            returned: 0,
            returnedCost: 0,
            total: 0,
            totalProfit: 0,
            totalValue: 0,
            transit: 0,
            transitProfit: 0,
            transitValue: 0,
            unverified: 0,
        };

        const dataArray = Object.entries(data[product]);

        const formated = dataArray?.map(([date, entryData], index) => {
            result.delivered += entryData.delivered;
            result.deliveredProfit += entryData.deliveredProfit;
            result.deliveredValue += entryData.deliveredValue;
            result.deposit += entryData.deposit;
            result.depositProfit += entryData.depositProfit;
            result.depositValue += entryData.depositValue;
            result.returned += entryData.returned;
            result.returnedCost += entryData.returnedCost;
            result.total += entryData?.total;
            result.totalProfit += entryData?.totalProfit;
            result.totalValue += entryData?.totalValue;
            result.transit += entryData.transit;
            result.transitProfit += entryData.transitProfit;
            result.transitValue += entryData.transitValue;
            result.unverified += entryData.unverified;

            return { date, ...entryData };
        });

        return {
            cards: result,
            charts: formated,
        };
    };

    const onRefresh = () => {
        fetchData();
    };
    const { getProducts, products: storeProducts } = useOrderStore();
    useLayoutEffect(() => {
        getProducts();
    }, []);

    useLayoutEffect(() => {
        fetchData();
    }, [activeFilter, activeProduct]);

    return (
        <GestureHandlerRootView>
            <NativeScrollView
                contentContainerStyle={[
                    { paddingHorizontal: 5, paddingTop: 10, paddingBottom: 120, gap: 10 },
                    loading && { height: "100%" },
                ]}
                refreshControl={<RefreshControl refreshing={loading} onRefresh={onRefresh} />}
            >
                <StatsCard style={{ paddingHorizontal: 10, gap: 5 }} key="filters">
                    <Text
                        style={{
                            fontFamily: typography.fontSemibold.fontFamily,
                            fontSize: typography.sm.fontSize,
                            color: colors.gray[900],
                        }}
                    >
                        Filters
                    </Text>
                    <Text
                        style={{
                            fontFamily: typography.fontSemibold.fontFamily,
                            fontSize: typography.xs.fontSize,
                        }}
                    >
                        Select Period
                    </Text>
                    <ScrollView horizontal contentContainerStyle={{ gap: 5, padding: 2 }}>
                        {filters?.map((filter, key) => {
                            const active = filter?.label === activeFilter?.label;
                            return (
                                <FilterChip
                                    disabled={loading}
                                    key={key}
                                    active={active}
                                    title={filter?.label}
                                    action={() => {
                                        if (!active) setActiveFilter(filter);
                                    }}
                                />
                            );
                        })}
                    </ScrollView>
                    {/* <Text
                        style={{
                            fontFamily: typography.fontSemibold.fontFamily,
                            fontSize: typography.xs.fontSize,
                        }}
                    >
                        Select Product
                    </Text> */}
                    <StatsPicker
                        label="Select Product"
                        items={products}
                        value={activeProduct}
                        onValueChange={(value) => setActiveProduct(value)}
                    />
                </StatsCard>
                {!loading ? (
                    <>
                        <StatsCard key="revenue-profit-chart">
                            <HomeChartV2
                                axis="date"
                                dataSet={["deliveredValue", "deliveredProfit"]}
                                data={formatedData.charts ? formatedData.charts : []}
                                height={300}
                                nYticks={8}
                                columnWidth={40}
                                label={`Revenue vs Profit`}
                                colorsSet={["primary", "blue"]}
                            />
                        </StatsCard>
                        <StatsCard key="orders-status-chart">
                            <HomeChartV2
                                axis="date"
                                dataSet={["deposit", "inTransit", "delivered", "returned"]}
                                data={formatedData.charts ? formatedData.charts : []}
                                height={300}
                                nYticks={8}
                                columnWidth={40}
                                label={`Orders units per Status`}
                                colorsSet={["secondary", "green", "primary", "blue"]}
                            />
                        </StatsCard>
                    </>
                ) : (
                    <View style={{ height: "100%", alignItems: "center", paddingTop: "30%" }}>
                        <ActivityIndicator />
                    </View>
                )}
            </NativeScrollView>
            {!loading && (
                <BottomSheet
                    style={[
                        {
                            borderColor: colors.gray[100],
                            borderRadius: 15,
                            backgroundColor: "white",
                            borderWidth: 1,
                            elevation: 1,
                        },
                    ]}
                    containerStyle={{ paddingTop: 20, paddingBottom: 10 }}
                    snapPoints={[100, 185]}
                    ref={bottomSheetRef}
                >
                    <BottomSheetView>
                        {/* created a padding, simple padding didnt work */}
                        <Text style={{ height: 10 }}></Text>
                    </BottomSheetView>
                    <ScrollView
                        horizontal
                        contentContainerStyle={{
                            flexDirection: "row",
                            alignItems: "flex-start",
                            gap: 10,
                            paddingHorizontal: 10,
                        }}
                    >
                        <DeliveryCard
                            mainValueLabel="Total"
                            mainValue={formatedData.cards?.total}
                            mainValueUnit="order"
                            MainIcon={TotalIcon}
                            secondaryValueLabel="~Profit"
                            SecondaryIcon={ProfitGainIcon}
                            secondaryValue={formatedData.cards?.totalProfit}
                            tertiaryValueLabel="Value"
                            tertiaryValue={formatedData.cards?.totalValue}
                            TertiaryIcon={MoneyIcon}
                            variant="gray"
                            color={handleStatus("deposit").color}
                            secondaryValueUnit={store.currency.code}
                            tertiaryValueUnit={store.currency.code}
                        />
                        <DeliveryCard
                            TertiaryIcon={MoneyIcon}
                            MainIcon={DepositIcon}
                            mainValueUnit="order"
                            mainValue={formatedData.cards?.deposit}
                            mainValueLabel="In Deposit"
                            SecondaryIcon={ProfitGainIcon}
                            secondaryValue={formatedData.cards?.depositProfit}
                            secondaryValueLabel="~Profit"
                            tertiaryValue={formatedData.cards?.depositValue}
                            tertiaryValueLabel="Value"
                            variant="orange"
                            color={handleStatus("deposit").color}
                            secondaryValueUnit={store.currency.code}
                            tertiaryValueUnit={store.currency.code}
                        />
                        <DeliveryCard
                            mainValueUnit="order"
                            TertiaryIcon={MoneyIcon}
                            MainIcon={TransitIcon}
                            mainValue={formatedData.cards?.transit}
                            mainValueLabel="In Transit"
                            SecondaryIcon={ProfitGainIcon}
                            secondaryValue={formatedData.cards?.transitProfit}
                            secondaryValueLabel="~Profit"
                            tertiaryValue={formatedData.cards?.transitValue}
                            tertiaryValueLabel="Value"
                            variant="teal"
                            color={handleStatus("in transit").color}
                            secondaryValueUnit={store.currency.code}
                            tertiaryValueUnit={store.currency.code}
                        />

                        <DeliveryCard
                            TertiaryIcon={MoneyIcon}
                            MainIcon={DeliveredIcon}
                            mainValue={formatedData.cards?.delivered}
                            mainValueLabel="Delivered"
                            SecondaryIcon={ProfitGainIcon}
                            secondaryValue={formatedData.cards?.deliveredProfit}
                            secondaryValueLabel="+Profit"
                            tertiaryValue={formatedData.cards?.deliveredValue}
                            mainValueUnit="order"
                            tertiaryValueLabel="Value"
                            variant="green"
                            color={handleStatus("delivered").color}
                            secondaryValueUnit={store.currency.code}
                            tertiaryValueUnit={store.currency.code}
                        />
                        <DeliveryCard
                            simple
                            TertiaryIcon={MoneyIcon}
                            MainIcon={ReturendIcon}
                            SecondaryIcon={ProfitLossIcon}
                            mainValue={formatedData.cards?.returned}
                            mainValueLabel="Returned"
                            variant="red"
                            secondaryValue={20}
                            secondaryValueLabel="+Profit"
                            tertiaryValue={5}
                            tertiaryValueLabel="Value"
                            mainValueUnit="order"
                            color={handleStatus("returned").color}
                        />
                    </ScrollView>
                </BottomSheet>
            )}
        </GestureHandlerRootView>
    );
};

export default products;
