import { ActivityIndicator, RefreshControl, Text, View, ScrollView as NativeScrollView } from "react-native";
import React, { useLayoutEffect, useRef, useState } from "react";

import StatsCard from "@components/stats/StatsCard";
import Duration<PERSON>hart from "@components/stats/DurationChart";
import { typography } from "@styles/typography";
import colors from "@styles/colors";
import HorizontalPercentageCharte from "@components/stats/VerticalPercentageCharte";
import HomeChartV2 from "@components/charts/HomeChart/HomeChartV2";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import DeliveryCard from "@components/stats/DeliveryCard";
import StatsPicker from "@components/inputs/select/StatsPicker";
import {
    DeliveredIcon,
    DepositIcon,
    MoneyIcon,
    ProfitGainIcon,
    ProfitLossIcon,
    ReturendIcon,
    TransitIcon,
} from "@components/icons/StatsIcons";
import { handleStatus } from "@components/inputs/chips/Tag/Tag";
import { Path, Svg } from "react-native-svg";
import { FormatedDeliveryProps } from "../../store/statsStoreProps";
import {
    fetchStats,
    getDaysSinceNovember13,
    getDaysSinceStartOfMonth,
    getDaysSinceStartOfWeek,
    getPeriod,
} from "../../utils/statsUtils";
import FilterChip from "@components/stats/FilterChip";
import { GestureHandlerRootView, ScrollView } from "react-native-gesture-handler";
import Divider from "@components/dividers/Divider";
import { useStoreStore } from "../../store/storeStore";

const delivery = () => {
    const { store } = useStoreStore();
    const bottomSheetRef = useRef<BottomSheet>(null);

    const initialFilters = [
        { label: "this week", value: getDaysSinceStartOfWeek() },
        { label: "last 7 days", value: 7 },
        { label: "this month", value: getDaysSinceStartOfMonth() },
        { label: "last 30 days", value: 30 },
        { label: "last three months", value: getDaysSinceStartOfMonth() + 61 },
        { label: "all time", value: getDaysSinceNovember13() },
    ];

    const initialActiveFilter = { label: "this week", value: getDaysSinceStartOfWeek() };

    const [loading, setLoading] = useState<boolean>(false);
    const [filters, setFilters] = useState<{ label: string; value: number }[]>(initialFilters);
    const [activeFilter, setActiveFilter] = useState(initialActiveFilter);

    const [formatedData, setFormatedData] = useState<Partial<ReturnType<typeof formatData>>>({});
    const [deliveryCompanies, setDeliveryCompanies] = useState<{ label: string; value: string }[]>([]);
    const [activeDeliveryCompany, setActiveDeliveryCompany] = useState<string>("store");

    const fetchData = async () => {
        setLoading(true);
        fetchStats(getPeriod(activeFilter.value))
            .then((data) => {
                // console.log("data stat =>",data.delivery.store.avgDeliveryTime);
                setDeliveryCompanies(
                    Object.keys(data.delivery)
                        .filter((key) => key !== "store")
                        .map((key) => ({
                            label: key,
                            value: key,
                        }))
                );
                setFormatedData((prev) => formatData(data.delivery, activeDeliveryCompany));
                // console.log("deliveryCompanies.avgDeliveryTime =>",JSON.stringify(deliveryCompanies));
            })
            .catch((e) => {
                console.error(e);
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const formatData = (data: FormatedDeliveryProps, deliveryCompany: string) => {
        let result = {
            avgDeliveryTime: 0,
            deliveredOrders: 0,
            deliveredOrdersPayment: 0,
            deliveredOrdersProfit: 0,
            deliveredOrdersValue: 0,
            depositOrders: 0,
            depositOrdersPayment: 0,
            depositOrdersProfit: 0,
            depositOrdersValue: 0,
            inTransitOrders: 0,
            inTransitOrdersPayment: 0,
            inTransitOrdersProfit: 0,
            inTransitOrdersValue: 0,
            returnedOrders: 0,
            returnedOrdersCost: 0,
            unverifiedOrders: 0,
        };

        if (deliveryCompany === "store") {
            let totalDeliveryTime = 0;

            // Get all delivery companies except "store"
            const companies = Object.keys(data).filter((key) => key !== "store");

            // Create a map to aggregate data by date across all companies
            const dateMap = new Map();

            companies.forEach((company) => {
                Object.entries(data[company]).forEach(([date, stats]) => {
                    if (!dateMap.has(date)) {
                        dateMap.set(date, {
                            date,
                            avgDeliveryTime: 0,
                            deliveredOrders: 0,
                            deliveredOrdersPayment: 0,
                            deliveredOrdersProfit: 0,
                            deliveredOrdersValue: 0,
                            depositOrders: 0,
                            depositOrdersPayment: 0,
                            depositOrdersProfit: 0,
                            depositOrdersValue: 0,
                            inTransitOrders: 0,
                            inTransitOrdersPayment: 0,
                            inTransitOrdersProfit: 0,
                            inTransitOrdersValue: 0,
                            returnedOrders: 0,
                            returnedOrdersCost: 0,
                            unverifiedOrders: 0,
                        });
                    }

                    const dateStats = dateMap.get(date);
                    totalDeliveryTime += stats.avgDeliveryTime * stats.deliveredOrders;

                    dateStats.deliveredOrders += stats.deliveredOrders;
                    dateStats.deliveredOrdersValue += stats.deliveredOrdersValue;
                    dateStats.deliveredOrdersPayment += stats.deliveredOrdersPayment;
                    dateStats.deliveredOrdersProfit += stats.deliveredOrdersProfit;
                    dateStats.returnedOrders += stats.returnedOrders;
                    dateStats.returnedOrdersCost += stats.returnedOrdersCost;
                    dateStats.depositOrders += stats.depositOrders;
                    dateStats.depositOrdersValue += stats.depositOrdersValue;
                    dateStats.depositOrdersPayment += stats.depositOrdersPayment;
                    dateStats.depositOrdersProfit += stats.depositOrdersProfit;
                    dateStats.inTransitOrders += stats.inTransitOrders;
                    dateStats.inTransitOrdersValue += stats.inTransitOrdersValue;
                    dateStats.inTransitOrdersPayment += stats.inTransitOrdersPayment;
                    dateStats.inTransitOrdersProfit += stats.inTransitOrdersProfit;
                    dateStats.unverifiedOrders += stats.unverifiedOrders;

                    // Update running totals for the result object
                    result.deliveredOrders += stats.deliveredOrders;
                    result.deliveredOrdersValue += stats.deliveredOrdersValue;
                    result.deliveredOrdersPayment += stats.deliveredOrdersPayment;
                    result.deliveredOrdersProfit += stats.deliveredOrdersProfit;
                    result.returnedOrders += stats.returnedOrders;
                    result.returnedOrdersCost += stats.returnedOrdersCost;
                    result.depositOrders += stats.depositOrders;
                    result.depositOrdersValue += stats.depositOrdersValue;
                    result.depositOrdersPayment += stats.depositOrdersPayment;
                    result.depositOrdersProfit += stats.depositOrdersProfit;
                    result.inTransitOrders += stats.inTransitOrders;
                    result.inTransitOrdersValue += stats.inTransitOrdersValue;
                    result.inTransitOrdersPayment += stats.inTransitOrdersPayment;
                    result.inTransitOrdersProfit += stats.inTransitOrdersProfit;
                    result.unverifiedOrders += stats.unverifiedOrders;
                });
            });

            // Convert the map to an array and calculate avgDeliveryTime for each date
            const formated = Array.from(dateMap.values()).map((dateStats) => {
                dateStats.avgDeliveryTime =
                    dateStats.deliveredOrders > 0 ? totalDeliveryTime / dateStats.deliveredOrders : 0;
                return dateStats;
            });

            // Calculate overall average delivery time
            result.avgDeliveryTime = result.deliveredOrders > 0 ? totalDeliveryTime / result.deliveredOrders : 0;

            return { cards: result, charts: formated };
        }

        // Original logic for specific delivery company
        const dataArray = Object.entries(data[deliveryCompany]);
        let totalDeliveryTime = 0;

        const formated = dataArray?.map(([date, entryData]) => {
            totalDeliveryTime += entryData.avgDeliveryTime * entryData.deliveredOrders;
            result.deliveredOrders += entryData.deliveredOrders;
            result.deliveredOrdersValue += entryData.deliveredOrdersValue;
            result.deliveredOrdersProfit += entryData.deliveredOrdersProfit;
            result.depositOrders += entryData.depositOrders;
            result.depositOrdersValue += entryData.depositOrdersValue;
            result.depositOrdersProfit += entryData.depositOrdersProfit;
            result.inTransitOrders += entryData.inTransitOrders;
            result.inTransitOrdersValue += entryData.inTransitOrdersValue;
            result.inTransitOrdersProfit += entryData.inTransitOrdersProfit;
            result.returnedOrders += entryData.returnedOrders;
            result.returnedOrdersCost += entryData.returnedOrdersCost;
            return { date, ...entryData };
        });

        // Calculate weighted average delivery time for specific company
        result.avgDeliveryTime = result.deliveredOrders > 0 ? totalDeliveryTime / result.deliveredOrders : 0;

        return {
            cards: result,
            charts: formated,
        };
    };

    const onRefresh = () => {
        fetchData();
    };

    useLayoutEffect(() => {
        fetchData();
    }, [activeFilter, activeDeliveryCompany]);

    return (
        <GestureHandlerRootView>
            <NativeScrollView
                contentContainerStyle={[
                    { paddingHorizontal: 5, paddingTop: 10, paddingBottom: 120, gap: 10 },
                    loading && { height: "100%" },
                ]}
                refreshControl={<RefreshControl refreshing={loading} onRefresh={onRefresh} />}
            >
                <StatsCard style={{ paddingHorizontal: 10, gap: 5 }}>
                    <Text
                        style={{
                            fontFamily: typography.fontSemibold.fontFamily,
                            fontSize: typography.sm.fontSize,
                            color: colors.gray[900],
                        }}
                    >
                        Filters
                    </Text>
                    <Text
                        style={{
                            fontFamily: typography.fontSemibold.fontFamily,
                            fontSize: typography.xs.fontSize,
                        }}
                    >
                        Select Period
                    </Text>
                    <ScrollView horizontal contentContainerStyle={{ gap: 5, padding: 2 }}>
                        {filters?.map((filter, key) => {
                            const active = filter?.label === activeFilter?.label;
                            return (
                                <FilterChip
                                    disabled={loading}
                                    key={key}
                                    active={active}
                                    title={filter?.label}
                                    action={() => {
                                        if (!active) setActiveFilter(filter);
                                    }}
                                />
                            );
                        })}
                    </ScrollView>
                    {/* <Text
                        style={{
                            fontFamily: typography.fontSemibold.fontFamily,
                            fontSize: typography.xs.fontSize,
                        }}
                    >
                        Select Delivery Company
                    </Text> */}
                    <StatsPicker
                        label="Select Delivery Company"
                        items={deliveryCompanies}
                        value={activeDeliveryCompany}
                        onValueChange={(value) => setActiveDeliveryCompany(value)}
                    />
                </StatsCard>
                {!loading ? (
                    <>
                        <StatsCard key="avg-delivery-time">
                            <DurationChart
                                title="Average Delivery Time"
                                value={formatedData.cards?.avgDeliveryTime}
                                description="The Average time for orders to be delivered"
                            />
                        </StatsCard>
                        <StatsCard key="delivery-ratio" style={{ gap: 10 }}>
                            <Text
                                style={{
                                    fontFamily: typography.fontBold.fontFamily,
                                    fontSize: typography.md.fontSize,
                                    color: colors.gray[800],
                                    textAlign: "center",
                                    paddingBottom: 10,
                                }}
                            >
                                Delivered/Returned Ratio
                            </Text>
                            <HorizontalPercentageCharte
                                firstColor="green"
                                secondColor="red"
                                firstValue={formatedData.cards?.deliveredOrders}
                                secondValue={formatedData.cards?.returnedOrders}
                            />
                            <View
                                style={{
                                    flexDirection: "column",
                                    width: "100%",
                                    padding: 10,
                                    alignItems: "flex-start",
                                    gap: 8,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: colors.gray[200],
                                }}
                            >
                                <View
                                    style={{
                                        flexDirection: "row",
                                        justifyContent: "space-between",
                                        width: "100%",
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: "center", gap: 5 }}>
                                        <View
                                            style={{
                                                width: 20,
                                                height: 20,
                                                backgroundColor: colors.green[500],
                                                borderRadius: 100,
                                            }}
                                        />
                                        <Text
                                            style={{
                                                fontFamily: typography.fontMedium.fontFamily,
                                                color: colors.gray[900],
                                            }}
                                        >
                                            {"Delivered Orders"}
                                        </Text>
                                    </View>
                                    <Text
                                        style={{
                                            fontFamily: typography.fontMedium.fontFamily,
                                            color: colors.gray[900],
                                        }}
                                    >
                                        {formatedData.cards?.deliveredOrders}
                                    </Text>
                                </View>
                                <Divider color={colors.gray[200]} />

                                <View
                                    style={{
                                        flexDirection: "row",
                                        justifyContent: "space-between",
                                        width: "100%",
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: "center", gap: 5 }}>
                                        <View
                                            style={{
                                                width: 20,
                                                height: 20,
                                                backgroundColor: colors.red[500],
                                                borderRadius: 100,
                                            }}
                                        />
                                        <Text
                                            style={{
                                                fontFamily: typography.fontMedium.fontFamily,
                                                color: colors.gray[900],
                                            }}
                                        >
                                            {"Returned Orders"}
                                        </Text>
                                    </View>
                                    <Text
                                        style={{
                                            fontFamily: typography.fontMedium.fontFamily,
                                            color: colors.gray[900],
                                        }}
                                    >
                                        {formatedData.cards?.returnedOrders}
                                    </Text>
                                </View>
                            </View>
                        </StatsCard>
                        <StatsCard key="orders-status-chart" style={{ overflow: "hidden" }}>
                            <HomeChartV2
                                axis="date"
                                dataSet={["depositOrders", "inTransitOrders", "deliveredOrders", "returnedOrders"]}
                                data={formatedData.charts ? formatedData.charts : []}
                                height={300}
                                nYticks={8}
                                columnWidth={45}
                                label={`Orders units per Status`}
                                colorsSet={["primary", "teal", "blue", "red"]}
                            />
                        </StatsCard>
                    </>
                ) : (
                    <View style={{ height: "100%", alignItems: "center", paddingTop: "30%" }}>
                        <ActivityIndicator />
                    </View>
                )}
            </NativeScrollView>

            {!loading && (
                <BottomSheet
                    style={[
                        {
                            borderColor: colors.gray[100],
                            borderRadius: 15,
                            backgroundColor: "white",
                            borderWidth: 1,
                            elevation: 1,
                        },
                    ]}
                    containerStyle={{ paddingTop: 20 }}
                    snapPoints={[100, 185]}
                    ref={bottomSheetRef}
                >
                    <BottomSheetView>
                        {/* created a padding, simple padding didnt work */}
                        <Text style={{ height: 10 }}></Text>
                    </BottomSheetView>
                    <ScrollView
                        horizontal
                        contentContainerStyle={{
                            flexDirection: "row",
                            alignItems: "flex-start",
                            gap: 10,
                            paddingHorizontal: 10,
                        }}
                    >
                        <DeliveryCard
                            TertiaryIcon={MoneyIcon}
                            MainIcon={DepositIcon}
                            mainValueUnit="order"
                            mainValue={formatedData.cards?.depositOrders}
                            mainValueLabel="In Deposit"
                            SecondaryIcon={ProfitGainIcon}
                            secondaryValue={formatedData.cards?.depositOrdersProfit}
                            secondaryValueLabel="~Profit"
                            tertiaryValue={formatedData.cards?.depositOrdersValue}
                            tertiaryValueLabel="Value"
                            secondaryValueUnit={store.currency.code}
                            tertiaryValueUnit={store.currency.code}
                            variant="orange"
                            color={handleStatus("deposit").color}
                        />
                        <DeliveryCard
                            mainValueUnit="order"
                            TertiaryIcon={MoneyIcon}
                            MainIcon={TransitIcon}
                            mainValue={formatedData.cards?.inTransitOrders}
                            mainValueLabel="In Transit"
                            SecondaryIcon={ProfitGainIcon}
                            secondaryValue={formatedData.cards?.inTransitOrdersProfit}
                            secondaryValueLabel="~Profit"
                            tertiaryValue={formatedData.cards?.inTransitOrdersValue}
                            tertiaryValueLabel="Value"
                            variant="teal"
                            color={handleStatus("in transit").color}
                            secondaryValueUnit={store.currency.code}
                            tertiaryValueUnit={store.currency.code}
                        />

                        <DeliveryCard
                            TertiaryIcon={MoneyIcon}
                            MainIcon={DeliveredIcon}
                            mainValue={formatedData.cards?.deliveredOrders}
                            mainValueLabel="Delivered"
                            SecondaryIcon={ProfitGainIcon}
                            secondaryValue={formatedData.cards?.deliveredOrdersProfit}
                            secondaryValueLabel="+Profit"
                            tertiaryValue={formatedData.cards?.deliveredOrdersValue}
                            mainValueUnit="order"
                            tertiaryValueLabel="Value"
                            variant="green"
                            color={handleStatus("delivered").color}
                            secondaryValueUnit={store.currency.code}
                            tertiaryValueUnit={store.currency.code}
                        />
                        <DeliveryCard
                            simple
                            TertiaryIcon={MoneyIcon}
                            MainIcon={ReturendIcon}
                            SecondaryIcon={ProfitLossIcon}
                            mainValue={formatedData.cards?.returnedOrders}
                            mainValueLabel="Returned"
                            variant="red"
                            secondaryValue={formatedData.cards?.depositOrdersProfit}
                            secondaryValueLabel="+Profit"
                            tertiaryValue={formatedData.cards?.depositOrdersValue}
                            tertiaryValueLabel="Value"
                            mainValueUnit="order"
                            color={handleStatus("returned").color}
                        />
                    </ScrollView>
                </BottomSheet>
            )}
        </GestureHandlerRootView>
    );
};

export default delivery;
