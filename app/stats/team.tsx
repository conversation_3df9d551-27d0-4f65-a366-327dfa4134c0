import { ActivityIndicator, RefreshControl, Text, View, ScrollView as NativeScrollView } from "react-native";
import * as React from "react";
import { useLayoutEffect, useRef, useState } from "react";
import {
    fetchStats,
    getDaysSinceNovember13,
    getDaysSinceStartOfMonth,
    getDaysSinceStartOfWeek,
    getPeriod,
} from "../../utils/statsUtils";
import { FormatedAdminsProps, FormatedProductProps, TeamStatsCard } from "../../store/statsStoreProps";
import HomeChartV2 from "@components/charts/HomeChart/HomeChartV2";
import {
    ConfirmedIcon,
    ProfitGainIcon,
    MoneyIcon,
    RejectedIcon,
    ProfitLossIcon,
    CreatedIcon,
    AttemptIcon,
} from "@components/icons/StatsIcons";
import { handleStatus } from "@components/inputs/chips/Tag/Tag";
import DeliveryCard from "@components/stats/DeliveryCard";
import DurationChart from "@components/stats/DurationChart";
import FilterChip from "@components/stats/FilterChip";
import StatsCard from "@components/stats/StatsCard";
import HorizontalPercentageCharte from "@components/stats/VerticalPercentageCharte";
import BottomSheet, { BottomSheetScrollView } from "@gorhom/bottom-sheet";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { GestureHandlerRootView, ScrollView } from "react-native-gesture-handler";
import { PieChart } from "react-native-gifted-charts";
import Svg, { Path } from "react-native-svg";

import StatsPicker from "@components/inputs/select/StatsPicker";
import Divider from "@components/dividers/Divider";

const team = () => {
    const bottomSheetRef = useRef<BottomSheet>(null);

    const initialFilters = [
        { label: "this week", value: getDaysSinceStartOfWeek() },
        { label: "last 7 days", value: 7 },
        { label: "this month", value: getDaysSinceStartOfMonth() },
        { label: "last 30 days", value: 30 },
        { label: "last three months", value: getDaysSinceStartOfMonth() + 61 },
        { label: "all time", value: getDaysSinceNovember13() },
    ];

    const initialActiveFilter = { label: "this week", value: getDaysSinceStartOfWeek() };

    const [loading, setLoading] = useState<boolean>(false);
    const [filters, setFilters] = useState<{ label: string; value: number }[]>(initialFilters);
    const [activeFilter, setActiveFilter] = useState(initialActiveFilter);

    const [formatedData, setFormatedData] = useState<Partial<ReturnType<typeof formatData>>>({});
    const [teamMembers, setTeamMembers] = useState<{ label: string; value: string }[]>([]);
    const [activeTeamMember, setActiveTeamMember] = useState<string>("store");

    const fetchData = async () => {
        setLoading(true);
        fetchStats(getPeriod(activeFilter.value))
            .then((data) => {
                // Filter out "store" since it will be added by StatsPicker
                setTeamMembers(
                    Object.keys(data?.admins)
                        .filter((key) => key !== "store")
                        .map((key) => ({
                            label: key,
                            value: key,
                        }))
                );
                setFormatedData((prev) => formatData(data.admins, activeTeamMember));
                console.log("formated data =>", formatedData);
            })
            .catch((e) => {
                console.log("network error");
                console.error(e);
            })
            .finally(() => {
                setLoading((prev) => {
                    return false;
                });
            });
    };

    const formatData = (data: FormatedAdminsProps, activeTeamMember: string) => {
        console.log("Starting formatData with activeTeamMember:", activeTeamMember);

        let result: TeamStatsCard = {
            confirmedOrders: 0,
            rejectedOrders: 0,
            createdOrders: 0,
            deletedOrders: 0,
            receivedOrders: 0,
            packedOrders: 0,
            attempts: 0,
            firstAttempts: 0,
            avgConfirmationTime: 0,
            avgTimeToFirstAttempt: 0,
            rejectionReasons: {},
        };

        if (activeTeamMember === "store") {
            console.log("Processing store data (aggregating all admins except 'store')");
            let totalConfirmationTime = 0;
            let totalFirstAttemptsTime = 0;
            let totalConfirmedOrders = 0;
            let totalFirstAttempts = 0;

            // Get all dates from all admins (excluding store)
            const dates = new Set<string>();
            Object.entries(data).forEach(([admin, adminData]) => {
                if (admin !== "store") {
                    Object.keys(adminData).forEach((date) => dates.add(date));
                }
            });

            console.log("All unique dates:", Array.from(dates));

            // Process each admin's data (excluding store)
            Object.entries(data).forEach(([admin, adminData]) => {
                if (admin === "store") {
                    console.log("Skipping 'store' admin data");
                    return; // Skip store data
                }

                console.log(`Processing admin: ${admin}`);

                Object.entries(adminData).forEach(([date, dayStats]) => {
                    console.log(`  Date: ${date}`);
                    console.log(`    confirmedOrders: ${dayStats.confirmedOrders}`);
                    console.log(`    rejectedOrders: ${dayStats.rejectedOrders}`);

                    // Accumulate totals for final result
                    result.confirmedOrders += dayStats.confirmedOrders;
                    result.rejectedOrders += dayStats.rejectedOrders;
                    result.createdOrders += dayStats.createdOrders;
                    result.deletedOrders += dayStats.deletedOrders;
                    result.receivedOrders += dayStats.receivedOrders;
                    result.packedOrders += dayStats.packedOrders;
                    result.attempts += dayStats.attempts;
                    result.firstAttempts += dayStats.firstAttempts;

                    console.log(
                        `    Running totals - confirmed: ${result.confirmedOrders}, rejected: ${result.rejectedOrders}`
                    );

                    // Accumulate weighted times and counts for averages
                    if (dayStats.confirmedOrders > 0) {
                        totalConfirmationTime += dayStats.avgConfirmationTime * dayStats.confirmedOrders;
                        totalConfirmedOrders += dayStats.confirmedOrders;
                    }
                    if (dayStats.firstAttempts > 0) {
                        totalFirstAttemptsTime += dayStats.avgTimeToFirstAttempt * dayStats.firstAttempts;
                        totalFirstAttempts += dayStats.firstAttempts;
                    }

                    // Handle rejection reasons
                    if (
                        dayStats.rejectionReasons &&
                        typeof dayStats.rejectionReasons === "object" &&
                        dayStats.rejectionReasons !== null
                    ) {
                        Object.entries(dayStats.rejectionReasons).forEach(([reason, count]) => {
                            result.rejectionReasons[reason] = (result.rejectionReasons[reason] || 0) + count;
                        });
                    }
                });
            });

            // Calculate final averages for result
            result.avgConfirmationTime = totalConfirmedOrders > 0 ? totalConfirmationTime / totalConfirmedOrders : 0;
            result.avgTimeToFirstAttempt = totalFirstAttempts > 0 ? totalFirstAttemptsTime / totalFirstAttempts : 0;

            console.log("Final store aggregated totals:", {
                confirmedOrders: result.confirmedOrders,
                rejectedOrders: result.rejectedOrders,
                createdOrders: result.createdOrders,
                attempts: result.attempts,
            });

            // Create charts data
            const formated = [];
            dates.forEach((date) => {
                const dateEntry = {
                    date,
                    confirmedOrders: 0,
                    rejectedOrders: 0,
                    createdOrders: 0,
                    deletedOrders: 0,
                    receivedOrders: 0,
                    packedOrders: 0,
                    attempts: 0,
                    firstAttempts: 0,
                    avgConfirmationTime: 0,
                    avgTimeToFirstAttempt: 0,
                    rejectionReasons: {},
                };

                // Aggregate data for this date across all admins (except store)
                Object.entries(data).forEach(([admin, adminData]) => {
                    if (admin !== "store" && adminData[date]) {
                        const dayStats = adminData[date];
                        dateEntry.confirmedOrders += dayStats.confirmedOrders;
                        dateEntry.rejectedOrders += dayStats.rejectedOrders;
                        dateEntry.createdOrders += dayStats.createdOrders;
                        dateEntry.deletedOrders += dayStats.deletedOrders;
                        dateEntry.receivedOrders += dayStats.receivedOrders;
                        dateEntry.packedOrders += dayStats.packedOrders;
                        dateEntry.attempts += dayStats.attempts;
                        dateEntry.firstAttempts += dayStats.firstAttempts;
                    }
                });

                formated.push(dateEntry);
            });

            return { cards: result, charts: formated };
        } else if (activeTeamMember && data[activeTeamMember]) {
            console.log(`Processing individual team member: ${activeTeamMember}`);
            // For individual team member stats
            const dataArray = Object.entries(data[activeTeamMember]);
            let totalConfirmationTime = 0;
            let totalFirstAttemptsTime = 0;
            let totalConfirmedOrders = 0;
            let totalFirstAttempts = 0;

            const formated = dataArray.map(([date, entryData]) => {
                console.log(`  Date: ${date}`);
                console.log(`    confirmedOrders: ${entryData.confirmedOrders}`);
                console.log(`    rejectedOrders: ${entryData.rejectedOrders}`);

                // Accumulate stats
                result.confirmedOrders += entryData.confirmedOrders;
                result.rejectedOrders += entryData.rejectedOrders;
                result.createdOrders += entryData.createdOrders;
                result.deletedOrders += entryData.deletedOrders;
                result.receivedOrders += entryData.receivedOrders;
                result.packedOrders += entryData.packedOrders;
                result.attempts += entryData.attempts;
                result.firstAttempts += entryData.firstAttempts;

                console.log(
                    `    Running totals - confirmed: ${result.confirmedOrders}, rejected: ${result.rejectedOrders}`
                );

                // Accumulate weighted times and counts for averages
                if (entryData.confirmedOrders > 0) {
                    totalConfirmationTime += entryData.avgConfirmationTime * entryData.confirmedOrders;
                    totalConfirmedOrders += entryData.confirmedOrders;
                }
                if (entryData.firstAttempts > 0) {
                    totalFirstAttemptsTime += entryData.avgTimeToFirstAttempt * entryData.firstAttempts;
                    totalFirstAttempts += entryData.firstAttempts;
                }

                // Handle rejection reasons
                if (
                    entryData.rejectionReasons &&
                    typeof entryData.rejectionReasons === "object" &&
                    entryData.rejectionReasons !== null
                ) {
                    Object.entries(entryData.rejectionReasons).forEach(([reason, count]) => {
                        result.rejectionReasons[reason] = (result.rejectionReasons[reason] || 0) + count;
                    });
                }

                return { date, ...entryData };
            });

            // Calculate final averages
            result.avgConfirmationTime = totalConfirmedOrders > 0 ? totalConfirmationTime / totalConfirmedOrders : 0;
            result.avgTimeToFirstAttempt = totalFirstAttempts > 0 ? totalFirstAttemptsTime / totalFirstAttempts : 0;

            console.log(`Final ${activeTeamMember} totals:`, {
                confirmedOrders: result.confirmedOrders,
                rejectedOrders: result.rejectedOrders,
                createdOrders: result.createdOrders,
                attempts: result.attempts,
            });

            return {
                cards: result,
                charts: formated,
            };
        } else {
            console.log(`Team member '${activeTeamMember}' not found, returning empty data`);
            // Return empty data if team member not found
            return {
                cards: result,
                charts: [],
            };
        }
    };

    const getPieChartData = (): { value: number; text: string; color: string }[] => {
        const shade = 500;
        const pieColors = [
            colors.primary[shade],
            colors.secondary[shade],
            colors.teal[shade],
            colors.blue[shade],
            colors.orange[shade],
        ];

        let totalReasons = 0;
        for (const key in formatedData.cards?.rejectionReasons) {
            totalReasons += formatedData.cards?.rejectionReasons[key] || 0;
        }

        const pieData = formatedData.cards
            ? Object.entries(formatedData.cards?.rejectionReasons).map(([key, value], index) => {
                  const realValue = Number(value) || 0;
                  return {
                      key: `seg-${key}-${index}`,
                      value: realValue,
                      text: `${key}:${((realValue * 100) / totalReasons).toFixed(1)}%`,
                      color: pieColors[index],
                  };
              })
            : [];

        return pieData;
    };

    const onRefresh = () => {
        fetchData();
    };

    useLayoutEffect(() => {
        fetchData();
    }, [activeFilter, activeTeamMember]);

    return (
        <GestureHandlerRootView>
            <NativeScrollView
                contentContainerStyle={[
                    { paddingHorizontal: 5, paddingTop: 10, paddingBottom: 120, gap: 10 },
                    loading && { height: "100%" },
                ]}
                refreshControl={<RefreshControl refreshing={loading} onRefresh={onRefresh} />}
            >
                <StatsCard key="filters-card" style={{ paddingHorizontal: 10, gap: 5 }}>
                    <Text
                        style={{
                            fontFamily: typography.fontSemibold.fontFamily,
                            fontSize: typography.sm.fontSize,
                            color: colors.gray[900],
                        }}
                    >
                        Filters
                    </Text>
                    <Text
                        style={{
                            fontFamily: typography.fontSemibold.fontFamily,
                            fontSize: typography.xs.fontSize,
                        }}
                    >
                        Select Period
                    </Text>
                    <ScrollView horizontal contentContainerStyle={{ gap: 5, padding: 2 }}>
                        {filters?.map((filter, index) => {
                            const active = filter?.label === activeFilter?.label;
                            return (
                                <FilterChip
                                    key={`filter-${filter.label}-${index}`}
                                    disabled={loading}
                                    active={active}
                                    title={filter?.label}
                                    action={() => {
                                        if (!active) setActiveFilter(filter);
                                    }}
                                />
                            );
                        })}
                    </ScrollView>
                    <StatsPicker
                        key="team-member-picker"
                        label="Select Team Member"
                        items={teamMembers}
                        value={activeTeamMember}
                        onValueChange={(value) => setActiveTeamMember(value)}
                    />
                </StatsCard>
                {!loading ? (
                    [
                        <StatsCard key="confirmed-rejected-ratio" style={{ gap: 10 }}>
                            <Text
                                style={{
                                    fontFamily: typography.fontBold.fontFamily,
                                    fontSize: typography.md.fontSize,
                                    color: colors.gray[800],
                                    textAlign: "center",
                                    paddingBottom: 10,
                                }}
                            >
                                Confirmed/Rejected Ratio
                            </Text>
                            <HorizontalPercentageCharte
                                firstColor="green"
                                secondColor="red"
                                firstValue={formatedData.cards?.confirmedOrders}
                                secondValue={formatedData.cards?.rejectedOrders}
                            />
                            <View
                                style={{
                                    flexDirection: "column",
                                    width: "100%",
                                    padding: 10,
                                    alignItems: "flex-start",
                                    gap: 8,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: colors.gray[200],
                                }}
                            >
                                <View
                                    style={{
                                        flexDirection: "row",
                                        justifyContent: "space-between",
                                        width: "100%",
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: "center", gap: 5 }}>
                                        <View
                                            style={{
                                                width: 20,
                                                height: 20,
                                                backgroundColor: colors.green[500],
                                                borderRadius: 100,
                                            }}
                                        />
                                        <Text
                                            style={{
                                                fontFamily: typography.fontMedium.fontFamily,
                                                color: colors.gray[900],
                                            }}
                                        >
                                            {"Confirmed Orders"}
                                        </Text>
                                    </View>
                                    <Text
                                        style={{
                                            fontFamily: typography.fontMedium.fontFamily,
                                            color: colors.gray[900],
                                        }}
                                    >
                                        {formatedData.cards?.confirmedOrders}
                                    </Text>
                                </View>
                                <Divider color={colors.gray[200]} />

                                <View
                                    style={{
                                        flexDirection: "row",
                                        justifyContent: "space-between",
                                        width: "100%",
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: "center", gap: 5 }}>
                                        <View
                                            style={{
                                                width: 20,
                                                height: 20,
                                                backgroundColor: colors.red[500],
                                                borderRadius: 100,
                                            }}
                                        />
                                        <Text
                                            style={{
                                                fontFamily: typography.fontMedium.fontFamily,
                                                color: colors.gray[900],
                                            }}
                                        >
                                            {"Rejected Orders"}
                                        </Text>
                                    </View>
                                    <Text
                                        style={{
                                            fontFamily: typography.fontMedium.fontFamily,
                                            color: colors.gray[900],
                                        }}
                                    >
                                        {formatedData.cards?.rejectedOrders}
                                    </Text>
                                </View>
                            </View>
                        </StatsCard>,
                        <StatsCard key="orders-status-card" style={{ overflow: "hidden" }}>
                            <HomeChartV2
                                key={"orders-status-chart"}
                                axis="date"
                                dataSet={["confirmedOrders", "rejectedOrders", "createdOrders", "attempts"]}
                                data={formatedData.charts || []}
                                height={300}
                                nYticks={8}
                                columnWidth={45}
                                label={`Orders units per Status`}
                                colorsSet={["green", "red", "primary", "blue"]}
                            />
                        </StatsCard>,

                        <StatsCard key="avg-confirmation-time">
                            <DurationChart
                                key={"avg-confirmation-time-chart"}
                                title="Average Confirmation Time"
                                value={formatedData.cards?.avgConfirmationTime}
                                description={`The Average time for orders to be delivered by ${
                                    activeTeamMember === "store" ? "all admins" : activeTeamMember
                                }`}
                            />
                        </StatsCard>,
                        <StatsCard key="rejection-reasons">
                            <Text
                                style={{
                                    fontFamily: typography.fontBold.fontFamily,
                                    fontSize: typography.lg.fontSize,
                                    color: colors.primary[500],
                                    textAlign: "center",
                                    paddingBottom: 10,
                                    textTransform: "capitalize",
                                }}
                            >
                                rejection reasons
                            </Text>

                            <View style={{ width: "100%", alignItems: "center", gap: 15 }}>
                                <PieChart
                                    key={"pieChart-rejection-reasons"}
                                    donut
                                    showText
                                    textColor="white"
                                    font={typography.fontNormal.fontFamily}
                                    textSize={15}
                                    radius={110}
                                    data={
                                        getPieChartData().length === 0
                                            ? [
                                                  {
                                                      name: "no-data",
                                                      text: "No Data",
                                                      color: colors.gray[300],
                                                      value: 100,
                                                  },
                                              ]
                                            : getPieChartData().map((item, index) => ({
                                                  ...item,
                                                  name: `segment-${index}`,
                                              }))
                                    }
                                />

                                <View
                                    style={{
                                        flexDirection: "column",
                                        width: "100%",
                                        padding: 10,
                                        alignItems: "flex-start",
                                        gap: 8,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        borderColor: colors.gray[200],
                                    }}
                                >
                                    {getPieChartData().length === 0 ? (
                                        <View
                                            key={"no-data-rejection-chart"}
                                            style={{
                                                flexDirection: "row",
                                                justifyContent: "space-between",
                                                width: "100%",
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: "center", gap: 5 }}>
                                                <View
                                                    style={{
                                                        width: 20,
                                                        height: 20,
                                                        backgroundColor: colors.gray[300],
                                                        borderRadius: 100,
                                                    }}
                                                />
                                                <Text
                                                    style={{
                                                        fontFamily: typography.fontMedium.fontFamily,
                                                        color: colors.gray[500],
                                                    }}
                                                >
                                                    No Data
                                                </Text>
                                            </View>
                                            <View>
                                                <Text
                                                    style={{
                                                        fontFamily: typography.fontMedium.fontFamily,
                                                        color: colors.gray[900],
                                                    }}
                                                >
                                                    NaN
                                                </Text>
                                            </View>
                                        </View>
                                    ) : (
                                        getPieChartData().map((entry, index) => [
                                            <View
                                                key={index}
                                                style={{
                                                    flexDirection: "row",
                                                    justifyContent: "space-between",
                                                    width: "100%",
                                                }}
                                            >
                                                <View style={{ flexDirection: "row", alignItems: "center", gap: 5 }}>
                                                    <View
                                                        style={{
                                                            width: 20,
                                                            height: 20,
                                                            backgroundColor: entry.color,
                                                            borderRadius: 100,
                                                        }}
                                                    />
                                                    <Text
                                                        style={{
                                                            fontFamily: typography.fontMedium.fontFamily,
                                                            color: colors.gray[900],
                                                        }}
                                                    >
                                                        {entry.text}
                                                    </Text>
                                                </View>
                                                <View>
                                                    <Text
                                                        style={{
                                                            fontFamily: typography.fontMedium.fontFamily,
                                                            color: colors.gray[900],
                                                        }}
                                                    >
                                                        {entry.value}
                                                    </Text>
                                                </View>
                                            </View>,
                                            index < getPieChartData().length - 1 && (
                                                <Divider color={colors.gray[200]} />
                                            ),
                                        ])
                                    )}
                                </View>
                            </View>
                        </StatsCard>,
                        <StatsCard key="avg-first-attempt-time">
                            <DurationChart
                                key={"avg-first-attempt-time-chart"}
                                title="Average Time to first Attempt"
                                value={formatedData.cards?.avgTimeToFirstAttempt}
                                description={`The Average time for orders to be delivered by ${
                                    activeTeamMember === "store" ? "all admins" : activeTeamMember
                                }`}
                            />
                        </StatsCard>,
                    ]
                ) : (
                    <View style={{ height: "100%", alignItems: "center", paddingTop: "30%" }}>
                        <ActivityIndicator />
                    </View>
                )}
            </NativeScrollView>
            {!loading && (
                <BottomSheet
                    style={[
                        {
                            borderColor: colors.gray[100],
                            borderRadius: 15,
                            backgroundColor: "white",
                            borderWidth: 1,
                            elevation: 1,
                        },
                    ]}
                    snapPoints={[100, 145]}
                    ref={bottomSheetRef}
                >
                    <ScrollView
                        horizontal
                        contentContainerStyle={{
                            flexDirection: "row",
                            alignItems: "flex-start",
                            gap: 10,
                            paddingHorizontal: 10,
                        }}
                    >
                        <DeliveryCard
                            key={"confirmed-orders-card"}
                            simple
                            mainValueLabel="Confirmed"
                            mainValue={formatedData.cards?.confirmedOrders}
                            mainValueUnit="order"
                            MainIcon={ConfirmedIcon}
                            secondaryValueLabel="~Profit"
                            SecondaryIcon={ProfitGainIcon}
                            tertiaryValueLabel="Value"
                            TertiaryIcon={MoneyIcon}
                            variant="green"
                            color={handleStatus("deposit").color}
                        />

                        <DeliveryCard
                            key={"rejected-orders-card"}
                            simple
                            TertiaryIcon={MoneyIcon}
                            MainIcon={RejectedIcon}
                            SecondaryIcon={ProfitLossIcon}
                            mainValue={formatedData.cards?.rejectedOrders}
                            mainValueLabel="Rejected"
                            variant="red"
                            secondaryValueLabel="+Profit"
                            tertiaryValueLabel="Value"
                            mainValueUnit="order"
                            color={handleStatus("returned").color}
                        />
                        <DeliveryCard
                            key={"created-orders-card"}
                            simple
                            mainValueUnit="order"
                            TertiaryIcon={MoneyIcon}
                            MainIcon={CreatedIcon}
                            mainValue={formatedData.cards?.createdOrders}
                            mainValueLabel="Created"
                            SecondaryIcon={ProfitGainIcon}
                            secondaryValueLabel="~Profit"
                            tertiaryValueLabel="Value"
                            variant="teal"
                            color={handleStatus("in transit").color}
                        />

                        <DeliveryCard
                            key={"attempts-orders-card"}
                            simple
                            TertiaryIcon={MoneyIcon}
                            MainIcon={AttemptIcon}
                            mainValue={formatedData.cards?.attempts}
                            mainValueLabel="Attempts"
                            SecondaryIcon={ProfitGainIcon}
                            secondaryValueLabel="+Profit"
                            mainValueUnit="order"
                            tertiaryValueLabel="Value"
                            variant="orange"
                            color={handleStatus("delivered").color}
                        />
                    </ScrollView>
                </BottomSheet>
            )}
        </GestureHandlerRootView>
    );
};

export default team;
