import {
    createMaterialTopTabNavigator,
    MaterialTopTabNavigationOptions,
    MaterialTopTabNavigationEventMap,
} from "@react-navigation/material-top-tabs";

import { ParamListBase, TabNavigationState } from "@react-navigation/native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { withLayoutContext } from "expo-router";
import { useEffect } from "react";
import { useStatsStore } from "../../store/statsStore";
import { getPeriod } from "../../utils/statsUtils";

const { Navigator } = createMaterialTopTabNavigator();

export const MaterialTopTabs = withLayoutContext<
    MaterialTopTabNavigationOptions,
    typeof Navigator,
    TabNavigationState<ParamListBase>,
    MaterialTopTabNavigationEventMap
>(Navigator);

export default function Layout() {
    // const { selectRange, updateStats } = useStatsStore();
    // useEffect(() => {
    //     try {
    //         updateStats(getPeriod(7));
    //         selectRange(7);
    //     } catch (error) {
    //         console.log("error in updating stats", error);
    //     }
    // }, []);

    return (
        <MaterialTopTabs
            id={undefined}
            initialRouteName="delivery"
            screenOptions={{
                tabBarStyle: { backgroundColor: colors.gray[50] },
                tabBarLabelStyle: [
                    typography.baseText,
                    { textTransform: "capitalize" },
                    typography.sm,
                    typography.fontSemibold,
                    typography.lineHeight5,
                ],
                swipeEnabled: false,

                tabBarActiveTintColor: colors.primary[500],
                tabBarInactiveTintColor: colors.gray[500],
                tabBarIndicatorStyle: { backgroundColor: colors.primary[500] },
                tabBarAndroidRipple: {
                    borderless: true,
                    color: colors.primary[100],
                },
            }}
        >
            <MaterialTopTabs.Screen name="delivery" />
            <MaterialTopTabs.Screen name="products" />
            <MaterialTopTabs.Screen name="team" />
        </MaterialTopTabs>
    );
}
