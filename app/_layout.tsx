import React from "react";
import { useFonts } from "expo-font";
import { fonts } from "@styles/typography";
import { configureReanimatedLogger, ReanimatedLogLevel } from "react-native-reanimated";

import { setupNotificationInterceptor } from "../utils/notificationsUtils";
import AppLayout from "../components/layout/AppLayout";

// Configure Reanimated logger
configureReanimatedLogger({
    level: ReanimatedLogLevel.warn,
    strict: false,
});

// Setup notification interceptor to add unique IDs to incoming notifications
setupNotificationInterceptor();

export default function Layout() {
    const [fontLoaded] = useFonts(fonts);

    return <AppLayout fontLoaded={fontLoaded} />;
}
