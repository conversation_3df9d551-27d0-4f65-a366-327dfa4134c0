import { StyleSheet, Text, View, Dimensions, Platform, StatusBar } from "react-native";
import React from "react";
import { router, useLocalSearchParams } from "expo-router";
import { typography } from "@styles/typography";
import { colors } from "react-native-elements";

const Padding = () => {
    return <View onTouchStart={router.back} style={{ flexGrow: 1 }} />;
};

const neworkError = () => {
    const { message, status } = useLocalSearchParams();

    return (
        <View style={styles.container}>
            <View style={styles.innerContainer}>
                <Padding />
                <View style={{ flexDirection: "row" }}>
                    <Padding />
                    <View style={styles.errorCard}>
                        <Text style={[typography.altTextMedium, typography.lg, { color: colors.error }]}>
                            {status ? `Error code: ${status}` : "Network Error"}
                        </Text>
                        <Text style={[typography.fontMedium, typography.sm]}>
                            {message || "Can't load the necessary data"}
                        </Text>
                    </View>
                    <Padding />
                </View>
                <Padding />
            </View>
        </View>
    );
};

const { width, height } = Dimensions.get("screen");
const statusBarHeight = StatusBar.currentHeight || 0;

export default neworkError;

const styles = StyleSheet.create({
    container: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: width,
        height: Platform.OS === "android" ? height + statusBarHeight : height,
        backgroundColor: "rgba(0,0,0,0.25)",
    },
    innerContainer: {
        flex: 1,
        width: "100%",
        height: "100%",
    },
    errorCard: {
        backgroundColor: "white",
        alignItems: "center",
        borderRadius: 15,
        gap: 5,
        padding: 30,
    },
});
