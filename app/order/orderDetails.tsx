import React, { use<PERSON>allback, useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";
import * as _styles from "@components/editOrders/Styles";
import { Linking, Text, TouchableOpacity, View } from "react-native";
import { styles } from "@components/order/orderDetailsStyles";
import { Tag } from "@components/inputs/chips/Tag/Tag";
import { arrowSvg, phoneIconSvg } from "@components/icons/OrderPageIcons";
import { useOrderStore } from "../../store/orders";
import { Image } from "expo-image";
import Divider from "@components/dividers/Divider";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { getDecimalString, getInt } from "@components/cards/orders/OrderCard";
import { router, useFocusEffect, useLocalSearchParams, useRouter } from "expo-router";
import { useStoreStore } from "../../store/storeStore";
import { setStringAsync } from "expo-clipboard";
import Toast from "react-native-toast-message";
import { Order } from "../../types/Order";
import api from "@api/api";
import { CombinationTag, SmallImage } from "@components/productDetails/components";
import { isImageLink } from "@components/productDetails/utils";
import { GestureHandlerRootView, RefreshControl, ScrollView } from "react-native-gesture-handler";
import { CartItem } from "@components/orders/types";
import { useProductStore } from "../../store/productStore";
import { Product } from "../../types/Product";
import { BlockCustomerBottomSheet } from "@components/buttomSheetCard/BlockCustomer/BlockCustomerBottomSheet";

const OrderHistoryCard = ({ date, status, user, rejectionReason }) => {
    return (
        <View style={styles.historyCardContainer}>
            <View
                style={
                    styles.historyCardRow
                    // { alignSelf: "flex-start" }
                }
            >
                {/* <Text style={styles.historyCardlabel}>At</Text> */}
                <Text style={styles.historyCardValue}>{date}</Text>
                <Tag status={status} />
            </View>
            <View style={styles.historyCardRow}>
                <Text style={styles.historyCardlabel}>Updated by:</Text>
                <Text style={styles.historyCardValue}> {user}</Text>
            </View>
            {/* <View style={styles.historyCardRow}>
                <Text style={styles.historyCardlabel}>Status</Text>
                <Tag status={status} />
            </View> */}

            {rejectionReason && (
                <View style={styles.historyCardRow}>
                    <Text style={styles.historyCardlabel}>Rejection Reason:</Text>
                    <Text style={styles.historyCardlabel}> {rejectionReason}</Text>
                </View>
            )}
        </View>
    );
};

const OrderHistory = ({ history }) => {
    const formatDate = useMemo(
        () => (date) => {
            const d = new Date(date);
            return `${d.getDate()}/${d.getMonth()}/${d.getFullYear()} ${d.getHours()}:${d.getMinutes()}`;
        },
        []
    );

    return (
        <ScrollView contentContainerStyle={styles.historyContainer}>
            {history.map((entry, index) => (
                <View key={index} style={styles.historyItem}>
                    <OrderHistoryCard
                        date={formatDate(entry.timestamp)}
                        status={entry.status}
                        user={entry.actionTaker}
                        rejectionReason={entry.rejectionReason}
                    />
                    {index < history?.length - 1 && arrowSvg()}
                </View>
            ))}
        </ScrollView>
    );
};

const ClientInfoCard = ({ order, onCheckOrders }: { order: Partial<Order>; onCheckOrders: () => void }) => {
    const { customer: client } = order;
    const handleCallPress = () => {
        Linking.openURL(`tel:${client.phone}`);
    };

    return (
        <View style={styles.clientContainer}>
            <View style={{ gap: 10, paddingHorizontal: 15 }}>
                <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
                    <Text
                        style={{
                            fontSize: typography.lg.fontSize,
                            fontFamily: typography.fontSemibold.fontFamily,
                            color: colors.gray[600],
                        }}
                    >
                        Client Details
                    </Text>
                    <TouchableOpacity
                        style={{
                            borderWidth: 1,
                            borderColor: colors.primary[500],
                            paddingHorizontal: 12,
                            paddingVertical: 6,
                            borderRadius: 8,
                        }}
                        onPress={onCheckOrders}
                    >
                        <Text style={{ color: colors.primary[500], fontFamily: typography.fontMedium.fontFamily }}>
                            Check Orders
                        </Text>
                    </TouchableOpacity>
                </View>
                <View style={styles.row}>
                    <View style={styles.column}>
                        <Text style={styles.grayLabelForClient}>Name</Text>
                        <Text selectable selectionColor={colors.blue[200]} style={styles.grayValue}>
                            {client?.name ? client.name : "-"}
                        </Text>
                    </View>
                    <View style={styles.columnRight}>
                        <Text style={styles.grayLabelForClient}>Email</Text>
                        <Text selectable selectionColor={colors.blue[200]} style={styles.grayValue}>
                            {client?.email ? client.email : "-"}
                        </Text>
                    </View>
                </View>
                <View style={styles.row}>
                    <View style={styles.column}>
                        <Text style={styles.grayLabelForClient}>Address</Text>
                        <Text selectable selectionColor={colors.blue[200]} style={styles.grayValue}>
                            {client?.address ? client.address : "-"}
                        </Text>
                    </View>
                    <View style={styles.columnRight}>
                        <Text style={styles.grayLabelForClient}>City</Text>
                        <Text selectable selectionColor={colors.blue[200]} style={styles.grayValue}>
                            {client?.city ? client.city : "-"}
                        </Text>
                    </View>
                </View>

                <View style={styles.row}>
                    {client.note && (
                        <View style={styles.column}>
                            <Text style={styles.grayLabelForClient}>Note</Text>
                            <Text selectable selectionColor={colors.blue[200]} style={styles.grayValue}>
                                {client?.note}
                            </Text>
                        </View>
                    )}

                    {order.note && (
                        <View style={styles.columnRight}>
                            <Text style={styles.grayLabelForClient}>Private Note</Text>
                            <Text selectable selectionColor={colors.blue[200]} style={styles.grayValue}>
                                {order.note}
                            </Text>
                        </View>
                    )}
                </View>

                {client.phone && (
                    <>
                        <Divider />
                        <TouchableOpacity
                            style={styles.callButton}
                            onPress={handleCallPress}
                            onLongPress={() => {
                                setStringAsync(client.phone);
                                Toast.show({
                                    type: "success",
                                    text1: "Phone number copied",
                                    text2: "You can paste it in the phone app",
                                });
                            }}
                        >
                            {phoneIconSvg()}
                            <Text style={styles.callButtonText}>{client?.phone}</Text>
                        </TouchableOpacity>
                    </>
                )}
                {client.phone2 && (
                    <>
                        <TouchableOpacity
                            style={[styles.callButton, { backgroundColor: colors.green[700] }]} // Apply styles together
                            onPress={handleCallPress}
                            onLongPress={() => {
                                setStringAsync(client.phone2);
                                Toast.show({
                                    type: "success",
                                    text1: "Phone number 2 copied",
                                    text2: "You can paste it in the phone app",
                                });
                            }}
                        >
                            {phoneIconSvg()}
                            <Text style={styles.callButtonText}>{client?.phone2}</Text>
                        </TouchableOpacity>
                    </>
                )}
                <Divider />
            </View>

            <View style={{ alignItems: "stretch", gap: 5, width: "100%", paddingTop: 10 }}>
                <Text
                    style={[
                        {
                            paddingLeft: 15,
                            fontSize: typography.lg.fontSize,
                            fontFamily: typography.fontMedium.fontFamily,
                            color: colors.gray[600],
                        },
                    ]}
                >
                    Cart
                </Text>
                <ScrollView
                    horizontal
                    style={{ width: "100%" }}
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{ gap: 10, paddingHorizontal: 15, paddingVertical: 5 }}
                >
                    {order?.cart?.map((cartItem, index) => (
                        <ProductCartCard cartItem={cartItem} key={index} />
                    ))}
                </ScrollView>
            </View>
        </View>
    );
};

const OrderDetails = () => {
    const { order, setOrder, loading } = useOrderStore();
    const [showBlockSheet, setShowBlockSheet] = useState(false);

    const formattedDate = useMemo(() => {
        return order ? new Date(order?.createdAt).toLocaleDateString() : "";
    }, [order]);

    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <ScrollView
                contentContainerStyle={{
                    flexGrow: 1,
                    paddingHorizontal: 10,
                    paddingTop: 5,
                    paddingBottom: 15,
                    gap: 10,
                }}
            >
                <>
                    <View style={styles.orderContainer}>
                        <View style={{ alignItems: "center" }}>
                            <Text style={styles.title}>Order Total</Text>
                            <Text style={styles.total}>
                                {getInt(order?.total?.totalPrice)}
                                <Text style={{ fontSize: 18 }}>
                                    .{getDecimalString(Number(order?.total?.totalPrice), 2)}{" "}
                                    {useStoreStore.getState().store.currency.code}
                                </Text>
                            </Text>
                        </View>

                        <View style={styles.detailsContainer}>
                            <View style={styles.row}>
                                <View style={styles.column}>
                                    <Text style={styles?.label}>Reference</Text>
                                    <Text style={styles.value}>#{order?.reference}</Text>
                                </View>
                                <View style={styles.columnRight}>
                                    <Text style={styles?.label}>Date Added</Text>
                                    <Text style={styles.value}>{formattedDate}</Text>
                                </View>
                            </View>

                            <View style={styles.row}>
                                <View style={[styles.column, { alignItems: "flex-start", flex: 0, gap: 1 }]}>
                                    <Text style={styles?.label}>Delivery</Text>
                                    <Tag delivery={order?.deliveryCompany} />
                                </View>
                                <View style={styles.columnRight}>
                                    <Text style={styles?.label}>Delivery Price</Text>
                                    <Text style={styles.value}>
                                        {order?.total?.deliveryPrice} {useStoreStore.getState().store.currency.code}
                                    </Text>
                                </View>
                            </View>

                            <View style={styles.row}>
                                <View style={styles.column}>
                                    <Text style={styles?.label}>Status</Text>
                                </View>
                                <View style={styles.columnRight}>
                                    {order.isTest && (order?.status === "pending" || order?.status === "abandoned") ? (
                                        <Tag status={"test"} />
                                    ) : order.duplicated &&
                                      (order?.status === "pending" || order?.status === "abandoned") ? (
                                        <Tag status={"duplicated"} />
                                    ) : (
                                        <Tag status={order?.status} attempt={order.attempt} />
                                    )}
                                </View>
                            </View>
                            {order.barcode && (
                                <View style={[styles.row, { justifyContent: "center" }]}>
                                    <Text
                                        selectable
                                        selectionColor={colors.blue[200]}
                                        style={[
                                            styles.label,
                                            {
                                                fontFamily: "LibreBarcode39Text-Regular",
                                                fontSize: order.barcode ? order.barcode.length * -2.35 + 86 : 16,
                                            },
                                        ]}
                                    >
                                        {order.barcode}
                                    </Text>
                                </View>
                            )}
                        </View>
                    </View>

                    <View>
                        <ClientInfoCard order={order} onCheckOrders={() => setShowBlockSheet(true)} />
                    </View>

                    {order?.history?.length > 1 && (
                        <View
                            style={{
                                elevation: 3,
                                backgroundColor: colors.gray[50],
                                borderRadius: 15,
                                paddingHorizontal: 20,
                                paddingVertical: 10,
                                gap: 10,
                                shadowColor: colors.gray["900"], // IOS
                                shadowOffset: { height: 1, width: 0 }, // IOS
                                shadowOpacity: 0.2, // IOS
                                shadowRadius: 1.5, //IOS
                            }}
                        >
                            <Text style={styles.historyTitle}>Order History</Text>
                            <OrderHistory history={order?.history} />
                        </View>
                    )}
                </>
            </ScrollView>

            <BlockCustomerBottomSheet
                visible={showBlockSheet}
                orderId={order?._id}
                onClose={() => setShowBlockSheet(false)}
                onSuccess={(isDeleteAll) => {
                    if (isDeleteAll) {
                        router.replace({ pathname: "/(mainNav)/orders", params: { refresh: "true" } });
                        return;
                    }

                    if (order) {
                        // Update the local order state to show it's deleted
                        // router.replace({ pathname: "/(mainNav)/orders" });
                        if (order.status === "deleted") {
                            setOrder({ ...order, status: "restored" });
                            Toast.show({
                                type: "success",
                                text1: "Order Restored",
                                text2: `Order #${order.reference} has been restored`,
                            });
                        } else {
                            setOrder({ ...order, status: "deleted" });
                            // Show a toast notification
                            Toast.show({
                                type: "success",
                                text1: "Order Deleted",
                                text2: `Order #${order.reference} has been deleted`,
                            });
                        }
                    }
                }}
            />
        </GestureHandlerRootView>
    );
};

export default OrderDetails;

const ProductCartCard = ({
    cartItem: { product, pricePerUnit, selectedVariants, quantity },
}: {
    cartItem: CartItem;
}) => {
    const { store } = useStoreStore();
    const { setSelectedProduct } = useProductStore();

    const router = useRouter();

    return (
        <TouchableOpacity
            onPress={() => {
                setSelectedProduct(product as Product);
                router.navigate({ pathname: "/products/productDetails/[ref]", params: { ref: product.reference } });
            }}
            style={[
                _styles.styles.productContainer,
                {
                    backgroundColor: colors.white,
                    elevation: 3,
                    shadowColor: colors.gray["900"], // IOS
                    shadowOffset: { height: 1, width: 0 }, // IOS
                    shadowOpacity: 0.2, // IOS
                    shadowRadius: 1.5, //IOS
                    borderWidth: 0.5,
                },
            ]}
        >
            <Image
                source={{ uri: product?.images[0].md ?? product?.images[0].sm }}
                style={_styles.styles.productImage}
            />
            <View style={_styles.styles.productInfo}>
                <Text style={_styles.styles.productName}>{product?.name}</Text>
                <View style={{ flexDirection: "row", alignSelf: "stretch", justifyContent: "space-between" }}>
                    <Text style={_styles.styles.cartItemLabel}>Qty.</Text>
                    <Text style={_styles.styles.cartItemLabel}>{quantity}</Text>
                </View>
                <View style={{ flexDirection: "row", alignSelf: "stretch", justifyContent: "space-between" }}>
                    <Text style={_styles.styles.cartItemLabel}>Price</Text>
                    <Text style={_styles.styles.cartItemLabel}>
                        {pricePerUnit.toFixed(2)}
                        <Text style={{ fontSize: 12 }}>{store.currency.code}</Text>
                    </Text>
                </View>
                <ScrollView
                    showsHorizontalScrollIndicator={false}
                    horizontal
                    contentContainerStyle={{ paddingVertical: 5, gap: 10, paddingHorizontal: 10 }}
                >
                    {selectedVariants &&
                        selectedVariants.map((variant, index) =>
                            isImageLink(variant.value) ? (
                                <SmallImage source={{ uri: variant.value }} key={index} />
                            ) : (
                                <CombinationTag key={index} value={variant.value} />
                            )
                        )}
                </ScrollView>
            </View>
        </TouchableOpacity>
    );
};
