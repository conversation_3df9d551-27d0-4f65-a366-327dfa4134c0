import ModalHeader from "@components/Navigation/ModalHeader";
import { Stack, useRouter } from "expo-router";
import * as React from "react";
import { useEffect } from "react";
import { useOrderStore } from "../../store/orders";
import { useStoreStore } from "../../store/storeStore";
import { GestureHandlerRootView } from "react-native-gesture-handler";

const _layout = () => {
    const router = useRouter();
    const { updateOrder, loading } = useOrderStore();
    const { user } = useStoreStore();

    return (
        <>
            <GestureHandlerRootView style={{ flex: 1 }}>
                <Stack.Screen options={{ headerShown: false }} />
                <Stack initialRouteName="edit">
                    <Stack.Screen
                        name="edit"
                        options={{
                            headerTitle: "Edit Order",
                            animation: "fade_from_bottom",
                            header: ({ navigation, options, route, back }) => {
                                return (
                                    <ModalHeader
                                        navigation={navigation}
                                        options={options}
                                        route={route}
                                        back={back}
                                        loading={loading}
                                        modalButtonAction={() => {
                                            const editOrder = JSON.parse(route.params["editOrder"]);

                                            updateOrder({ ...editOrder });
                                        }}
                                        modalButtonLabel="save"
                                    />
                                );
                            },
                        }}
                    />
                    <Stack.Screen
                        name="orderDetails"
                        options={{
                            headerTitle: "Order Detail",
                            header: ({ navigation, options, route, back }) => {
                                return (
                                    <ModalHeader
                                        navigation={navigation}
                                        options={options}
                                        route={route}
                                        back={back}
                                        modalButtonAction={() =>
                                            (user?.permissions === "all" || user?.permissions?.order?.update) &&
                                            router.navigate("/order/edit")
                                        }
                                        backIcon
                                        modalButtonLabel={
                                            user?.permissions === "all" || user?.permissions?.order?.update
                                                ? "edit"
                                                : undefined
                                        }
                                    />
                                );
                            },
                        }}
                    />
                </Stack>
            </GestureHandlerRootView>
        </>
    );
};

export default _layout;
