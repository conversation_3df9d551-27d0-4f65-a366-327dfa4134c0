import React, { useCallback, useEffect, useLayoutEffect, useState, useRef } from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import { useOrderStore } from "../../store/orders";
import colors from "@styles/colors";
import { useStoreStore } from "../../store/storeStore";

import { router } from "expo-router";
import { EditOrderDetails } from "@components/editOrders/EditOrderDetails";
import { EditCustomerDetails } from "@components/editOrders/EditCustomerDetails";
import { EditDeliveryDetails } from "@components/editOrders/EditDeliveryDetails";
import { capitalizeFirstLetter } from "@components/editOrders/utils";
import Button from "@components/inputs/buttons/Button";
import api from "@api/api";
import Toast from "react-native-toast-message";
import ModalBase from "@components/Navigation/ModalView/ModalBase";
import { typography } from "@styles/typography";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
// Uncomment the line below to add debug tests to this page
// import DeleteTestComponent from "@components/debug/DeleteTestComponent";

const Edit = () => {
    const { order, getProducts, products, findOrderAndDelete, setLoading, restoreOrder } = useOrderStore();
    const { store, auth, user } = useStoreStore();
    const [integratedCompanies, setIntegratedCompanies] = useState<
        {
            value: string;
            label: string;
        }[]
    >([]);
    const [updatedOrderValues, setUpdatedOrderValues] = useState(order);
    const [isDeleting, setIsDeleting] = useState(false);
    const deleteTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const navigationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // useFocusEffect(
    //     useCallback(() => {
    //         if (orderWasDeleted) {
    //             router.back();
    //             Toast.show({
    //                 type: "info",
    //                 text1: "test test",
    //                 text2: `teeeest`,
    //             })
    //         }
    //         return () => {
    //             setOrderWasDeleted(false);
    //         };
    //     }, [orderWasDeleted])
    // );

    useLayoutEffect(() => {
        getProducts();
        router.setParams({ editOrder: JSON.stringify(order) });
    }, []);

    useLayoutEffect(() => {
        router.setParams({ editOrder: JSON.stringify(updatedOrderValues) });
    }, [updatedOrderValues]);

    useEffect(() => {
        if (store && auth === "loaded") {
            const _integrations = store.integrations
                .filter(
                    (integration) =>
                        !["microsoft-clarity", "google-analytics", "facebook", "tiktok"].includes(integration.ref) &&
                        integration.integrated === true
                )
                ?.map((integration) => ({
                    value: integration.ref,
                    label: capitalizeFirstLetter(integration.ref),
                }));
            setIntegratedCompanies(_integrations);
        }
    }, [store, auth]);

    // Cleanup timeouts on unmount and handle app state changes
    useEffect(() => {
        return () => {
            if (deleteTimeoutRef.current) {
                clearTimeout(deleteTimeoutRef.current);
            }
            if (navigationTimeoutRef.current) {
                clearTimeout(navigationTimeoutRef.current);
            }
            // Reset loading state on unmount to prevent stuck loaders
            if (isDeleting) {
                setLoading(false);
            }
        };
    }, [isDeleting]);

    // Handle app state changes (iOS background/foreground)
    useEffect(() => {
        const handleAppStateChange = (nextAppState: string) => {
            if (nextAppState === "background" && isDeleting) {
                console.log("App backgrounded during delete, cleaning up...");
                // Clean up any pending operations
                if (deleteTimeoutRef.current) {
                    clearTimeout(deleteTimeoutRef.current);
                }
                setLoading(false);
                setIsDeleting(false);
            }
        };

        const { AppState } = require("react-native");
        const subscription = AppState.addEventListener("change", handleAppStateChange);

        return () => {
            subscription?.remove();
        };
    }, [isDeleting]);

    // if (loading)
    //     return (
    //         <View style={{ height: "100%", alignItems: "center", justifyContent: "center" }}>
    //             <ActivityIndicator size="large" color={colors.primary[500]} />
    //         </View>
    //     );

    const safeNavigateBack = useCallback(() => {
        try {
            // Clear any existing navigation timeout
            if (navigationTimeoutRef.current) {
                clearTimeout(navigationTimeoutRef.current);
            }

            // Use immediate navigation for better reliability
            // if (router.canGoBack()) {
            //     router.back();
            // } else {
            router.replace("/(mainNav)/orders");
            // }
        } catch (error) {
            console.error("Navigation error:", error);
            // Fallback navigation
            router.replace("/(mainNav)/orders");
        }
    }, []);

    const deleteOrder = useCallback(async () => {
        if (isDeleting) {
            console.log("Delete already in progress, ignoring duplicate call");
            return;
        }

        try {
            setIsDeleting(true);
            setLoading(true);

            console.log(`Starting delete for order ${order._id} (${order.reference})`);

            // Set a timeout to prevent infinite loading
            deleteTimeoutRef.current = setTimeout(() => {
                console.log("Delete timeout reached, forcing cleanup");
                setLoading(false);
                setIsDeleting(false);
                Toast.show({
                    type: "error",
                    text1: "Request Timeout",
                    text2: "The delete request took too long. Please try again.",
                });
            }, 15000); // 15 second timeout

            const response = await api.delete(`/order/${order._id}`);

            // Clear timeout since we got a response
            if (deleteTimeoutRef.current) {
                clearTimeout(deleteTimeoutRef.current);
                deleteTimeoutRef.current = null;
            }

            if (response.success) {
                console.log(`Order ${order.reference} deleted successfully`);

                // Immediately update local state as fallback
                // findOrderAndDelete(order._id);

                //comment if there is double toast
                // Show success message
                // Toast.show({
                //     type: "success",
                //     text1: "Order Deleted",
                //     text2: `Order #${order.reference} has been deleted`,
                // });

                // Navigate back immediately - don't wait for socket events
                safeNavigateBack();
            } else {
                throw new Error(response.message || "Failed to delete order");
            }
        } catch (error) {
            console.error("Delete error:", error);

            // Clear timeout on error
            if (deleteTimeoutRef.current) {
                clearTimeout(deleteTimeoutRef.current);
                deleteTimeoutRef.current = null;
            }

            Toast.show({
                type: "error",
                text1: "Failed To Delete Order",
                text2: error?.response?.data?.message || error?.message || `Couldn't Delete Order ${order.reference}.`,
            });
        } finally {
            // Always cleanup loading states
            setLoading(false);
            setIsDeleting(false);
        }
    }, [order._id, order.reference, isDeleting, findOrderAndDelete, safeNavigateBack]);
    const [visible, setVisible] = useState(false);
    const [modalType, setModalType] = useState<"delete" | "restore">("delete");

    return (
        <ScrollView contentContainerStyle={styles.container} nestedScrollEnabled>
            <EditOrderDetails
                pageType="edit"
                integratedCompanies={integratedCompanies}
                order={order}
                setUpdatedOrderValues={setUpdatedOrderValues}
                updatedOrderValues={updatedOrderValues}
            />
            <EditCustomerDetails
                order={order}
                setUpdatedOrderValues={setUpdatedOrderValues}
                updatedOrderValues={updatedOrderValues}
            />
            <EditDeliveryDetails
                order={order}
                products={products}
                setUpdatedOrderValues={setUpdatedOrderValues}
                store={store}
                updatedOrderValues={updatedOrderValues}
            />
            {!order.barcode &&
                (user?.permissions === "all" || user?.permissions?.order?.delete) &&
                (order.status !== "deleted" ? (
                    <Button
                        label={isDeleting ? "Deleting..." : "Delete Order"}
                        action={() => {
                            if (!isDeleting) {
                                setModalType("delete");
                                setVisible(true);
                            }
                        }}
                        variant="red"
                        type="filled"
                        style={{ height: 50 }}
                        disabled={isDeleting}
                    />
                ) : (
                    <Button
                        label={"Restore Order"}
                        action={() => {
                            setModalType("restore");
                            setVisible(true);
                        }}
                        variant="green"
                        type="filled"
                        style={{ height: 50 }}
                    />
                ))}
            <ModalBase visible={visible} setVisible={setVisible}>
                <View style={{ gap: 10 }}>
                    <Text
                        style={[typography.fontMedium, typography.lg, { color: colors.gray[800], textAlign: "center" }]}
                    >
                        {modalType === "delete" ? "Delete" : "Restore"} Order #{order.reference}
                    </Text>
                    <Text style={[typography.fontNormal, typography.md, { color: colors.gray[800] }]}>
                        Are you sure you want to {modalType === "delete" ? "delete" : "restore"} Order{" "}
                        <Text style={[typography.altTextMedium, { color: colors.primary[700] }]}>
                            #{order?.reference}
                        </Text>
                        ?
                    </Text>
                    <View style={{ flexDirection: "row", gap: 10 }}>
                        <TextButton
                            style={{ flex: 1 }}
                            label="Cancel"
                            variant="outlined"
                            color={modalType === "delete" ? colors.red[500] : colors.green[500]}
                            onPress={() => {
                                setVisible(false);
                            }}
                        />
                        <TextButton
                            style={{ flex: 1 }}
                            label={modalType === "delete" ? "Delete" : "Restore"}
                            variant="contained"
                            color={modalType === "delete" ? colors.red[500] : colors.green[500]}
                            disabled={isDeleting}
                            onPress={async () => {
                                if (modalType === "delete") {
                                    setVisible(false); // Close modal first to prevent conflicts
                                    await deleteOrder();
                                } else {
                                    try {
                                        setVisible(false);
                                        await restoreOrder(order._id);
                                        safeNavigateBack();
                                    } catch (error) {
                                        console.error("Restore error:", error);
                                        Toast.show({
                                            type: "error",
                                            text1: "Failed to Restore Order",
                                            text2: error?.message || "Please try again",
                                        });
                                    }
                                }
                            }}
                        />
                    </View>
                </View>
            </ModalBase>

            {/* Uncomment the component below to add debug tests to this page */}
            {/* <DeleteTestComponent onTest={(testName, result, details) => {
                console.log(`[ORDER EDIT TEST] ${testName}: ${result}`, details);
            }} /> */}
        </ScrollView>
    );
};

export default Edit;

const styles = StyleSheet.create({
    container: {
        paddingVertical: 20,
        paddingHorizontal: 10,
        gap: 20,
        backgroundColor: colors.gray[100],
        paddingBottom: 50,
    },
});
