import { Dimensions, Platform, StyleSheet, Text, TextInput, TouchableOpacity, View } from "react-native";
import React, { useEffect, useState } from "react";
import { Filter, OrderResponseProp } from "../components/orders/types";
import api from "@api/api";
import { router, Slot } from "expo-router";
import { useStoreStore } from "../store/storeStore";
import { useOrderStore } from "../store/orders";
import Button from "../components/inputs/buttons/Button";
import Toast from "react-native-toast-message";
import { Status } from "../types/Order";
import colors from "@styles/colors";
import { FlatList, GestureHandlerRootView, RefreshControl } from "react-native-gesture-handler";
import { BarcodeScanningResult, Camera, CameraView, Point } from "expo-camera";
import Svg, { Polygon } from "react-native-svg";

const statusList = ["all", "restored", "uploaded", "confirmed", "rejected"];

const Debug = () => {
    const { store, user } = useStoreStore();
    const { orders, setOrders } = useOrderStore();

    const getOrders = async (page: number, filter: Filter) => {
        try {
            if (user?.permissions !== "all" && !user?.permissions?.order?.read) {
                router.navigate({
                    pathname: "/network-error",
                    params: {
                        message: "You do not have permission to view orders",
                    },
                });
                return [];
            }

            const queryParams = new URLSearchParams();

            queryParams.append("mobile", "true");
            queryParams.append("limit", String(10));
            queryParams.append("page", String(page));

            const filterArray = Object.entries(filter);

            filterArray.forEach(([key, value]) => {
                queryParams.append(key, String(value));
            });

            const response: OrderResponseProp = await api.get(`/order?${queryParams.toString()}`);

            return response.data;
        } catch (error) {
            router.navigate({
                pathname: "/network-error",
                params: error,
            });
            return [];
        }
    };

    const [filter, setFilter] = useState<Filter>({});
    const [page, setPage] = useState(1);
    const [noMore, setNoMore] = useState(false);
    const [fetchMore, setFetchMore] = useState(true);
    const [loading, setLoading] = useState(true);

    const { order, setOrder } = useOrderStore();

    useEffect(() => {
        if (fetchMore && !noMore) {
            setLoading(true);
            getOrders(page, filter)
                .then((res) => {
                    setOrders([...orders, ...res]);
                    res.length === 10
                        ? setPage((prev) => {
                              return prev + 1;
                          })
                        : setNoMore(true);
                })
                .catch(() => {
                    Toast.show({ text1: "error" });
                })
                .finally(() => {
                    setLoading(false);
                    setFetchMore(false);
                });
        } else {
            console.log("here");
        }
    }, [page, fetchMore]);

    const handleFilterChange = ({ status, search }: Partial<Filter>) => {
        !status &&
            !search &&
            setFilter(() => {
                return {};
            });
        status &&
            setFilter((prev) => {
                return { ...prev, status: status !== "all" ? status : "" };
            });
        (search || search == "") && setFilter((prev) => ({ ...prev, search }));
        setOrders([]);
        setPage(1);
        setNoMore(false);
        setFetchMore(true);
    };

    const [code, setCode] = useState<BarcodeScanningResult>();

    return (
        <>
            <GestureHandlerRootView style={{ gap: 10 }}>
                <View style={{ flexDirection: "column", gap: 10 }}>
                    <Text>{JSON.stringify({ page, noMore, fetchMore })}</Text>
                    <Text>{JSON.stringify(filter)}</Text>
                    <Button
                        disabled={noMore}
                        type="filled"
                        variant="blue"
                        action={() => {
                            !noMore &&
                                getOrders(page, filter)
                                    .then((res) => {
                                        setOrders([...orders, ...res]);
                                        res.length === 10
                                            ? setPage((prev) => {
                                                  return prev + 1;
                                              })
                                            : setNoMore(true);
                                    })
                                    .catch(() => {
                                        Toast.show({ text1: "error" });
                                    })
                                    .finally(() => {
                                        setFetchMore(false);
                                    });
                        }}
                        label="fetch more"
                    />
                    <Button
                        type="filled"
                        variant="blue"
                        action={() => {
                            setOrders([]);
                            setPage(1);
                        }}
                        label="clear orders"
                    />
                </View>
                <View>
                    <Text>search</Text>
                    <TextInput
                        onChangeText={(t) => {
                            handleFilterChange({ search: t });
                        }}
                        style={{ borderWidth: 1, borderRadius: 5, borderColor: "gray", padding: 10 }}
                    />
                </View>
                <View style={{ flexDirection: "row", gap: 20 }}>
                    {statusList.map((status, index) => {
                        return (
                            <TouchableOpacity
                                key={index}
                                style={[status === filter.status && { backgroundColor: "gray" }, { padding: 20 }]}
                                onPress={() => {
                                    handleFilterChange({ status });
                                }}
                            >
                                <Text>{status}</Text>
                            </TouchableOpacity>
                        );
                    })}
                </View>
                <FlatList
                    style={{ height: 300, borderWidth: 1 }}
                    contentContainerStyle={{ gap: 10 }}
                    data={orders}
                    renderItem={({ item }) => {
                        return (
                            <TouchableOpacity
                                onPress={() => {
                                    setOrder(item);
                                    router.navigate({
                                        pathname: "/order/orderDetails",
                                        params: { order: order._id },
                                    });
                                }}
                                style={{ backgroundColor: colors.gray[200] }}
                            >
                                <Text>{item.reference}</Text>
                                <Text>{item.customer.name}</Text>
                            </TouchableOpacity>
                        );
                    }}
                    onEndReached={() => {
                        if (!noMore) {
                            console.log("end not reached");
                            setFetchMore(true);
                        }
                    }}
                    refreshControl={
                        <RefreshControl
                            refreshing={loading}
                            onRefresh={() => {
                                setPage((prev) => {
                                    return 1;
                                });
                                setOrders([]);
                                setFetchMore(true);
                                setNoMore(false);
                            }}
                        />
                    }
                />
            </GestureHandlerRootView>
        </>
        // <View style={{ flex: 1, position: "relative", justifyContent: "center" }}>
        //     <CameraView
        //         barcodeScannerSettings={{ barcodeTypes: ["qr"] }}
        //         style={{
        //             height: "100%",
        //             width: "100%",
        //             paddingBottom: 30,
        //             alignItems: "center",
        //         }}
        //         onBarcodeScanned={(code) => {
        //             setCode(code);
        //         }}
        //     >
        //         <View style={{ backgroundColor: "white", height: 100, width: "75%" }}>
        //             <Text>{JSON.stringify(code?.cornerPoints)}</Text>
        //         </View>
        //     </CameraView>
        //     <ScanningBounderies points={code?.cornerPoints} code={code} />
        // </View>
    );
};
const ScanningBounderies = ({ points, code }: { points: Point[]; code: BarcodeScanningResult }) => {
    let stringCoordinates = "";
    points?.map((point) => {
        stringCoordinates =
            `${(16 / 9) * (Dimensions.get("window").width - point?.x)},${point?.y} ` + stringCoordinates;
    });
    // if (Platform.OS === "android" && code) {
    //     return (
    //         <View style={{ position: "absolute", height: "100%", width: "100%" }}>
    //             <View
    //                 style={{
    //                     position: "absolute",

    //                     bottom: code.bounds.origin.y,
    //                     left: code.bounds.origin.x,
    //                     height: code.bounds.size.height,
    //                     width: code.bounds.size.width,
    //                     backgroundColor: "black",
    //                 }}
    //             ></View>
    //         </View>
    //     );
    // }

    return (
        <Svg
            height="100%"
            width="100%"
            style={{
                position: "absolute", // Absolute positioning for the SVG
                top: 0,
                left: 0,
            }}
            fillOpacity={0.2}
        >
            <Polygon
                points={stringCoordinates} // x1,y1 x2,y2 x3,y3 x4,y4
                fill="blue" // Fill color of the quadrilateral
                stroke="black" // Border color
                strokeWidth="0.5"
            />
        </Svg>
    );
};

export default Debug;

const styles = StyleSheet.create({});
