import { View, Text, LayoutChangeEvent } from "react-native";
import React, { useEffect, useState } from "react";
import { StyleSheet } from "react-native";
import { Gesture, GestureDetector, GestureHandlerRootView } from "react-native-gesture-handler";
import Swipeable from "react-native-gesture-handler/ReanimatedSwipeable";
import Animated, { useSharedValue, useAnimatedStyle, withSpring, withTiming, runOnJS } from "react-native-reanimated";
import colors from "@styles/colors";
import { router } from "expo-router";
import { EditIcon } from "@components/icons/BudgetManagerIcons";

const testing = () => {
    return (
        <GestureHandlerRootView style={{ gap: 30 }}>
            <Text>degaa</Text>
            <Swipeable
                leftThreshold={5}
                renderLeftActions={() => {
                    return (
                        <View style={{ backgroundColor: colors.orange[500] }}>
                            <Text>edit</Text>
                            <EditIcon />
                        </View>
                    );
                }}
            >
                <View style={styles.ball}></View>
            </Swipeable>
            <Ball />
        </GestureHandlerRootView>
    );
};

function Ball() {
    const isPressed = useSharedValue(false);
    const start = useSharedValue({ x: 0 });
    const offset = useSharedValue({ x: 0 });
    const [dragEnded, setDragEnded] = useState(0);

    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

    const [state, setState] = useState(false);

    const onLayout = (event: LayoutChangeEvent) => {
        const { width, height } = event.nativeEvent.layout;
        setDimensions({ width, height });
    };

    const tapGesture = Gesture.Tap().onBegin(() => {});

    const gesture = Gesture.Pan()
        .onBegin(() => {
            isPressed.value = true;
        })
        .onUpdate((e) => {})
        .onEnd(() => {
            runOnJS(setState)(!state);
        })
        .onFinalize(() => {
            isPressed.value = false;
        });
    return (
        <GestureDetector gesture={gesture}>
            <Animated.View style={[styles.ball]}>
                <Text>{JSON.stringify(dimensions)}</Text>
                {<Text>{JSON.stringify(state)}</Text>}
            </Animated.View>
        </GestureDetector>
    );
}

const styles = StyleSheet.create({
    ball: {
        width: "100%",
        height: 100,
        borderRadius: 15,
        backgroundColor: colors.gray[300],
        alignSelf: "center",
        alignItems: "center",
        justifyContent: "center",
    },
});

export default testing;
