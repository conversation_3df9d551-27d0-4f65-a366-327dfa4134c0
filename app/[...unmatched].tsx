import React, { useEffect } from "react";
import { Redirect, usePathname, Stack } from "expo-router";
import { View, ActivityIndicator, Linking } from "react-native";
import colors from "@styles/colors";
import { useAuthStore } from "../store/authStore";

export default function UnmatchedRoute() {
    const pathname = usePathname();
    const auth = useAuthStore((state) => state.auth);

    // Log the unmatched route for debugging
    useEffect(() => {
        console.log(`Redirecting from unmatched route: ${pathname}`);

        // Check if the path is login or signup
        if (pathname === "/login" || pathname === "/signup") {
            // Close the app and redirect to browser
            const url = `https://partner.converty.shop${pathname}`;
            Linking.openURL(url).catch((err) => console.error("Couldn't open URL", err));
            return;
        }
    }, [pathname]);

    return (
        <>
            <Stack.Screen options={{ headerShown: false }} />

            {auth === "pending" ? (
                <View
                    style={{ flex: 1, justifyContent: "center", alignItems: "center", backgroundColor: colors.white }}
                >
                    <ActivityIndicator size="large" color={colors.orange[500]} />
                </View>
            ) : auth === "auth" ? (
                <Redirect href="/(mainNav)/(home)" />
            ) : (
                <Redirect href="/" />
            )}
        </>
    );
}
