import React, { useEffect, useState } from "react";
import { View, FlatList, Text, Image, ActivityIndicator } from "react-native";
import TeamCard from "@components/cards/team/TeamCard";
import colors from "@styles/colors";
import api from "@api/api";
import { router } from "expo-router";

const Team = () => {
    const [team, setTeam] = useState([]);
    const [loading, setLoading] = useState(true);
    const [noPermession, setNoPermission] = useState(false);

    useEffect(() => {
        api.get("/staff")
            .then((res: any) => {
                setTeam(res.data);
            })
            .catch((e) => {
                console.error(e);
                if (e === "insufficient permissions") {
                    setNoPermission(true);
                } else {
                    setNoPermission(false);
                    router.navigate("/network-error");
                }
            })
            .finally(() => setLoading(false));
    }, []);

    if (loading) {
        return (
            <View style={{ height: "80%", justifyContent: "center" }}>
                <ActivityIndicator color={colors.primary[400]} size="large" />
            </View>
        );
    }

    if (noPermession) {
        return (
            <View
                style={{
                    width: "100%",
                    height: "80%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                }}
            >
                <Text style={{ fontWeight: "bold" }}>You Dont Have Permission!</Text>
            </View>
        );
    }

    if (team.length === 0) {
        return (
            <View
                style={{
                    width: "100%",
                    height: "80%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                }}
            >
                <Text style={{ fontWeight: "bold" }}>No team members!</Text>
            </View>
        );
    }

    return (
        <View>
            <FlatList
                contentContainerStyle={{ paddingLeft: 10, paddingRight: 10 }}
                data={team}
                renderItem={({ item, index }) => <TeamCard user={item} key={index} />}
                ItemSeparatorComponent={() => <View style={{ height: 10 }} />}
            />
        </View>
    );
};

export default Team;
