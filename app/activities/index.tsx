import api from "@api/api";
import ActivityCard from "@components/team/ActivitiesCard";
import colors from "@styles/colors";
import React, { useEffect, useState } from "react";
import { ActivityIndicator, FlatList, RefreshControl, Text, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

type Activity = { user: string; action: string; timestamp: number; collection?: string; document: number };

const Activities = () => {
    const [activities, setActivities] = useState<Activity[]>([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);

    const fetchActivities = async () => {
        try {
            const res = await api.get("/activities");

            setActivities((res.data as Activity[]).reverse());
        } catch (error) {
            console.error("Failed to fetch blocked IPs", error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useEffect(() => {
        fetchActivities();
    }, []);

    const handleRefresh = () => {
        setRefreshing(true);
        fetchActivities();
    };

    if (loading) {
        return (
            <View style={{ height: "80%", justifyContent: "center" }}>
                <ActivityIndicator color={colors.primary[400]} size="large" />
            </View>
        );
    }

    if (activities.length === 0) {
        return (
            <View
                style={{
                    width: "100%",
                    height: "80%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                }}
            >
                <Text style={{ fontWeight: "bold" }}>No blocked IPs!</Text>
            </View>
        );
    }

    return (
        <GestureHandlerRootView style={{ height: "100%" }}>
            <FlatList
                contentContainerStyle={{ paddingLeft: 10, paddingRight: 10, gap: 10, paddingBottom: 120 }}
                refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
                data={activities}
                renderItem={({ item }) => {
                    if (item.action === "orderUpdated") item.action = "updated";
                    let action = `${item.action}${item.collection ? " " + item.collection : ""}${
                        item.document ? " " + item.document : ""
                    }`;
                    return <ActivityCard user={item.user} action={action} time={item.timestamp} />;
                }}
            />
        </GestureHandlerRootView>
    );
};

export default Activities;
