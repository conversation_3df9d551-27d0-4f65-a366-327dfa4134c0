import React, { useLayoutEffect, useState, useEffect } from "react";
import { StyleSheet, Text, TouchableOpacity, View, ScrollView, Switch, Image } from "react-native";
import { useStoreStore } from "../../../store/storeStore";
import { useLoaderStore } from "../../../store/loaderStore";
import { useNavigation, useLocalSearchParams, useRouter } from "expo-router";
import Toast from "react-native-toast-message";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import Divider from "@components/dividers/Divider";
import { isDeviceTokenRegistered } from "../../../utils/notificationsUtils";
import { useAuthStore } from "../../../store/authStore";

type SettingItemProps = { title: string; onPress: () => void; selected?: boolean };

const SettingItem = ({ title, onPress, selected }: SettingItemProps) => {
    return (
        <TouchableOpacity onPress={() => !selected && onPress()} style={styles.settingItem}>
            <Text style={styles.settingItemText}>{title}</Text>
            <View style={[styles.radioButton, { backgroundColor: selected ? colors.blue[500] : colors.gray[50] }]} />
        </TouchableOpacity>
    );
};

const StoreSubscriptionItem = ({ store, subscribed, onToggle }) => {
    return (
        <View style={styles.storeItem}>
            <View style={styles.storeInfo}>
                <View style={[styles.storeIcon, { backgroundColor: colors.white }]}>
                    {store.logo?.sm ? (
                        <Image source={{ uri: store.logo.sm }} style={styles.storeLogo} />
                    ) : (
                        <Text style={styles.storeInitial}>{store.name.charAt(0)}</Text>
                    )}
                </View>
                <Text style={styles.storeName}>{store.name}</Text>
            </View>
            <Switch
                value={subscribed}
                onValueChange={onToggle}
                trackColor={{ false: colors.gray[300], true: colors.blue[300] }}
                thumbColor={subscribed ? colors.blue[500] : colors.gray[100]}
            />
        </View>
    );
};

const NotificationSettings = () => {
    const router = useRouter();
    const { user, auth, updateUser, stores } = useStoreStore();
    const { setLoading } = useLoaderStore.getState();
    const [storeSubscriptions, setStoreSubscriptions] = useState({});
    const [selectedSound, setSelectedSound] = useState("");
    const [subscribedStoreIds, setSubscribedStoreIds] = useState<string[]>([]);
    const [isDeviceRegistered, setIsDeviceRegistered] = useState<boolean | null>(null);

    // Listen for save action from the header
    const params = useLocalSearchParams();
    useEffect(() => {
        if (params.saveChanges === "true") {
            saveChanges();
            // Clear the param after handling
            router.setParams({ saveChanges: "" });
        }
    }, [params.saveChanges]);

    // Handle loading state based on auth
    useEffect(() => {
        if (auth === "loading") {
            setLoading(true);
        } else {
            setLoading(false);
        }
    }, [auth, setLoading]);

    const saveChanges = async () => {
        setLoading(true);
        try {
            const result = await updateUser({
                ...user,
                preferences: {
                    ...user?.preferences,
                    sound: selectedSound,
                    notificationStores: subscribedStoreIds,
                },
            });

            Toast.show({
                type: "success",
                text1: "Settings Saved",
                text2: "Your notification preferences have been updated",
            });

            return result;
        } catch (error) {
            Toast.show({
                type: "error",
                text1: "Error",
                text2: "Failed to save notification settings",
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // Initialize selected sound
        setSelectedSound(user?.preferences?.sound || "chaching");

        // Initialize store subscriptions from user preferences
        const initialSubscriptions = {};
        const initialSubscribedIds = user?.preferences?.notificationStores || [];
        setSubscribedStoreIds(initialSubscribedIds);

        stores.forEach((store) => {
            initialSubscriptions[store._id] = initialSubscribedIds.includes(store._id);
        });
        setStoreSubscriptions(initialSubscriptions);

        setLoading(false);
    }, []);

    // Update subscribedStoreIds when storeSubscriptions change
    useEffect(() => {
        const currentSubscribedIds = Object.entries(storeSubscriptions)
            .filter(([_, isSubscribed]) => isSubscribed)
            .map(([storeId]) => storeId);

        setSubscribedStoreIds(currentSubscribedIds);
    }, [storeSubscriptions]);

    const toggleStoreSubscription = (storeId) => {
        setStoreSubscriptions((prev) => ({
            ...prev,
            [storeId]: !prev[storeId],
        }));
    };

    const updateSound = (sound) => {
        setSelectedSound(sound);
    };

    // Check if device is registered for notifications
    useEffect(() => {
        if (useAuthStore.getState().auth === "auth") {
            const checkDeviceRegistration = async () => {
                const registered = await isDeviceTokenRegistered();
                setIsDeviceRegistered(registered);
            };
            checkDeviceRegistration();
        }
    }, [auth]);

    if (auth === "loading") {
        return null;
    }

    return (
        <ScrollView style={styles.container}>
            {useAuthStore.getState().auth === "auth" && isDeviceRegistered === false && (
                <View
                    style={{
                        backgroundColor: colors.red[100],
                        padding: 12,
                        borderRadius: 8,
                        marginBottom: 16,
                        flexDirection: "row",
                        alignItems: "center",
                    }}
                >
                    <View
                        style={{
                            width: 8,
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: colors.red[500],
                            marginRight: 8,
                        }}
                    />
                    <Text style={[typography.sm, { color: colors.red[700] }]}>
                        This device is not set up for notifications
                    </Text>
                </View>
            )}

            {stores && stores.length > 1 && (
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>Store Notifications</Text>
                    <Text style={styles.sectionSubtitle}>Select stores to receive notifications from</Text>
                    <View style={styles.card}>
                        {stores.map((store, index) => (
                            <React.Fragment key={`${store._id}-${index}`}>
                                <StoreSubscriptionItem
                                    store={store}
                                    subscribed={storeSubscriptions[store._id]}
                                    onToggle={() => toggleStoreSubscription(store._id)}
                                />
                                {index < stores.length - 1 && <Divider color={colors.gray[200]} />}
                            </React.Fragment>
                        ))}
                    </View>
                </View>
            )}

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Notification Sound</Text>
                <View style={styles.card}>
                    <SettingItem
                        title="Chaching 💰"
                        onPress={() => updateSound("chaching")}
                        selected={selectedSound === "chaching"}
                    />
                    <Divider color={colors.gray[200]} />
                    <SettingItem
                        title="Ahmed Mohsen 🐢"
                        onPress={() => updateSound("ahmed_mohsen")}
                        selected={selectedSound === "ahmed_mohsen"}
                    />
                    <Divider color={colors.gray[200]} />
                    <SettingItem
                        title="Siuu! CR7"
                        onPress={() => updateSound("siuu")}
                        selected={selectedSound === "siuu"}
                    />
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.gray[50],
        padding: 16,
    },
    section: {
        marginBottom: 24,
    },
    sectionTitle: {
        color: colors.primary[700],
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: typography.md.fontSize,
        marginBottom: 8,
        textTransform: "capitalize",
    },
    sectionSubtitle: {
        color: colors.gray[600],
        fontFamily: typography.fontNormal.fontFamily,
        fontSize: typography.sm.fontSize,
        marginBottom: 12,
    },
    card: {
        backgroundColor: colors.white,
        borderRadius: 12,
        paddingHorizontal: 16,
        shadowColor: colors.gray[900],
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 2,
    },
    storeScrollContainer: {
        maxHeight: 240, // This will allow approximately 4 stores to show before scrolling
    },
    settingItem: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingVertical: 12,
    },
    settingItemText: {
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.gray[900],
        fontSize: typography.md.fontSize,
    },
    radioButton: {
        borderRadius: 100,
        borderWidth: 1,
        borderColor: colors.gray[400],
        height: 20,
        width: 20,
    },
    storeItem: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingVertical: 12,
    },
    storeInfo: {
        flexDirection: "row",
        alignItems: "center",
    },
    storeIcon: {
        width: 36,
        height: 36,
        borderRadius: 18,
        justifyContent: "center",
        alignItems: "center",
        marginRight: 12,
        backgroundColor: colors.white,
        borderWidth: 1,
        borderColor: colors.gray[300],
        overflow: "hidden",
    },
    storeInitial: {
        color: colors.gray[800],
        fontFamily: typography.fontBold.fontFamily,
        fontSize: typography.md.fontSize,
    },
    storeName: {
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.gray[900],
        fontSize: typography.md.fontSize,
    },
    storeLogo: {
        width: 35,
        height: 35,
        borderRadius: 18,
    },
});

export default NotificationSettings;
