import { StyleSheet } from "react-native";
import React from "react";
import { Stack, useLocalSearchParams } from "expo-router";
import ModalHeader from "@components/Navigation/ModalHeader";
import colors from "@styles/colors";

const _layout = () => {
    const routerParams = useLocalSearchParams();
    return (
        <Stack
            screenOptions={{
                // ! animation and presentation doesnt work
                header: ({ navigation, options, route, back }) => (
                    <ModalHeader navigation={navigation} options={options} route={route} back={back} />
                ),
                contentStyle: {
                    backgroundColor: colors.gray[50],
                },
            }}
        >
            <Stack.Screen
                name="notificationSettings/index"
                options={{
                    animation: "slide_from_bottom",
                    headerTitle: "Notification Settings",
                    header: ({ navigation, options, route, back }) => (
                        <ModalHeader
                            navigation={navigation}
                            options={options}
                            route={route}
                            back={back}
                            modalButtonLabel="Save"
                            modalButtonAction={() => navigation.setParams({ saveChanges: "true" })}
                        />
                    ),
                }}
            />
        </Stack>
    );
};

export default _layout;

const styles = StyleSheet.create({});
