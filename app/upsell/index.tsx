import React, { useLayoutEffect } from "react";
import { View, Text, ActivityIndicator, FlatList, RefreshControl } from "react-native";
import useUpsellStore from "../../store/upsellStore";
import colors from "@styles/colors";
import UpsellCard from "../../components/cards/upsell/UpsellCard";

const UpsellPage = () => {
    const { fetchUpsellItems, upsellItems, loading } = useUpsellStore();
    const [refreshing, setRefreshing] = React.useState(false);

    const refresh = () => {
        setRefreshing(true);
        fetchUpsellItems();
        setRefreshing(false);
    };

    useLayoutEffect(() => {
        refresh();
    }, []);

    if (loading) {
        return (
            <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                <ActivityIndicator size="large" color={colors.blue[500]} />
            </View>
        );
    }

    return (
        <FlatList
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={refresh}></RefreshControl>}
            contentContainerStyle={{ gap: 10, paddingVertical: 10, paddingHorizontal: 15 }}
            data={upsellItems}
            keyExtractor={({ _id }) => {
                return _id;
            }}
            renderItem={({ item }) => <UpsellCard upsell={item} />}
        />
    );
};

export default UpsellPage;
