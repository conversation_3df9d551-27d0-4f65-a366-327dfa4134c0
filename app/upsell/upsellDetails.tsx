import React, { Dispatch, SetStateAction, useLayoutEffect, useState } from "react";
import { Alert, Image, ScrollView, StyleSheet, Text, TouchableOpacity, View, Switch } from "react-native";
import { useLocalSearchParams } from "expo-router";
import { useNavigation } from "@react-navigation/native";
import useUpsellStore from "../../store/upsellStore";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import Input from "@components/inputs/textInputs/Input";
import { TabsContentCellDelete } from "@components/icons/BudgetManagerIcons";
import { useOrderStore } from "../../store/orders";
import ModalHeader from "@components/Navigation/ModalHeader";
import SelectModal from "@components/inputs/select/SelectModal";
import { CartItem } from "@components/orders/types";
import AddProductModal from "@components/Navigation/ModalView/AddProductModal";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import Divider from "@components/dividers/Divider";

type Product = {
    _id: string;
    name: string;
    price: number;
    images: {
        sm: string;
    }[];
};

type StatusProps = { title: string; onPress: () => void; selected?: boolean };

const StatusItem = ({ title, onPress, selected }: StatusProps) => (
    <TouchableOpacity onPress={onPress} style={styles.statusItem}>
        <Text style={styles.statusText}>{title}</Text>
        <View style={[styles.statusCircle, selected && styles.statusCircleSelected]} />
    </TouchableOpacity>
);

const UpsellProducts = ({ cart, onDelete }: { cart: any[]; onDelete: (productID: string) => void }) => (
    <View style={{ gap: 10 }}>
        {cart.map((product: any, index) => (
            <View key={index} style={styles.productContainer}>
                <Image source={{ uri: product?.images[0].sm }} style={styles.productImage} />
                <View style={styles.productInfo}>
                    <Text style={styles.productName}>{product.name}</Text>
                </View>
                <TouchableOpacity onPress={() => onDelete(product._id)} style={styles.deleteButton}>
                    <TabsContentCellDelete />
                </TouchableOpacity>
            </View>
        ))}
    </View>
);
const handleToggle = (isToggled: boolean) => {
    if (isToggled) {
        Alert.alert("Swiped", "The button has been swiped successfully!");
    } else {
        Alert.alert("Reset", "The button has been reset!");
    }
};

const UpsellOfferProducts = ({
    cart,
    onDelete,
    setUpsell,
}: {
    cart: Product[];
    onDelete: (productID: string) => void;
    setUpsell: Dispatch<SetStateAction<any>>;
}) => {
    return (
        <View style={{ gap: 10 }}>
            {cart.map((product: any, index) => (
                <View key={index} style={styles.productContainer2}>
                    <View style={{ flexDirection: "row", alignItems: "center" }}>
                        <Image source={{ uri: product?.images[0].sm }} style={styles.productImage2} />

                        <View style={{ flex: 1 }}>
                            <Text style={styles.productName2}>{product.name}</Text>
                        </View>

                        <TouchableOpacity onPress={() => onDelete(product._id)} style={styles.deleteButton2}>
                            <TabsContentCellDelete />
                        </TouchableOpacity>
                    </View>

                    <View style={{ paddingTop: 20 }}>
                        <Input
                            inputProps={{
                                defaultValue: product.price.toString(),
                            }}
                            label="Price"
                            onChange={(value) => {
                                setUpsell((prev) => ({
                                    ...prev,
                                    offer: prev.offer.map((item) =>
                                        item.product === product._id
                                            ? {
                                                  ...item,
                                                  price: Number.parseFloat(value),
                                              }
                                            : null
                                    ),
                                }));
                            }}
                            placeholder={"Price"}
                        />

                        {product.quantity && (
                            <Input
                                label="Quantity"
                                onChange={(value) => {
                                    setUpsell((prev) => ({
                                        ...prev,
                                        offer: prev.offer.map((item) =>
                                            item.product === product._id
                                                ? {
                                                      ...item,
                                                      quantity: Number(value),
                                                  }
                                                : null
                                        ),
                                    }));
                                }}
                                placeholder={"Quantity"}
                                inputProps={{
                                    defaultValue: product.quantity.toString(),
                                }}
                            />
                        )}
                    </View>
                </View>
            ))}
        </View>
    );
};

const UpsellDetails = () => {
    const navigation = useNavigation();
    const { upsellID } = useLocalSearchParams();
    const { upsellItems, updateUpsell } = useUpsellStore();
    const { products, getProducts } = useOrderStore();
    const [upsell, setUpsell] = useState(() => upsellItems.find((item) => item._id === upsellID));
    const [status, setStatus] = useState(upsell?.status);

    useLayoutEffect(() => {
        if (upsell) {
            navigation.setOptions({
                contentStyle: { backgroundColor: colors.gray[50] },
                headerTitle: upsell.name,
                header: ({ navigation, options, route, back }) => {
                    return (
                        <ModalHeader
                            navigation={navigation}
                            options={options}
                            route={route}
                            back={back}
                            modalButtonAction={() => {
                                updateUpsell({ ...upsell });
                            }}
                            backIcon
                            modalButtonLabel={"Save"}
                        />
                    );
                },
            });
        }
        getProducts();
    }, [navigation, upsell]);

    if (!upsell) {
        return <Text>No upsell data available</Text>;
    }

    const handleStatusChange = (newStatus) => {
        setStatus(newStatus);
        setUpsell((prev) => ({ ...prev, status: newStatus }));
    };
    const toggleSwitch = () => {
        setStatus((prevStatus) => (prevStatus === "shown" ? "hidden" : "shown"));
    };

    const findProductsInOfferArray = (offer: any) => {
        return offer
            .map((item) => {
                const product = products.find((product) => product._id === item.product);
                return product ? { ...product, quantity: item.quantity, price: item.price } : null;
            })
            .filter(Boolean);
    };

    const handleDeleteProductFromProducts = (productId) => {
        setUpsell((prev) => ({
            ...prev,
            products: prev.products.filter((item: any) => item._id !== productId),
        }));
    };

    const handleDeleteProductFromOffer = (productId) => {
        setUpsell((prev) => ({
            ...prev,
            offer: prev.offer.filter((item) => item.product !== productId),
        }));
    };

    const [addModalVisible, setAddModalVisible] = useState(false);
    const [addMOdalVisibleTwo, setAddModalVisibleTwo] = useState(false);

    return (
        <ScrollView contentContainerStyle={styles.container} automaticallyAdjustKeyboardInsets>
            <View style={styles.element}>
                <Input
                    label="Upsell/CrossSell Name"
                    inputProps={{ defaultValue: upsell?.name }}
                    onChange={(value) => setUpsell({ ...upsell, name: value })}
                    placeholder="Upsell/CrossSell Name"
                />
            </View>

            <View style={styles.switchContainer}>
                <Text style={styles?.label}>Status:{status === "shown" ? "Shown" : "Hidden"}</Text>
                <Switch
                    value={status === "shown"}
                    onValueChange={toggleSwitch}
                    trackColor={{ false: colors.gray[300], true: colors.primary[500] }}
                    thumbColor={status === "shown" ? "white" : "white"}
                />
                {/* <View style={styles.statusContainer}>
                    <StatusItem
                        title="Shown"
                        onPress={() => handleStatusChange("shown")}
                        selected={status === "shown"}
                    />
                    <Divider color={colors.gray[200]} />
                    <StatusItem
                        title="Hidden"
                        onPress={() => handleStatusChange("hidden")}
                        selected={status === "hidden"}
                    />
                </View> */}
            </View>
            <View style={styles.element}>
                <Input
                    label="Priority"
                    inputProps={{ defaultValue: upsell?.priority.toString() }}
                    onChange={(value) => setUpsell({ ...upsell, priority: Number(value) })}
                    placeholder="Priority"
                />
            </View>
            <View style={styles.element}>
                <Text style={styles?.label}>Base Product:</Text>

                <AddProductModal
                    visible={addModalVisible}
                    setVisible={setAddModalVisible}
                    action={(value) => {
                        setUpsell((prev) => ({
                            ...prev,
                            products: [...prev.products, value as unknown as CartItem],
                        }));
                    }}
                />
            </View>
            <UpsellProducts cart={upsell.products} onDelete={handleDeleteProductFromProducts} />
            <TextButton
                variant="outlined"
                label="Add Product"
                onPress={() => {
                    setAddModalVisible(true);
                }}
            />
            <Divider />
            <View style={styles.element}>
                <Text style={styles?.label}> Discount on:</Text>

                <AddProductModal
                    visible={addMOdalVisibleTwo}
                    setVisible={setAddModalVisibleTwo}
                    action={(value) => {
                        setUpsell((prev) => ({
                            ...prev,
                            offer: [
                                ...prev.offer,
                                {
                                    product: value._id,
                                    quantity: 1,
                                    price: value.price - value.price * 0.2,
                                },
                            ],
                        }));
                    }}
                />
            </View>
            <UpsellOfferProducts
                cart={findProductsInOfferArray(upsell.offer)}
                onDelete={handleDeleteProductFromOffer}
                setUpsell={setUpsell}
            />
            <TextButton
                variant="outlined"
                label="Add Product"
                onPress={() => {
                    setAddModalVisibleTwo(true);
                }}
            />
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        // borderRadius: 13,
        paddingHorizontal: 15,
        paddingBottom: 100,
        gap: 10,
    },

    label: {
        fontSize: 14,
        fontWeight: "bold",
        color: colors.gray[800],
    },
    element: {
        width: "100%",
        gap: 5,
        borderRadius: 10,
        paddingVertical: 5,
    },
    statusContainer: {
        padding: 10,
        alignItems: "stretch",
        gap: 8,
        borderRadius: 10,
        borderWidth: 1,
        borderColor: colors.gray[200],
    },
    statusItem: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingRight: 10,
    },
    statusText: {
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.gray[900],
        fontSize: 12,
    },
    statusCircle: {
        borderRadius: 100,
        borderWidth: 1,
        borderColor: colors.gray[400],
        backgroundColor: colors.gray[50],
        height: 16,
        width: 16,
    },
    statusCircleSelected: {
        backgroundColor: colors.blue[500],
    },
    productContainer: {
        flexDirection: "row",
        alignItems: "center",
        borderWidth: 0.5,
        borderColor: colors.gray[300],
        borderRadius: 10,
        // marginVertical: 5,
        paddingVertical: 10,
        paddingHorizontal: 10,
        backgroundColor: colors.white,
    },
    productImage: {
        width: 100,
        height: 100,
        borderRadius: 10,

        // paddingRight: 10,
        // paddingLeft: 20,
    },

    productName: {
        textAlign: "center",
        fontSize: 16,
        fontWeight: "bold",
        width: "90%",
        color: colors.black,
        paddingLeft: 20,
    },
    deleteButton: {
        opacity: 0.8,
        // borderRadius: 100,
        backgroundColor: "transparent",
        // borderWidth: 0.5,
        borderColor: colors.gray[300],
        // padding: 5,
    },
    productInfo: {
        flex: 1,
        flexDirection: "column",
        width: "100%",
        // alignItems: "center",
    },
    productContainer2: {
        // paddingBottom: 20,
        padding: 10,
        borderColor: colors.gray[300],
        borderWidth: 1,
        borderRadius: 8,
    },
    productImage2: {
        width: 60,
        height: 60,
        borderRadius: 8,
        marginRight: 10,
    },
    productName2: {
        fontSize: 16,
        fontWeight: "bold",
        color: colors.black,
    },
    deleteButton2: {
        padding: 10,
        justifyContent: "center",
        alignItems: "center",
    },
    clickableArea: {
        height: 45,
        width: 330,
        justifyContent: "center",
        alignItems: "center",
    },
    switchContainer: {
        flexDirection: "row",
        alignItems: "center",
        // padding: 16,
        gap: 20,
    },
});

export default UpsellDetails;
