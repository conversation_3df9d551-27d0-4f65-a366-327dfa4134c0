import React, { useEffect, useState } from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import colors from "@styles/colors";
import Input from "@components/inputs/textInputs/Input";
import { useStoreStore } from "../../store/storeStore";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import Svg, { Path } from "react-native-svg";
import api from "@api/api";
import Toast from "react-native-toast-message";
import { useProductStore } from "../../store/productStore";
import { router } from "expo-router";

const SaveIcon = () => {
    return (
        <View>
            <Svg width="15" height="12" viewBox="0 0 15 12" fill="none">
                <Path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M7.50016 0C7.86835 0 8.16683 0.298477 8.16683 0.666667V5.72386L9.02876 4.86193C9.28911 4.60158 9.71122 4.60158 9.97157 4.86193C10.2319 5.12228 10.2319 5.54439 9.97157 5.80474L7.97204 7.80426C7.97044 7.80587 7.96882 7.80748 7.9672 7.80907C7.84735 7.92673 7.68325 7.99947 7.50216 8C7.5015 8 7.50083 8 7.50016 8C7.4995 8 7.49883 8 7.49816 8C7.4085 7.99973 7.323 7.98177 7.24497 7.94941C7.16813 7.91762 7.09603 7.87084 7.03313 7.80907C7.03151 7.80748 7.02989 7.80587 7.02828 7.80426L5.02876 5.80474C4.76841 5.54439 4.76841 5.12228 5.02876 4.86193C5.28911 4.60158 5.71122 4.60158 5.97157 4.86193L6.8335 5.72386V0.666667C6.8335 0.298477 7.13197 0 7.50016 0ZM1.41928 2.58579C1.79436 2.21071 2.30306 2 2.8335 2H4.8335C5.20169 2 5.50016 2.29848 5.50016 2.66667C5.50016 3.03486 5.20169 3.33333 4.8335 3.33333H2.8335C2.65668 3.33333 2.48712 3.40357 2.36209 3.5286C2.23707 3.65362 2.16683 3.82319 2.16683 4V10C2.16683 10.1768 2.23707 10.3464 2.36209 10.4714C2.48712 10.5964 2.65669 10.6667 2.8335 10.6667H12.1668C12.3436 10.6667 12.5132 10.5964 12.6382 10.4714C12.7633 10.3464 12.8335 10.1768 12.8335 10V4C12.8335 3.82319 12.7633 3.65362 12.6382 3.5286C12.5132 3.40357 12.3436 3.33333 12.1668 3.33333H10.1668C9.79864 3.33333 9.50016 3.03486 9.50016 2.66667C9.50016 2.29848 9.79864 2 10.1668 2H12.1668C12.6973 2 13.206 2.21071 13.581 2.58579C13.9561 2.96086 14.1668 3.46957 14.1668 4V10C14.1668 10.5304 13.9561 11.0391 13.581 11.4142C13.206 11.7893 12.6973 12 12.1668 12H2.8335C2.30306 12 1.79436 11.7893 1.41928 11.4142C1.04421 11.0391 0.833496 10.5304 0.833496 10V4C0.833496 3.46957 1.04421 2.96086 1.41928 2.58579Z"
                    fill={colors.white}
                />
            </Svg>
        </View>
    );
};

const Settings = () => {
    const { store, setStore } = useStoreStore();
    const { products, getProducts } = useProductStore();

    const [preferences, setPreferences] = useState(store?.preferences || {});

    useEffect(() => {
        getProducts();
    }, []);

    const handleSubmit = () => {
        // * Safety check
        if (preferences?.maxOrderAmount) {
            const maxOrderAmount = preferences.maxOrderAmount;
            const product = products.find(
                (product) =>
                    product.price > maxOrderAmount && product.status !== "hidden" && product.status !== "outOfStock"
            );

            if (product) {
                router.navigate({
                    pathname: "/network-error",
                    params: {
                        message: `Product ${product.name} exceeds the maximum order amount!`,
                    },
                });
                return;
            }
        }

        api.patch("/store", {
            ...store,
            preferences,
        })
            .then((response) => {
                Toast.show({
                    type: "success",
                    text1: "Success",
                    text2: "Settings saved successfully",
                });
                setStore(response.data);
            })
            .catch((error) =>
                router.navigate({
                    pathname: "/network-error",
                    params: error,
                })
            );
    };

    return (
        <ScrollView contentContainerStyle={styles.container}>
            <View style={styles.profileContainer}>
                <Text style={styles.sectionTitle}>Cart Settings</Text>
                <View style={{ gap: 5 }}>
                    <Input
                        label="Maximum Order Quantity (per item)"
                        placeholder="Orders exceeding this quantity will be deleted"
                        inputProps={{
                            value: preferences?.maxOrderQuantity ? String(preferences?.maxOrderQuantity) : "",
                            inputMode: "decimal",
                        }}
                        onChange={(value) =>
                            setPreferences({ ...preferences, maxOrderQuantity: value ? Number(value) : null })
                        }
                    />
                </View>
                <View style={{ gap: 5 }}>
                    <Input
                        label="Maximum Order Amount"
                        placeholder="Orders that exceed this amount will be deleted"
                        inputProps={{
                            value: preferences?.maxOrderAmount ? String(preferences?.maxOrderAmount) : "",
                            inputMode: "decimal",
                        }}
                        onChange={(value) =>
                            setPreferences({ ...preferences, maxOrderAmount: value ? Number(value) : null })
                        }
                    />
                </View>
                <View style={{ gap: 10, paddingBottom: 10, paddingTop: 10 }}>
                    <TextButton
                        label="Save Changes"
                        onPress={handleSubmit}
                        leftIcon={<SaveIcon />}
                        color={colors.blue[500]}
                    />
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    label: {
        fontSize: 12,
        fontWeight: "bold",
        color: colors.gray[700],
    },
    container: {
        gap: 25,
        paddingVertical: 5,
        paddingHorizontal: 10,
    },
    profileContainer: {
        borderColor: colors.gray[200],
        backgroundColor: colors.white,
        borderRadius: 13.06,
        paddingTop: 20,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 1,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: "bold",
        textAlign: "center",
        color: colors.primary[500],
        paddingVertical: 10,
    },
    logoutButton: {
        width: "90%",
        alignSelf: "center",
    },
});

export default Settings;
