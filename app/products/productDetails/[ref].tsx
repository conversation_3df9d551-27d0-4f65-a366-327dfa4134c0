import React, { useEffect, useState } from "react";
import { Stack } from "expo-router";
import { Dimensions, Text, View, Image as Img } from "react-native";
import { useProductStore } from "../../../store/productStore";
import NormalSheetCard, {
    BrokenIcon,
    CompareIcon,
    ShopIcon,
    TruckIcon,
} from "@components/buttomSheetCard/NormalSheetCard";
import { GestureHandlerRootView, ScrollView } from "react-native-gesture-handler";
import { flattenCombination } from "@components/productDetails/utils";
import {
    Combinations,
    CombinationsSection,
    DescriptionSection,
    OptionSection,
} from "@components/productDetails/components";
import { styles } from "@components/productDetails/styles";

const ProductDetails = () => {
    const { selectedProduct } = useProductStore();
    const width = Dimensions.get("window").width;
    const imageWidth = width / 1.4;
    const [combinations, setCombinations] = useState<Combinations>([]);

    useEffect(() => {
        let _result: Combinations = [];
        let _currentPath: string[] = [];
        let _combinations = selectedProduct.combinations;

        if (selectedProduct.combinations) {
            flattenCombination(_combinations, _currentPath, _result);
            setCombinations(_result);
        }
    }, []);

    return (
        <GestureHandlerRootView>
            <ScrollView style={styles.scrollView} contentContainerStyle={{ minHeight: "100%" }}>
                <Stack.Screen options={{ headerTitle: "Details" }} />

                <View style={styles.container}>
                    <View style={styles.header}>
                        <View style={styles.headerText}>
                            <Text style={styles.nameText}>{selectedProduct?.name}</Text>
                            <Text style={styles.refText}>#{selectedProduct?.reference}</Text>
                        </View>
                    </View>
                    <View>
                        <ScrollView
                            horizontal
                            showsHorizontalScrollIndicator={false}
                            contentContainerStyle={styles.imageScrollContainer}
                        >
                            {selectedProduct?.images.map((item, index) => (
                                <View
                                    key={index}
                                    style={{
                                        width: imageWidth,

                                        justifyContent: "space-between",
                                    }}
                                >
                                    <Img source={{ uri: item.lg }} style={styles.carouselImage} />
                                </View>
                            ))}
                        </ScrollView>
                    </View>

                    <View style={{ gap: 10, paddingHorizontal: 10 }}>
                        <View style={{ gap: 10 }}>
                            <NormalSheetCard
                                variant="primary"
                                icon={ShopIcon}
                                label="Price"
                                value={selectedProduct?.price}
                                style={{
                                    flex: 1,
                                    paddingHorizontal: 15,
                                    backgroundColor: "white",

                                    elevation: 2,
                                }}
                            />
                            <NormalSheetCard
                                variant="primary"
                                icon={CompareIcon}
                                label="Compare Price"
                                value={selectedProduct?.comparePrice}
                                style={{
                                    flex: 1,
                                    paddingHorizontal: 15,
                                    backgroundColor: "white",

                                    elevation: 2,
                                }}
                            />
                            <NormalSheetCard
                                variant="primary"
                                icon={TruckIcon}
                                label="Delivery Price"
                                value={selectedProduct?.deliveryPrice}
                                style={{
                                    flex: 1,
                                    paddingHorizontal: 15,
                                    backgroundColor: "white",

                                    elevation: 2,
                                }}
                            />

                            <NormalSheetCard
                                variant="primary"
                                icon={BrokenIcon}
                                label={selectedProduct.stock > 0 ? "In Stock" : "No Stock"}
                                value={selectedProduct.stock > 0 ? selectedProduct.stock : 0}
                                style={{
                                    flex: 1,
                                    paddingHorizontal: 15,
                                    backgroundColor: "white",

                                    elevation: 2,
                                }}
                                valueUnit={"Units"}
                            />
                        </View>

                        {selectedProduct?.variants && selectedProduct.variants.length !== 0 && (
                            <OptionSection variants={selectedProduct.variants} />
                        )}
                    </View>

                    <CombinationsSection combinations={combinations} />

                    <View style={{ gap: 10, paddingHorizontal: 10, height: "100%" }}>
                        <DescriptionSection />
                    </View>
                </View>
            </ScrollView>
        </GestureHandlerRootView>
    );
};

export default ProductDetails;
