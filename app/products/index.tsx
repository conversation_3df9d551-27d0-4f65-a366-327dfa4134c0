import api from "@api/api";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { ActivityIndicator, FlatList, RefreshControl, Text, View } from "react-native";
import BigSheetCard from "@components/buttomSheetCard/BigSheetCard";
import BottomSheetCard from "@components/buttomSheetCard/BottomSheetCard";
import ProductCard from "@components/cards/products/ProductCard";
import { MoneyIcon } from "@components/icons/StatsIcons";
import FAB from "@components/inputs/buttons/FloatingActionButtonProducts";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import colors from "@styles/colors";
import { Slot, useRouter } from "expo-router";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { G, Mask, Path, Rect, Svg } from "react-native-svg";
import { useProductStore } from "../../store/productStore";
import { Product } from "../../types/Product";

type ResProp = { data: Product[] };

const Products = () => {
    const { products, setProducts, updateProductsList } = useProductStore();
    const [page, setPage] = useState(1);
    const [loading, setLoading] = useState(true);
    const [more, setMore] = useState(false);
    const { productStatistics, setProductStatistics } = useProductStore();
    const router = useRouter();

    const bottomSheetRef = useRef<BottomSheet>(null);
    const handleSheetChanges = useCallback((index: number) => {}, []);

    useEffect(() => {
        api.get("/product")
            .then((res: any) => {
                setProducts(res.data);
            })
            .catch((e) => {
                router.navigate("/network-error");
            })
            .finally(() => {
                setLoading(false);
                setMore(false);
            });
        api.get("/statistics/products")
            .then((res: any) => {
                setProductStatistics(res.data);
            })
            .catch((e) => {
                router.navigate("/network-error");
            });
    }, []);

    const handleFetchMore = () => {
        if (loading || more || products.length % 10 !== 0) return;
        setMore(true);
        setPage(page + 1);

        api.get(`/product?page=${page + 1}`)
            .then((res: ResProp) => updateProductsList(res.data))
            .catch(() => {
                router.navigate("/network-error");
            })
            .finally(() => {
                setLoading(false);
                setMore(false);
            });
    };

    if (loading) {
        return (
            <View style={{ height: "80%", justifyContent: "center" }}>
                <ActivityIndicator color={colors.primary[400]} size="large" />
            </View>
        );
    }

    if (products.length === 0) {
        return (
            <View
                style={{
                    width: "100%",
                    height: "80%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                }}
            >
                <Text style={{ fontWeight: "bold" }}>No products!</Text>
            </View>
        );
    }

    return (
        <GestureHandlerRootView>
            <FlatList
                contentContainerStyle={{ paddingLeft: 10, paddingRight: 10, gap: 10, paddingBottom: 120 }}
                refreshControl={<RefreshControl refreshing={more}></RefreshControl>}
                data={products}
                keyExtractor={({ _id }) => {
                    return _id;
                }}
                renderItem={({ item }) => <ProductCard product={item} />}
                onEndReached={handleFetchMore}
                onEndReachedThreshold={0.3}
                ListFooterComponent={
                    <View
                        style={{
                            paddingVertical: 20,
                            alignItems: "center",
                            justifyContent: "center",
                            paddingBottom: 40,
                        }}
                    >
                        {!more && (
                            <Svg height="16px" viewBox="0 -960 960 960" width="16px" fill={colors.gray[300]}>
                                <Path d="M480-200q-117 0-198.5-81.5T200-480q0-117 81.5-198.5T480-760q117 0 198.5 81.5T760-480q0 117-81.5 198.5T480-200Z" />
                            </Svg>
                        )}
                    </View>
                }
            />
            {/* <Slot /> */}

            {/* <FAB
                action={() => {
                    router.navigate("/createProduct");
                }}
            /> */}

            <BottomSheet
                style={[
                    {
                        borderColor: colors.gray[100],
                        borderRadius: 15,
                        backgroundColor: "white",
                        borderWidth: 1,
                        elevation: 1,
                        // paddingHorizontal: 5,
                    },
                    // isKeyboardVisible && { display: 'none' },
                ]}
                snapPoints={[150, 260]}
                ref={bottomSheetRef}
                onChange={handleSheetChanges}
                enableOverDrag={false}
            >
                <BottomSheetView style={{ gap: 5, paddingHorizontal: 5 }}>
                    <BottomSheetView style={{ flexDirection: "row", gap: 5, width: "100%" }}>
                        <BottomSheetCard
                            MainIcon={BrokenPackageIcon}
                            SecondaryIcon={MoneyIcon}
                            mainValue={0}
                            variant="primary"
                            mainValueUnit="Units"
                            mainValueLabel="In Stock"
                            secondaryValueLabel="Value"
                            secondaryValue={productStatistics?.stockValue}
                            soft
                            style={{ flex: 1 }}
                        />

                        <BottomSheetCard
                            MainIcon={PackageIcon}
                            SecondaryIcon={MoneyIcon}
                            mainValue={0}
                            variant="red"
                            mainValueUnit="Units"
                            mainValueLabel="Damaged"
                            secondaryValueLabel="Value"
                            secondaryValue={productStatistics?.totalStock}
                            soft
                            style={{ flex: 1 }}
                        />
                    </BottomSheetView>
                    <BigSheetCard
                        MainIcon={MoneyIconBig}
                        mainValue={productStatistics?.totalRevenue}
                        variant="green"
                        mainValueLabel="Estimated Revenue"
                        style={{ paddingHorizontal: 30 }}
                        mainValueUnit="TND"
                    />
                </BottomSheetView>
            </BottomSheet>
        </GestureHandlerRootView>
    );
};

export const BrokenPackageIcon = () => {
    return (
        <Svg width="24" height="24" viewBox="0 0 24 24">
            <Mask id="mask0_10952_14744" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                <Rect width="24" height="24" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_10952_14744)">
                <Path
                    d="M16.65 12.9992L11 7.34922L16.65 1.69922L22.3 7.34922L16.65 12.9992ZM3 10.9992V2.99922H11V10.9992H3ZM13 20.9992V12.9992H21V20.9992H13ZM3 20.9992V12.9992H11V20.9992H3ZM5 8.99922H9V4.99922H5V8.99922ZM16.675 10.1992L19.5 7.37422L16.675 4.54922L13.85 7.37422L16.675 10.1992ZM15 18.9992H19V14.9992H15V18.9992ZM5 18.9992H9V14.9992H5V18.9992Z"
                    fill="white"
                />
            </G>
        </Svg>
    );
};
export const PackageIcon = () => {
    return (
        <Svg width="21" height="21" viewBox="0 0 21 21" fill="none">
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.5 10.575V17.425L2.5 13.95V7.1L8.5 10.575ZM12.8896 16.041L10.5 17.425V10.575L16.5 7.1V11.65H18.5V6.025C18.5 5.65833 18.4125 5.325 18.2375 5.025C18.0625 4.725 17.8167 4.48333 17.5 4.3L10.5 0.275C10.1833 0.0916667 9.85 0 9.5 0C9.15 0 8.81667 0.0916667 8.5 0.275L1.5 4.3C1.18333 4.48333 0.9375 4.725 0.7625 5.025C0.5875 5.325 0.5 5.65833 0.5 6.025V13.975C0.5 14.3417 0.5875 14.675 0.7625 14.975C0.9375 15.275 1.18333 15.5167 1.5 15.7L8.5 19.725C8.81667 19.9083 9.15 20 9.5 20C9.85 20 10.1833 19.9083 10.5 19.725L17.5 15.7L12.4945 18.5741L12.8896 16.041ZM15.425 5.425L13.5 6.525L7.55 3.125L9.5 2L15.425 5.425ZM11.45 7.725L9.5 8.85L3.575 5.425L5.525 4.3L11.45 7.725Z"
                fill="white"
            />
            <Path d="M19.9755 13.9497L20.0278 12.9511L18.0531 12.7661L18.0013 13.7531L19.9755 13.9497Z" fill="white" />
            <Path
                d="M16.1443 12.6661L15.4964 14.6597L13.8446 15.3737L13.1286 18.4795L14.2615 17.9003L14.7144 16.0676L16.2652 15.384L16.7724 13.7108L18.0013 13.7531L18.0531 12.7661L16.1443 12.6661Z"
                fill="white"
            />
            <Path
                d="M19.5625 17.1644C19.7529 16.874 19.8577 16.5457 19.8769 16.1795L19.9337 15.0964L20.0278 12.9511L19.9755 13.9497L18.0013 13.7531L17.881 16.0499L14.2615 17.9003L13.8718 20.3438L18.788 17.8498C19.1138 17.6833 19.372 17.4548 19.5625 17.1644Z"
                fill="white"
            />
            <Path
                d="M12.6852 20.9458C12.6856 20.9994 13.8718 20.3438 13.8718 20.3438L14.2615 17.9003L13.1286 18.4795C13.1286 18.4795 12.6796 20.2295 12.6852 20.9458Z"
                fill="white"
            />
        </Svg>
    );
};
export const MoneyIconBig = () => {
    return (
        <Svg width="30" height="30" viewBox="0 0 24 24" fill="white">
            <Path d="M19 14V6C19 4.9 18.1 4 17 4H3C1.9 4 1 4.9 1 6V14C1 15.1 1.9 16 3 16H17C18.1 16 19 15.1 19 14ZM17 14H3V6H17V14ZM10 7C8.34 7 7 8.34 7 10C7 11.66 8.34 13 10 13C11.66 13 13 11.66 13 10C13 8.34 11.66 7 10 7ZM23 7V18C23 19.1 22.1 20 21 20H4V18H21V7H23Z" />
        </Svg>
    );
};

export default Products;
