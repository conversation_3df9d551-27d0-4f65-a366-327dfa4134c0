import React, { useState } from "react";
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Order } from "@components/orders/types";

interface Notification {
    id: number;
    message: string;
    additionalMessage: string;
    color: string;
    textColor: string;
}

const mockOrders: Order[] = [
    {
        _id: "1",
        reference: 12345,
        customer: { name: "<PERSON>" },
        cart: [],
        status: "pending",
        total: { basePrice: 100, totalPrice: 120 },
        deliveryCompany: "aramex",
        history: [],
        refunded: false,
    },
    {
        _id: "2",
        reference: 12346,
        customer: { name: "<PERSON>" },
        cart: [],
        status: "confirmed",
        total: { basePrice: 200, totalPrice: 220 },
        deliveryCompany: "intigo",
        history: [],
        refunded: false,
    },
    {
        _id: "3",
        reference: 12347,
        customer: { name: "<PERSON>" },
        cart: [],
        status: "returned",
        total: { basePrice: 150, totalPrice: 170 },
        deliveryCompany: "none",
        history: [],
        refunded: false,
    },
    {
        _id: "4",
        reference: 12348,
        customer: { name: "<PERSON>" },
        cart: [],
        status: "cancelled",
        total: { basePrice: 250, totalPrice: 270 },
        deliveryCompany: "mylerz",
        history: [],
        refunded: false,
    },
];

const Notifications: React.FC = () => {
    const [notifications, setNotifications] = useState<Notification[]>([
        {
            id: 1,
            message: `Order #${mockOrders[0].reference} Created`,
            additionalMessage: `Customer: ${mockOrders[0].customer.name}\nTotal: $${mockOrders[0].total.totalPrice}`,
            color: colors.green[100],
            textColor: colors.green[800],
        },
        {
            id: 2,
            message: `Order #${mockOrders[1].reference} Updated`,
            additionalMessage: `Customer: ${mockOrders[1].customer.name}\nTotal: $${mockOrders[1].total.totalPrice}`,
            color: colors.orange[100],
            textColor: colors.orange[800],
        },
        {
            id: 3,
            message: `Order #${mockOrders[2].reference} Deleted`,
            additionalMessage: `Customer: ${mockOrders[2].customer.name}\nTotal: $${mockOrders[2].total.totalPrice}`,
            color: colors.red[100],
            textColor: colors.red[800],
        },
        {
            id: 4,
            message: `Order #${mockOrders[3].reference} Restored`,
            additionalMessage: `Customer: ${mockOrders[3].customer.name}\nTotal: $${mockOrders[3].total.totalPrice}`,
            color: colors.blue[100],
            textColor: colors.blue[800],
        },
    ]);

    const [collapsed, setCollapsed] = useState<{ [key: number]: boolean }>({});

    const toggleNotification = (id: number) => {
        setCollapsed((prevCollapsed) => ({
            ...prevCollapsed,
            [id]: !prevCollapsed[id],
        }));
    };

    const renderNotification = ({ item }: { item: Notification }) => (
        <View style={[styles.notificationContainer, { backgroundColor: item.color }]}>
            <View style={styles.mainContent}>
                <View style={styles.iconContainer}>
                    <Text style={[styles.iconText, { color: item.textColor }]}>i</Text>
                </View>
                <Text style={[styles.message, { color: item.textColor }]}>{item.message}</Text>
                <TouchableOpacity style={styles.toggleButton} onPress={() => toggleNotification(item.id)}>
                    <Text style={[styles.toggleText, { color: item.textColor }]}>
                        {collapsed[item.id] ? "Show" : "Hide"}
                    </Text>
                </TouchableOpacity>
            </View>
            <View style={[styles.additionalMessage, collapsed[item.id] && { display: "none" }]}>
                <Text style={[styles.notificationText, { color: item.textColor }]}>{item.additionalMessage}</Text>
            </View>
        </View>
    );

    return (
        <FlatList
            data={notifications}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderNotification}
            contentContainerStyle={styles.listContainer}
        />
    );
};

const styles = StyleSheet.create({
    listContainer: {
        padding: 20,
        gap: 16,
    },
    notificationContainer: {
        padding: 20,
        borderRadius: 12,
        shadowColor: colors.gray[800],
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 5,
    },
    mainContent: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        gap: 7,
    },
    iconContainer: {
        width: 32,
        height: 32,
        borderRadius: 16,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: colors.white,
        shadowColor: colors.gray[800],
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 5,
    },
    iconText: {
        fontSize: typography.md.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    message: {
        flex: 1,
        fontSize: typography.md.fontSize,
        fontFamily: typography.fontSemibold.fontFamily,
    },
    toggleButton: {
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderRadius: 8,
        backgroundColor: colors.white,
        shadowColor: colors.gray[800],
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 5,
    },
    toggleText: {
        fontSize: typography.sm.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    additionalMessage: {
        padding: 16,
        borderRadius: 8,
        marginTop: 16,
        backgroundColor: colors.gray[50],
    },
    notificationText: {
        fontSize: typography.sm.fontSize,
        fontFamily: typography.fontNormal.fontFamily,
    },
});

export default Notifications;
