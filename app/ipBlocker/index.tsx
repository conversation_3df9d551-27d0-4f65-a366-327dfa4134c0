import api from "@api/api";
import Button from "@components/inputs/buttons/Button";
import Input from "@components/inputs/textInputs/Input";
import BlockedIPCard from "@components/ipBlocker/blockedIPCard";
import Header from "@components/Navigation/Header";
import Modal from "@components/Navigation/ModalView/ModalView";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Stack } from "expo-router";
import React, { useEffect, useState } from "react";
import { ActivityIndicator, FlatList, Pressable, RefreshControl, Text, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import RNPickerSelect from "react-native-picker-select";
import Svg, { Path } from "react-native-svg";
import Toast from "react-native-toast-message";

type IPItem = {
    ip: string;
    ttl: number;
    blockedAttempts?: number;
};

const IpBlocker = () => {
    const [blockedIPs, setBlockedIPs] = useState([]);
    const [loading, setLoading] = useState(true);
    const [modalStatus, setModalStatus] = useState(false);
    const [formValues, setFormValues] = useState<IPItem>({ ip: "", ttl: 3600 });

    const fetchBlockedIPs = async () => {
        try {
            const res = await api.get("/blocked-ips");
            setBlockedIPs(res.data);
        } catch (error) {
            console.error("Failed to fetch blocked IPs", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchBlockedIPs();
    }, []);

    const handleRefresh = () => {
        setLoading(true);
        fetchBlockedIPs();
    };

    const handleDelete = (ip: string) => {
        api.delete("/blocked-ips", { ip })
            .then((res) => {
                if (res.success) {
                    const newData = blockedIPs?.filter((item) => item.ip !== ip);
                    setBlockedIPs(newData);
                    setModalStatus(false);
                    Toast.show({
                        type: "success",
                        text1: "Success",
                        text2: res.message,
                    });
                }
                return;
            })
            .catch((err) =>
                Toast.show({
                    type: "error",
                    text1: "Error",
                    text2: err?.message,
                })
            );
    };

    const handleSubmit = (ip: IPItem) => {
        api.post("/blocked-ips", ip)
            .then((res) => {
                if (res.success) {
                    const existingIpIndex = blockedIPs.findIndex((item) => item.ip === ip.ip);
                    let newData;
                    if (existingIpIndex !== -1) {
                        newData = blockedIPs.map((item, index) => (index === existingIpIndex ? ip : item));
                    } else {
                        newData = [...blockedIPs, ip];
                    }
                    setBlockedIPs(newData);
                    Toast.show({
                        type: "success",
                        text1: "Success",
                        text2: res.message,
                    });
                    setModalStatus(false);
                }
            })
            .catch((err) =>
                Toast.show({
                    type: "error",
                    text1: "Error",
                    text2: err?.message,
                })
            );
    };

    return (
        <>
            <Stack.Screen
                options={{
                    headerTitle: "IP Blocker",
                    header: (props) => {
                        return (
                            <Header
                                {...props}
                                NoCap
                                modalButtonLabel="Block"
                                modalButtonAction={() => {
                                    setFormValues({ ip: "", ttl: 3600 });
                                    setModalStatus(true);
                                }}
                            />
                        );
                    },
                }}
            />
            {loading ? (
                <ActivityIndicator color={colors.red[400]} size="large" />
            ) : blockedIPs.length === 0 ? (
                <View
                    style={{
                        width: "100%",
                        height: "80%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    <Text style={[typography.fontBold]}>No blocked IPs.</Text>
                </View>
            ) : (
                <GestureHandlerRootView style={{ height: "100%" }}>
                    <FlatList
                        contentContainerStyle={{ paddingLeft: 10, paddingRight: 10, gap: 10, paddingBottom: 120 }}
                        refreshControl={<RefreshControl refreshing={loading} onRefresh={handleRefresh} />}
                        data={blockedIPs}
                        renderItem={({ item }) => (
                            <Pressable
                                onPress={() => {
                                    setFormValues({ ip: item.ip, ttl: item.ttl });
                                    setModalStatus(true);
                                }}
                            >
                                <BlockedIPCard IP={item.ip} blockedAttempts={item.blockedAttempts} ttl={item.ttl} />
                            </Pressable>
                        )}
                    />
                </GestureHandlerRootView>
            )}
            <Modal activeModal={modalStatus} setActiveModal={setModalStatus}>
                <View style={{ display: "flex", flexDirection: "row", width: "100%", gap: 10 }}>
                    <Input
                        onChange={(e) => setFormValues((prev) => ({ ...prev, ip: e }))}
                        label="IP to block"
                        placeholder="example: ************"
                        inputProps={{
                            inputMode: "decimal",
                            defaultValue: formValues.ip,
                        }}
                        containerStyle={{ width: "60%" }}
                    />
                    <View style={{ width: "35%" }}>
                        <Text
                            style={[
                                { color: colors.black, fontWeight: "bold", paddingBottom: 5 },
                                typography.baseText,
                                typography.xs,
                                typography.fontSemibold,
                                typography.lineHeight4,
                            ]}
                        >
                            TTL
                        </Text>
                        <RNPickerSelect
                            placeholder={{ label: "TTL", value: null, color: colors.gray[400] }}
                            onValueChange={(value) => setFormValues((prev) => ({ ...prev, ttl: value }))}
                            items={[
                                { label: "1 hour", value: 3600 },
                                { label: "4 hours", value: 14400 },
                                { label: "12 hours", value: 43200 },
                                { label: "1 day", value: 86400 },
                                { label: "2 days", value: 172800 },
                                { label: "7 days", value: 604800 },
                            ]}
                            useNativeAndroidPickerStyle={false}
                            Icon={() => (
                                <Svg height="24" viewBox="0 -960 960 960" width="24">
                                    <Path d="M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z" />
                                </Svg>
                            )}
                            style={{
                                iconContainer: {
                                    height: "100%",
                                    justifyContent: "center",
                                    paddingRight: 10,
                                    width: 30,
                                },
                                inputAndroid: {
                                    height: 50,
                                    fontFamily: typography.fontMedium.fontFamily,
                                    color: colors.black,
                                    borderWidth: 0.5,
                                    borderColor: colors.gray[400],
                                    borderRadius: 5,
                                    padding: 10,
                                    backgroundColor: colors.white,
                                },
                                inputIOS: {
                                    height: 50,
                                    fontFamily: typography.fontMedium.fontFamily,
                                    color: colors.black,
                                    borderWidth: 0.5,
                                    borderColor: colors.gray[400],
                                    borderRadius: 5,
                                    padding: 10,
                                    backgroundColor: colors.white,
                                },
                            }}
                        />
                    </View>
                </View>
                <View
                    style={{
                        flexDirection: "row",
                        alignSelf: "stretch",
                        justifyContent: "space-between",
                        gap: 5,
                    }}
                >
                    {formValues.ip ? (
                        <Button label="delete" type="filled" variant="red" action={() => handleDelete(formValues.ip)} />
                    ) : (
                        <View />
                    )}
                    <View
                        style={{
                            flexDirection: "row",
                            alignSelf: "stretch",
                            justifyContent: "flex-end",
                            gap: 5,
                        }}
                    >
                        <Button label="cancel" type="text" variant="gray" action={() => setModalStatus(false)} />
                        <Button label="save" type="filled" variant="primary" action={() => handleSubmit(formValues)} />
                    </View>
                </View>
            </Modal>
        </>
    );
};

export default IpBlocker;
