import BudgetMangerTabs from "@components/budgetManager/BudgetManagerTabs/BudgetMangerTabs";
import InitialBudgetCard from "@components/budgetManager/BudgetMangerComponents/InitialBudgetCard";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React, { useCallback, useEffect, useRef } from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import Svg, { Defs, G, LinearGradient, Mask, Path, Rect, Stop } from "react-native-svg";
import useBudgetStore from "../../store/budgetStore";
import BudgetInfoCard from "@components/budgetManager/BudgetMangerComponents/BudgetInfoCard";
import budgetStore from "../../store/budgetStore";
import { useStoreStore } from "../../store/storeStore";
import { Stack } from "expo-router";
import Header from "@components/Navigation/Header";

export type ColorsProp =
    | "white"
    | "black"
    | "primary"
    | "secondary"
    | "blue"
    | "green"
    | "red"
    | "orange"
    | "teal"
    | "cyan"
    | "pink"
    | "gray"
    | "blackAlpha"
    | "whiteAlpha";

type Budget = {
    balance: number;
    createdAt: string;
    description: string;
    expenses: Array<{
        name: string;
        subCategories: Array<any>;
        _id: string;
    }>;
    isDeleted: boolean;
    name: string;
    reference: number;
    revenues: Array<{
        name: string;
        subCategories: Array<any>;
        _id: string;
    }>;
    slug: string;
    startingBalance: number;
    store: string;
    updatedAt: string;
    __v: number;
    _id: string;
};

const BudgetManger = () => {
    let budgetStore = useBudgetStore();

    useEffect(() => {
        budgetStore.getAllBudgets();
        budgetStore.getLatestBudget();
        budgetStore.setSelectedBudget(budgetStore.latestBudget);
    }, []);

    let revenuesTotal = budgetStore.selectedBudget?.revenues.reduce(
        (acc, category) => acc + category.subCategories.reduce((acc, subCategory) => acc + subCategory.amount, 0),
        0
    );
    let expensesTotal = budgetStore.selectedBudget?.expenses.reduce(
        (acc, category) => acc + category.subCategories.reduce((acc, subCategory) => acc + subCategory.amount, 0),
        0
    );

    let currentBalance = budgetStore.selectedBudget?.startingBalance + revenuesTotal - expensesTotal;
    const bottomSheetRef = useRef<BottomSheet>(null);
    const handleSheetChanges = useCallback((index: number) => {}, []);

    return (
        <>
            <Stack.Screen
                options={{
                    headerTitle: "Budget Manager",
                    header: (props) => (
                        <Header
                            {...props}
                            // You can add a button action if needed
                            // modalButtonLabel="Add Budget"
                            // modalButtonAction={() => {/* Your action */}}
                        />
                    ),
                }}
            />

            <GestureHandlerRootView style={{ height: "100%" }}>
                <View style={{ height: "100%" }}>
                    <ScrollView style={{ height: "100%" }}>
                        <View style={styles.content}>
                            <BudgetMangerTabs>
                                <BudgetInfoCard
                                    key={budgetStore.selectedBudget._id}
                                    budgetName={budgetStore.selectedBudget.name}
                                    creationDate={new Date(budgetStore.selectedBudget.createdAt).toLocaleDateString()}
                                    budgetList={budgetStore.allBudgets}
                                    selectedBudget={budgetStore.selectedBudget}
                                />
                                <InitialBudgetCard
                                    initialBudget={budgetStore.selectedBudget.startingBalance}
                                    currency={useStoreStore.getState().store.currency.code}
                                    handleAction={(value: any) => {
                                        budgetStore.updateBudget(budgetStore.selectedBudget, {
                                            ...budgetStore.selectedBudget,
                                            startingBalance: value,
                                        });
                                    }}
                                />
                            </BudgetMangerTabs>
                        </View>
                    </ScrollView>
                </View>
                <BottomSheet
                    style={[
                        {
                            borderColor: colors.gray[100],
                            borderRadius: 15,
                            backgroundColor: "white",
                            borderWidth: 1,
                            elevation: 1,
                        },
                        // isKeyboardVisible && { display: 'none' },
                    ]}
                    snapPoints={[150, 310]}
                    ref={bottomSheetRef}
                    onChange={handleSheetChanges}
                    enableOverDrag={false}
                >
                    <BottomSheetView
                        style={{
                            alignItems: "center",
                            paddingHorizontal: 20,
                            gap: 10,
                            width: "100%",
                            paddingBottom: 30,
                        }}
                    >
                        <BottomSheetCard
                            color="primary"
                            label="Estimated Balance"
                            value={currentBalance}
                            icon={<EstimatedBudgetIcon />}
                        />
                        <View
                            style={{
                                flexDirection: "row",
                                width: "100%",
                                alignItems: "stretch",
                                gap: 10,
                            }}
                        >
                            <BottomSheetCard
                                stretch
                                color="green"
                                label="Revenue"
                                value={revenuesTotal}
                                icon={<EstimatedBudgetIcon />}
                            />
                            <BottomSheetCard
                                stretch
                                color="red"
                                label="Expenses"
                                value={expensesTotal}
                                icon={<EstimatedBudgetIcon />}
                            />
                        </View>
                    </BottomSheetView>
                </BottomSheet>
            </GestureHandlerRootView>
        </>
    );
};

const BottomSheetCard = ({
    value,
    color,
    label,
    icon,
    stretch,
}: {
    label: string;
    value: number;
    color: ColorsProp;
    icon?: React.ReactElement;
    stretch?: boolean;
}) => {
    const { store } = useStoreStore();

    return (
        <View
            style={[
                {
                    flexDirection: "row",
                    backgroundColor: colors[color][500],
                    borderRadius: 12,
                    padding: 20,
                    overflow: "hidden",
                    alignSelf: "stretch",
                },
                stretch && { flex: 1 },
            ]}
        >
            <View
                style={{
                    position: "absolute",
                    right: 0,
                    height: "100%",
                    width: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: "pink",
                    overflow: "visible",
                }}
            >
                <Elipses color={color} key={Math.random()} />
            </View>
            <View style={{ flex: 1, justifyContent: "center" }}>
                <View
                    style={{
                        flexDirection: "row",
                        gap: 10,
                        alignItems: "center",
                    }}
                >
                    {icon}
                    <Text
                        style={{
                            fontFamily: typography.fontSemibold.fontFamily,
                            fontSize: typography.lg.fontSize,
                            color: colors.white,
                        }}
                    >
                        {label}
                    </Text>
                </View>
                <Text
                    style={{
                        fontFamily: typography.fontBold.fontFamily,
                        fontSize: typography[color === "primary" ? "xl" : "lg"].fontSize,
                        color: colors.white,
                    }}
                >
                    {value.toFixed(2)}
                    <Text
                        style={{
                            fontSize: typography.xs.fontSize,
                        }}
                    >
                        {store.currency?.code}
                    </Text>
                </Text>
            </View>
        </View>
    );
};

export const Elipses = ({ color }: { color: ColorsProp }) => {
    const array = [20, 30, 45, 60, 80];
    return (
        <View
            key={color}
            style={{
                backgroundColor: "blue",
                justifyContent: "center",
                alignItems: "center",
                width: 1,
            }}
        >
            {array.map((deg, index) => {
                return <Ellipse key={index} rotate={`-${deg}deg`} color={color} />;
            })}
        </View>
    );
};

export const Ellipse = ({ rotate, color }: { rotate: string; color: ColorsProp }) => {
    return (
        <View
            style={{
                position: "absolute",
                left: "50%",
                top: "50%",
            }}
        >
            <View
                style={{
                    position: "relative",
                    left: "-30%",
                    top: "-50%",
                    transform: [{ rotate: rotate }],
                    // height: 1,
                    // width: 1,
                }}
            >
                <Svg width="323" height="173" viewBox="0 0 323 173" fill="none">
                    <Path
                        d="M322.087 86.4844C322.087 133.981 250.118 172.484 161.34 172.484C72.5624 172.484 0.59375 133.981 0.59375 86.4844C0.59375 38.9879 72.5624 0.484375 161.34 0.484375C250.118 0.484375 322.087 38.9879 322.087 86.4844Z"
                        fill="url(#paint0_linear_8671_32252)"
                    />
                    <Defs>
                        <LinearGradient
                            id="paint0_linear_8671_32252"
                            x1="161.34"
                            y1="0.484375"
                            x2="161.34"
                            y2="172.484"
                            gradientUnits="userSpaceOnUse"
                        >
                            <Stop stopColor={colors[color][600]} />
                            <Stop offset="1" stopColor={colors[color][900]} />
                        </LinearGradient>
                    </Defs>
                </Svg>
            </View>
        </View>
    );
};

const EstimatedBudgetIcon = () => {
    return (
        <Svg width="36" height="36" viewBox="0 0 36 36" fill="none">
            <Mask id="mask0_8307_31457" maskUnits="userSpaceOnUse" x="0" y="0" width="36" height="36">
                <Rect width="36" height="36" fill="#ffffff" />
            </Mask>
            <G mask="url(#mask0_8307_31457)">
                <Path
                    d="M21 19.5C19.75 19.5 18.6875 19.0625 17.8125 18.1875C16.9375 17.3125 16.5 16.25 16.5 15C16.5 13.75 16.9375 12.6875 17.8125 11.8125C18.6875 10.9375 19.75 10.5 21 10.5C22.25 10.5 23.3125 10.9375 24.1875 11.8125C25.0625 12.6875 25.5 13.75 25.5 15C25.5 16.25 25.0625 17.3125 24.1875 18.1875C23.3125 19.0625 22.25 19.5 21 19.5ZM10.5 24C9.675 24 8.96875 23.7063 8.38125 23.1188C7.79375 22.5312 7.5 21.825 7.5 21V9C7.5 8.175 7.79375 7.46875 8.38125 6.88125C8.96875 6.29375 9.675 6 10.5 6H31.5C32.325 6 33.0312 6.29375 33.6188 6.88125C34.2062 7.46875 34.5 8.175 34.5 9V21C34.5 21.825 34.2062 22.5312 33.6188 23.1188C33.0312 23.7063 32.325 24 31.5 24H10.5ZM13.5 21H28.5C28.5 20.175 28.7937 19.4688 29.3812 18.8813C29.9688 18.2938 30.675 18 31.5 18V12C30.675 12 29.9688 11.7063 29.3812 11.1188C28.7937 10.5312 28.5 9.825 28.5 9H13.5C13.5 9.825 13.2063 10.5312 12.6187 11.1188C12.0312 11.7063 11.325 12 10.5 12V18C11.325 18 12.0312 18.2938 12.6187 18.8813C13.2063 19.4688 13.5 20.175 13.5 21ZM30 30H4.5C3.675 30 2.96875 29.7063 2.38125 29.1188C1.79375 28.5312 1.5 27.825 1.5 27V10.5H4.5V27H30V30Z"
                    fill="white"
                />
            </G>
        </Svg>
    );
};

export default BudgetManger;

const styles = StyleSheet.create({
    content: { paddingHorizontal: 15, paddingBottom: 20, height: "100%" },
});
