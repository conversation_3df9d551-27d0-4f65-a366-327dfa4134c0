import { View, Text, StyleSheet, FlatList, TouchableOpacity, RefreshControl, ActivityIndicator } from "react-native";
import React, { useEffect, useState } from "react";
import { router, Stack } from "expo-router";
import Header from "@components/Navigation/Header";
import colors from "@styles/colors";
import Divider from "@components/dividers/Divider";
import { useStoreStore } from "../../store/storeStore";
import { typography } from "@styles/typography";

const Item = ({ name }: { name: string }) => (
    <View>
        <TouchableOpacity
            style={styles.row}
            onPress={() => {
                router.navigate({ pathname: "/detailPreset", params: { name: name } });
            }}
        >
            <Text style={styles.budgetDate}>{"Name"}</Text>
            <Text style={styles.budgetName}>{name}</Text>
        </TouchableOpacity>
    </View>
);

export type DzCities =
    | "Adrar"
    | "Chlef"
    | "Laghouat"
    | "Oum El Bouaghi"
    | "Batna"
    | "Bejaia"
    | "Biskra"
    | "Bechar"
    | "Blida"
    | "Bouira"
    | "Tamanrasset"
    | "Tebessa"
    | "Tlemcen"
    | "Tiaret"
    | "Tizi Ouzou"
    | "Alger"
    | "Djelfa"
    | "Jijel"
    | "Setif"
    | "Saida"
    | "Skikda"
    | "Sidi Bel Abbes"
    | "Annaba"
    | "Guelma"
    | "Constantine"
    | "Medea"
    | "Mostaganem"
    | "M'Sila"
    | "Mascara"
    | "Ouargla"
    | "Oran"
    | "El Bayadh"
    | "Illizi"
    | "Bordj Bou Arreridj"
    | "Boumerdes"
    | "El Tarf"
    | "Tindouf"
    | "Tissemsilt"
    | "El Oued"
    | "Khenchela"
    | "Souk Ahras"
    | "Tipaza"
    | "Mila"
    | "Ain Defla"
    | "Naama"
    | "Ain Temouchent"
    | "Ghardaia"
    | "Relizane"
    | "Timimoun"
    | "Bordj Badji Mokhtar"
    | "Ouled Djellal"
    | "Beni Abbes"
    | "In Salah"
    | "In Guezzam"
    | "Touggourt"
    | "Djanet"
    | "El M ghaier"
    | "El Meniaa";

export type Preset = Record<DzCities, { stopdesk: number; delivery: number }>;
export type PresetsRecord = Record<string, Preset>;
type PresetsArray = Array<{ name: string; preset: Preset }>;

const Index = () => {
    const [presets, setPresets] = useState<PresetsArray>([]);
    // const [loading, setLoading] = useState(true);

    const { store, getStore } = useStoreStore();

    useEffect(() => {
        const _presetsIntegration = store?.integrations?.find((integration) => {
            return integration?.ref === "dz-wilaya-presets";
        });

        if (_presetsIntegration?.fields?.presets) {
            setPresets(
                Object.entries(_presetsIntegration?.fields?.presets).map((value: [string, Preset]) => ({
                    name: value[0],
                    preset: value[1],
                }))
            );
        }
    }, [store]);

    const refresh = () => {
        getStore();
    };

    return (
        <>
            <Stack.Screen
                options={{
                    header: (props) => {
                        return (
                            <Header
                                {...props}
                                modalButtonLabel="Add"
                                modalButtonAction={() => {
                                    router.navigate("/createPreset");
                                }}
                            />
                        );
                    },
                }}
            />
            {presets.length === 0 ? (
                <View
                    style={{
                        width: "100%",
                        height: "80%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    <Text style={[typography.fontBold]}>You Don't have any Preset setup</Text>
                </View>
            ) : (
                <FlatList
                    data={presets}
                    renderItem={({ item }) => <Item name={item.name} />}
                    keyExtractor={(_, index) => index.toString()}
                    contentContainerStyle={{
                        backgroundColor: colors.gray[50],
                        paddingHorizontal: 10,
                        paddingTop: 10,
                        paddingBottom: 500,
                        gap: 10,
                    }}
                />
            )}
        </>
    );
};

export default Index;

const styles = StyleSheet.create({
    label: {
        fontSize: 16,
        fontWeight: "semibold",
        color: colors.primary[700],
        paddingBottom: 10,
        paddingLeft: 10,
    },
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderColor: colors.gray[400],
        borderWidth: 1,
        borderRadius: 10,
    },
    column: {
        flexDirection: "column",
        justifyContent: "space-between",
        alignItems: "flex-start",
    },
    firstView: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderColor: colors.gray[400],
        borderWidth: 1,
        borderRadius: 10,
    },
    budgetName: {
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.md.fontSize,
        color: colors.gray[600],
    },
    budgetDate: {
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: typography.sm.fontSize,
        color: colors.gray[600],
    },
    buttonLabel: {
        textTransform: "capitalize",
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: typography.sm.fontSize,
        color: colors.gray[600],
    },
});
