import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { Linking, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useStoreStore } from "../../store/storeStore";

const StoreInfo = () => {
    const { store } = useStoreStore();

    const openLink = (url: string) => {
        Linking.openURL(url)
            .then((r) => console.log(r))
            .catch((e) => {
                console.error("can't open link");
            });
    };

    return (
        <View style={styles.container}>
            <View style={styles.container}>
                <View style={styles.card}>
                    <Text style={styles.header}>Store Information</Text>
                    <View style={styles.cardRow}>
                        <Text style={styles?.label}>Name:</Text>
                        <Text style={styles.value}>{store.name}</Text>
                    </View>
                    <View style={styles.cardRow}>
                        <Text style={styles?.label}>Title:</Text>

                        <Text style={styles.value}>{store.title || store.name}</Text>
                    </View>
                    <View style={styles.cardRow}>
                        <Text style={styles?.label}>SEO Description:</Text>
                        <Text style={styles.value}>{store.seoDescription || "N/A"}</Text>
                    </View>
                </View>
                <View style={styles.card}>
                    <Text style={styles.header}>Owner Information</Text>
                    <View style={styles.cardRow}>
                        <Text style={styles?.label}>Phone:</Text>
                        <Text style={styles.value}>{store.contactInfo.phones[0]}</Text>
                    </View>
                    <View style={styles.cardRow}>
                        <Text style={styles?.label}>Email:</Text>
                        <Text style={styles.value}>{store.contactInfo.email ? store.contactInfo.email : "N/A"}</Text>
                    </View>
                </View>
                <View style={styles.card}>
                    <Text style={styles.header}>Store Domain</Text>
                    <View style={styles.cardRow}>
                        <Text style={styles?.label}>Default Domain:</Text>
                        <TouchableOpacity onPress={() => openLink(store.domain)} style={{ paddingHorizontal: 5 }}>
                            <Text style={styles.link}>{store.domain}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={styles.card}>
                    <Text style={styles.header}>Social Links</Text>
                    <TouchableOpacity onPress={() => openLink(store.contactInfo.facebook)}>
                        <Text style={styles.link}>Facebook</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => openLink(store.contactInfo.instagram)}>
                        <Text style={styles.link}>Instagram</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => openLink(store.contactInfo.tiktok)}>
                        <Text style={styles.link}>TikTok</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        paddingTop: 5,
        paddingHorizontal: 5,
        backgroundColor: colors.gray[50],
        height: "100%",
        gap: 20,
    },
    cardRow: { flexDirection: "row", justifyContent: "space-between" },
    card: {
        backgroundColor: "white",
        borderRadius: 10,
        padding: 15,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 5,
        elevation: 3,
        gap: 10,
    },
    header: {
        fontFamily: typography.fontBold.fontFamily,
        fontSize: 18,
        color: colors.primary[800],
    },
    label: {
        fontSize: 16,
        color: colors.gray[800],
        justifyContent: "space-between",
        textAlign: "justify",
    },
    value: {
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.gray[900],
    },
    link: {
        fontSize: 16,
        color: colors.primary[800],
        textDecorationLine: "underline",
    },
});

export default StoreInfo;
