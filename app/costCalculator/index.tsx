import api from "@api/api";
import SheetElement from "@components/costCalculator/CostCalculatorComponents/BottomSheet/SheetElement";
import CostCalculatorContent from "@components/costCalculator/CostCalculatorComponents/Content/CostCalculatorContent";
import content from "@components/costCalculator/CostCalculatorUtils/content";
import results from "@components/costCalculator/CostCalculatorUtils/results";
import { FormValuesProps } from "@components/costCalculator/Props";
import Button from "@components/inputs/buttons/Button";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React, { useState, useRef, useCallback, useEffect } from "react";
import { ActivityIndicator, Keyboard, StyleSheet, View, Platform } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { useStoreStore } from "../../store/storeStore";
import { snapPoint } from "@gorhom/bottom-sheet/lib/typescript/utilities/snapPoint";
import Toast from "react-native-toast-message";
import { Stack } from "expo-router";
import Header from "@components/Navigation/Header";
// or
import ModalHeader from "@components/Navigation/ModalHeader";

const CostCalculator = () => {
    const [formValues, setFormValues] = useState({
        productCost: 15,
        sellingPrice: 40,
        deliveryCost: 7,
        returnCost: 1,
        fulfillmentCost: 0,
        leadCost: 3.5,
        leadReceived: 100,
        confirmationRate: 75,
        deliveryRate: 80,
    });

    const [lastSubmitedValues, setLastSubmitedValues] = useState<any>({});

    const [result, setResult] = useState({
        breakEvenLeadCost: 0,
        deliveredLeads: 0,
        totalProfit: 0,
        profitPerUnit: 0,
        confirmedLeads: 0,
        costPerDelivered: 0,
    });

    const [loading, setLoading] = useState(false);
    // Initialize sheet index based on platform
    const [sheetIndex, setSheetIndex] = useState(Platform.OS === "android" ? 0 : 1);

    const bottomSheetRef = useRef<BottomSheet>(null);

    const handleSheetChanges = useCallback((index: number) => {
        //console.log("Sheet Index => ", index);
        setSheetIndex(index);
    }, []);

    // When you need to expand the sheet
    const expandSheet = () => {
        setSheetIndex(Platform.OS === "android" ? 1 : 2);
    };

    // When you need to collapse the sheet
    const collapseSheet = () => {
        setSheetIndex(Platform.OS === "android" ? 0 : 1);
    };

    const handleCalculate = () => {
        setLoading(true);
        expandSheet(); // Use the platform-aware function instead of hardcoding
        const isSubmited = !Object.keys(formValues).some((key) => formValues[key] !== lastSubmitedValues[key]);
        if (isSubmited) {
            // todo: fix this so the user knows nothing changed
            setTimeout(() => {
                setLoading(false);
            }, 1000);
            return;
        }
        for (const key in formValues) {
            if (Object.prototype.hasOwnProperty.call(formValues, key)) {
                const value = formValues[key].toString();
                if (value.includes(",")) {
                    formValues[key] = Number.parseFloat(value.replace(",", "."));
                }
            }
        }
        console.log("formValues => ", formValues);
        api.post("/calculator", formValues)
            .then((res) => {
                setResult(res.data);
            })
            .catch((err) => {
                console.log(err);
                //todo: add toast
            })
            .finally(() => {
                setLoading(false);
                setLastSubmitedValues({ ...formValues });
            });
    };
    const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
    const { store } = useStoreStore();

    useEffect(() => {
        console.log("sheetIndex changed to:", sheetIndex);
    }, [sheetIndex]);

    const sheetResults = results(result, store.currency?.code);

    const pageContent = content(store.currency?.code, formValues, setFormValues);

    return (
        <>
            <Stack.Screen
                options={{
                    headerTitle: "Cost Calculator",
                    header: (props) => (
                        <Header {...props} modalButtonLabel="Calculate" modalButtonAction={handleCalculate} />
                    ),
                }}
            />

            <GestureHandlerRootView style={{ height: "100%" }}>
                <CostCalculatorContent pageContent={pageContent} />
                {/* <View style={{ height: '30%' }}></View> */}
                <BottomSheet
                    // keyboardBehavior="fillParent"
                    style={[
                        {
                            borderColor: colors.gray[100],
                            borderRadius: 15,
                            backgroundColor: "white",
                            borderWidth: 1,
                            elevation: 1,
                        },
                        // !isKeyboardVisible && { display: 'none' },
                    ]}
                    snapPoints={Platform.OS === "android" ? ["22%", "65%"] : ["22%", "60%"]}
                    ref={bottomSheetRef}
                    onChange={handleSheetChanges}
                    enableOverDrag={false}
                    enablePanDownToClose={false}
                    enableDynamicSizing
                    index={sheetIndex}
                >
                    <BottomSheetView
                        style={[
                            {
                                flex: 1,
                                zIndex: 100,
                                alignItems: "center",
                                paddingHorizontal: 20,
                                gap: 10,
                            },
                        ]}
                    >
                        <Button
                            disabled={loading}
                            rounded
                            label="Calculate"
                            type="filled"
                            variant={loading ? "gray" : "primary"}
                            action={handleCalculate}
                            style={{ height: 40, alignSelf: "stretch" }}
                        />
                        {loading ? (
                            <View style={{ height: "100%", paddingTop: "20%", alignItems: "center" }}>
                                <ActivityIndicator />
                            </View>
                        ) : (
                            <View style={{ gap: 5, width: "100%" }}>
                                {sheetResults.map((sheetResult, index) => {
                                    return (
                                        <SheetElement
                                            key={index}
                                            label={sheetResult?.label}
                                            value={sheetResult.value}
                                            unit={sheetResult.unit}
                                            icon={sheetResult.icon}
                                        />
                                    );
                                })}
                            </View>
                        )}
                    </BottomSheetView>
                </BottomSheet>
            </GestureHandlerRootView>
        </>
    );
};

export default CostCalculator;

const styles = StyleSheet.create({
    scroll: {
        height: "100%",
        paddingHorizontal: 10,
    },
    content: {
        gap: 10,
        paddingBottom: "50%",
    },
    card: {
        padding: 20,
        gap: 20,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "stretch",
        borderColor: colors.gray[300],
        backgroundColor: "white",
        borderWidth: 1,
        borderRadius: 10,
    },
    title: {
        color: colors["black"][600],
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.md.fontSize,
        lineHeight: 24,
        textTransform: "capitalize",
    },
    label: {},
    input: {},
    currency: { color: colors.gray[600], fontSize: typography.sm.fontSize },
});
