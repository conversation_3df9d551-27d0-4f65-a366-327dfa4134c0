import { Button, Dimensions, Platform, ScrollView, Text, TouchableOpacity, View } from "react-native";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
// import { BarCodeScanningResult, Camera } from "expo-camera/legacy";
import { router, useFocusEffect } from "expo-router";
import colors from "@styles/colors";
import QrOrder from "@components/scanner/QrOrder/QrOrder";
import { getBarCodeStatus, ScanRecord } from "@components/scanner/utils/QrScan.utils";
import Flash from "@components/scanner/Flash/Flash";
import { CameraView, CameraType, useCameraPermissions, BarcodeScanningResult, Point } from "expo-camera";
import Svg, { Polygon } from "react-native-svg";
import { string } from "zod";
import { Order } from "@components/orders/types";
import _ from "lodash";
import api from "@api/api";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import OrderCard from "@components/cards/orders/OrderCard";
import { useOrderStore } from "../../../store/orders";
import * as ScreenOrientation from "expo-screen-orientation";

import { Gyroscope, Accelerometer } from "expo-sensors";
import Divider from "@components/dividers/Divider";
import { Tag } from "@components/inputs/chips/Tag/Tag";
import delivery from "@app/stats/delivery";
import { DeliveryCompanyLogo } from "@components/Navigation/ModalView/SelectDeliveryCompany";
import { useStoreStore } from "../../../store/storeStore";
import { ProductImage } from "@components/cards/products/ProductCard";
import { ColorTag, SmallImage } from "@components/productDetails/components";
import { typography } from "@styles/typography";
import { TruckIcon } from "@components/buttomSheetCard/NormalSheetCard";
import { ActivityIndicator } from "react-native";
import { isImageLink } from "@components/productDetails/utils";
import { StyleSheet } from "react-native";

const scanner = () => {
    const [facing, setFacing] = useState<CameraType>("back");
    const [flash, setFlash] = useState(false);

    const [permission, requestPermission] = useCameraPermissions();

    const [barcode, setBarcode] = useState<BarcodeScanningResult>();
    const [barcodeData, setBarcodeData] = useState<string>("");
    const [scanned, setScanned] = useState(false);
    const [lastScanTime, setLastScanTime] = useState<Date | null>(null);
    const [loading, setLoading] = useState(false);

    const [scanningResult, setScanningResult] = useState<Partial<Order>>();

    const [position, setPosition] = useState({ x: 0, y: 0, z: 0 });
    const [orientation, setOrientation] = useState("");

    const { store } = useStoreStore();

    const { setOrder } = useOrderStore();

    const [success, setSuccess] = useState(false);

    const [bottomSheetPosition, setBottomSheetPosition] = useState(0);

    useEffect(() => {
        const subscription = Accelerometer.addListener(({ x, y, z }) => {
            setPosition({ x, y, z });
            const threshold = 0.75; // Adjust as needed for sensitivity
            const absX = Math.abs(x);
            const absY = Math.abs(y);
            const absZ = Math.abs(z);

            if (absZ > threshold) {
                if (absX > absY) {
                    setOrientation("Horizontal");
                } else {
                    setOrientation("Vertical");
                }
            } else if (absY > absX) {
                setOrientation(y > 0 ? "Vertical" : "Vertical");
            } else {
                setOrientation(x > 0 ? "Horizontal" : "Horizontal");
            }
        });
        Accelerometer.setUpdateInterval(100);
        return () => {
            subscription.remove();
        };
    }, []);

    useEffect(() => {
        const interval = setInterval(() => {
            if (scanned && lastScanTime) {
                const now = new Date();
                const timeElapsed = now.getTime() - lastScanTime.getTime();

                if (timeElapsed > 500) {
                    setScanned(false); // Reset the scanned state
                }
            }
        }, 200);

        return () => clearInterval(interval);
    }, [scanned, lastScanTime]);

    useEffect(() => {
        barcodeData && fetchOrders();
    }, [barcodeData]);

    const fetchOrders = async () => {
        try {
            setLoading((prev) => {
                return true;
            });
            console.log("scanning...");

            console.log({ barcodeData });
            if (barcodeData !== scanningResult?._id) {
                const response = await api.patch("/order/scan/" + barcodeData);

                console.log(response.data);
                setScanningResult(response.data);
            } else {
                console.log("same id");
            }
            setSuccess(true);
        } catch (error) {
            setSuccess(false);
            console.log("scanning error=>", error);
        } finally {
            setLoading((prev) => {
                return false;
            });
        }
    };

    const handleSheetChanges: (index: number) => void = (index) => {
        setBottomSheetPosition(index);
    };

    const bottomSheetRef = useRef<BottomSheet>(null);

    if (!permission) {
        return <View />;
    }

    if (!permission.granted) {
        return (
            <View>
                <Text>We need permission to show the camera</Text>
                <Button title="Grant Permission" onPress={requestPermission} />
            </View>
        );
    }

    const snapPoints =
        Platform.OS === "ios"
            ? [!loading && success && scanned ? 110 : 70, !loading && success ? 250 : 70]
            : [!loading && success && scanned ? 120 : 75, !loading && success ? 270 : 70];

    return (
        <GestureHandlerRootView style={styles.container}>
            <CameraView
                enableTorch={flash}
                style={styles.camera}
                facing={facing}
                barcodeScannerSettings={{ barcodeTypes: ["qr"] }}
                onBarcodeScanned={(t) => {
                    setBarcode(t);
                    t.data !== barcodeData && setBarcodeData(t.data);
                    setScanned((prev) => {
                        return true;
                    });
                    setLastScanTime((prev) => {
                        return new Date();
                    });
                }}
                responsiveOrientationWhenOrientationLocked={true}
                onResponsiveOrientationChanged={(e) => {
                    console.log("change");
                }}
            />

            <Flash
                on={flash}
                onPress={() => {
                    setFlash((prev) => {
                        return !prev;
                    });
                }}
            />
            {scanned && (
                <ScanningBounderies orientation={orientation} barcode={barcode} points={barcode?.cornerPoints} />
            )}
            <BottomSheet
                style={[
                    {
                        borderColor: colors.gray[100],
                        borderRadius: 15,
                        backgroundColor: "white",
                        borderWidth: 1,
                        elevation: 1,
                    },
                ]}
                snapPoints={snapPoints}
                ref={bottomSheetRef}
                onChange={handleSheetChanges}
                enableOverDrag={false}
            >
                {loading && (
                    <View style={{ justifyContent: "center", alignItems: "center", padding: 10 }}>
                        <ActivityIndicator />
                    </View>
                )}

                {!loading && !barcodeData && (
                    <View
                        style={{
                            height: "100%",
                            paddingHorizontal: 15,
                            paddingBottom: 10,
                            flexDirection: "row",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <Text style={[typography.altTextSemiBold, typography.lg, { color: colors.primary[500] }]}>
                            QR Scanner
                        </Text>
                    </View>
                )}

                {!loading && !success && barcodeData && (
                    <View
                        style={{
                            paddingHorizontal: 15,
                            flexDirection: "row",
                            alignItems: "center",
                            justifyContent: "center",
                            height: "100%",
                        }}
                    >
                        <Text style={[typography.altTextSemiBold, typography.sm, { color: colors.red[500] }]}>
                            Invalid QR Code, Please Scan Another QR
                        </Text>
                    </View>
                )}

                {!loading && success && (
                    <BottomSheetView style={{ height: "100%" }}>
                        <TouchableOpacity
                            onPress={() => {
                                setOrder(scanningResult as Order);
                                router.navigate({
                                    params: { id: scanningResult._id },
                                    pathname: "/order/orderDetails",
                                });
                            }}
                            style={{ gap: 5, height: "100%", justifyContent: "space-between" }}
                        >
                            <View style={{ paddingHorizontal: 15, gap: 5 }}>
                                <View>
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            justifyContent: "space-between",
                                            alignItems: "center",
                                        }}
                                    >
                                        <View>
                                            <Text
                                                style={[
                                                    typography.fontSemibold,
                                                    typography.lg,
                                                    { color: colors.gray[700] },
                                                ]}
                                            >
                                                {scanningResult?.customer?.name}
                                            </Text>
                                            <Text
                                                style={[
                                                    typography.fontNormal,
                                                    typography.sm,
                                                    { color: colors.gray[600] },
                                                ]}
                                            >
                                                {new Date(scanningResult?.createdAt).toDateString()}
                                            </Text>
                                        </View>
                                        {scanningResult?.status && <Tag status={scanningResult.status} />}
                                    </View>
                                </View>

                                {scanningResult?.deliveryCompany && scanningResult?.deliveryCompany !== "none" && (
                                    <Tag delivery={scanningResult?.deliveryCompany} />
                                )}
                            </View>
                            <View style={{ flexGrow: 1 }}>
                                <ScrollView
                                    horizontal
                                    style={{ flexGrow: 0 }}
                                    contentContainerStyle={{
                                        gap: 10,
                                        paddingVertical: 5,
                                        paddingBottom: 10,
                                        paddingLeft: 15,
                                    }}
                                >
                                    {scanningResult?.cart?.map((item, index) => {
                                        return (
                                            <View
                                                key={index}
                                                style={{
                                                    gap: 5,
                                                    borderColor: colors.gray[300],
                                                    borderRadius: 10,
                                                    padding: 10,
                                                    borderWidth: 1,
                                                    width: 300,
                                                    overflow: "hidden",
                                                }}
                                            >
                                                <View
                                                    style={{ flexDirection: "row", alignItems: "flex-start", gap: 10 }}
                                                >
                                                    {item.product?.images.length > 0 && (
                                                        <View style={{ paddingVertical: 5 }}>
                                                            <SmallImage source={{ uri: item.product?.images[0].sm }} />
                                                        </View>
                                                    )}
                                                    <View style={{ flexGrow: 1 }}>
                                                        <View style={{ width: 220 }}>
                                                            <Text
                                                                numberOfLines={1}
                                                                style={[
                                                                    typography.md,
                                                                    typography.altText,
                                                                    { color: colors.gray[700] },
                                                                ]}
                                                            >
                                                                {item.selectedVariants.length} {item.product.name}
                                                            </Text>
                                                        </View>
                                                        <View
                                                            style={{
                                                                flexDirection: "row",
                                                                justifyContent: "space-between",
                                                            }}
                                                        >
                                                            <View
                                                                style={{
                                                                    flexDirection: "row",
                                                                    gap: 20,
                                                                    alignItems: "center",
                                                                }}
                                                            >
                                                                <Text
                                                                    style={[
                                                                        typography.fontNormal,
                                                                        typography.sm,
                                                                        { color: colors.gray[700] },
                                                                    ]}
                                                                >
                                                                    Qty. {"  "}
                                                                    {item.quantity}
                                                                </Text>
                                                                <Text
                                                                    style={[
                                                                        typography.fontNormal,
                                                                        typography.sm,
                                                                        { color: colors.gray[700] },
                                                                    ]}
                                                                >
                                                                    ×
                                                                </Text>
                                                                <Text
                                                                    style={[
                                                                        typography.fontNormal,
                                                                        typography.sm,
                                                                        { color: colors.gray[700] },
                                                                    ]}
                                                                >
                                                                    {item.pricePerUnit}
                                                                </Text>
                                                            </View>
                                                            <Text
                                                                style={[
                                                                    typography.fontNormal,
                                                                    typography.sm,
                                                                    { color: colors.gray[700] },
                                                                ]}
                                                            >
                                                                {item.quantity * item.pricePerUnit}{" "}
                                                                {store.currency.code}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </View>
                                                {item.selectedVariants?.length > 0 && (
                                                    <View
                                                        style={{
                                                            flexDirection: "row",
                                                            justifyContent: "space-between",
                                                            alignItems: "center",
                                                        }}
                                                    >
                                                        <Text
                                                            style={[
                                                                typography.fontNormal,
                                                                typography.sm,
                                                                { color: colors.gray[700] },
                                                            ]}
                                                        >
                                                            Variants:
                                                        </Text>
                                                        <CardVariants selectedVariants={item.selectedVariants} />
                                                    </View>
                                                )}
                                            </View>
                                        );
                                    })}
                                </ScrollView>
                            </View>
                            <View></View>

                            <View style={{ paddingHorizontal: 15, gap: 10 }}>
                                <Divider />
                                <View
                                    style={{
                                        flexDirection: "row",
                                        justifyContent: "space-between",
                                        paddingHorizontal: 10,
                                    }}
                                >
                                    <Text
                                        style={[
                                            typography.altTextSemiBold,
                                            typography.lg,
                                            { color: colors.primary[500] },
                                        ]}
                                    >
                                        Total
                                    </Text>
                                    <Text
                                        style={[
                                            typography.altTextSemiBold,
                                            typography.lg,
                                            { color: colors.primary[500] },
                                        ]}
                                    >
                                        {scanningResult?.total.totalPrice} {store.currency.code}
                                    </Text>
                                </View>
                            </View>
                        </TouchableOpacity>
                    </BottomSheetView>
                )}
            </BottomSheet>
            {/* <View
                style={{
                    position: "absolute",
                    top: 50,
                    backgroundColor: "white",
                    width: "50%",
                    height: 100,
                    opacity: 0.5,
                }}
            >
                <Text>
                    {JSON.stringify({
                        scanned,
                        success,
                        loading,
                        scanningResult,
                        barcode,
                        barcodeData,
                    })}
                </Text>
            </View> */}
            {(bottomSheetPosition === 0 || !(!loading && success)) && (
                <View style={{ paddingHorizontal: 15 }}>
                    <Divider />
                </View>
            )}
        </GestureHandlerRootView>
    );
};

const CardVariants = ({
    selectedVariants,
}: {
    selectedVariants: {
        name: string;
        type: "text" | "color" | "image";
        value: string;
    }[];
}) => {
    return (
        <View
            style={{
                flexDirection: "row",
                gap: 10,
            }}
        >
            {selectedVariants.map((variant, index) => {
                const { value } = variant;
                if (variant.type === "color") return <ColorTag color={value} key={index} />;
                if (variant.type === "image" || isImageLink(value)) {
                    return <SmallImage source={{ uri: value }} key={index} />;
                }
                return (
                    <View
                        key={index}
                        style={{
                            backgroundColor: "rgba(115,0,200,0.05)",
                            padding: 5,
                            paddingHorizontal: 10,
                            borderRadius: 5,
                        }}
                    >
                        <Text
                            numberOfLines={1}
                            style={[
                                typography.fontMedium,
                                typography.sm,
                                {
                                    color: typography.grey[600],
                                },
                            ]}
                        >
                            {value}
                        </Text>
                    </View>
                );
            })}
        </View>
    );
};

const ScanningBounderies = ({
    points,
    barcode,
    orientation,
}: {
    points: Point[];
    barcode: BarcodeScanningResult;
    orientation: string;
}) => {
    let stringCorrdinates = "";
    points?.map(({ x, y }) => {
        let _x = x;
        let _y = y;
        if (Platform.OS === "android") {
            _x = Dimensions.get("window").width - x;
        }
        stringCorrdinates += `${_x},${_y} `;
    });

    if (Platform.OS == "android") {
        return (
            <View
                style={[
                    {
                        position: "absolute",

                        height: barcode?.bounds.size.width,
                        width: barcode?.bounds.size.width,
                        backgroundColor: "black",
                        opacity: 0.25,
                        borderRadius: barcode?.bounds.size.height,
                    },
                    orientation === "Horizontal"
                        ? {
                              top: barcode?.bounds.origin.y * 1.45,
                              left: barcode?.bounds.origin.x - barcode?.bounds.size.height,
                          }
                        : {
                              top: barcode?.bounds.origin.x,
                              right: barcode?.bounds.origin.y,
                          },
                ]}
            />
        );
    }

    return (
        <Svg
            height={Dimensions.get("window").height}
            width={Dimensions.get("window").width}
            style={styles.svg}
            fillOpacity={0.2}
        >
            <Polygon
                points={stringCorrdinates} // x1,y1 x2,y2 x3,y3 x4,y4
                fill="blue" // Fill color of the quadrilateral
                stroke="black" // Border color
                strokeWidth="0.5"
            />
        </Svg>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        position: "relative",
        justifyContent: "center",
    },
    svg: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    message: {
        textAlign: "center",
        paddingBottom: 10,
    },
    camera: {
        flex: 1,
    },
    buttonContainer: {
        flex: 1,
        flexDirection: "row",
        backgroundColor: "transparent",
        margin: 64,
    },
    button: {
        flex: 1,
        alignSelf: "flex-end",
        alignItems: "center",
    },
    text: {
        fontSize: 24,
        fontWeight: "bold",
        color: "white",
    },
});

export default scanner;
