import React, { memo, useCallback, useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { router } from 'expo-router';
import { useStoreStore } from '../../../store/storeStore';
import { useOrderStore } from '../../../store/orders';
import { OrdersListModal } from '@components/modals/OrdersListModal';
import { OrdersContext } from '../../../contexts/OrdersContext';
import { EditIcon } from '@components/icons/BudgetManagerIcons';
import { CloseIcon } from '@components/icons/HeaderIcons';
import { SendIcon } from '@components/icons/AccountListIcons';
import FAB from '@components/inputs/buttons/FloatingActionButton';
import { Divider } from 'react-native-elements';
import colors from '@styles/colors';
import { typography } from '@styles/typography';

// New components
import OrdersSearch from '@components/orders/OrdersSearch';
import OrdersFilters from '@components/orders/OrdersFilters';
import OrdersList from '@components/orders/OrdersList';

// New hooks
import { useOrdersData } from '../../../hooks/orders/useOrdersData';
import { useOrdersPerformance } from '../../../hooks/orders/useOrdersPerformance';

// Types for backward compatibility
export type SocketInfo = {
    eventId?: string;
    eventTimestamp?: number;
};

export type NotificationProps = {
    title: string;
    message: string;
    date: string;
    tag: string;
};

/**
 * OrdersPage Component (Refactored)
 *
 * A clean, performant orders page that orchestrates sub-components and hooks.
 * Reduced from 904 lines to under 300 lines with improved maintainability.
 */
const OrdersPage = memo(() => {
    const { store, user } = useStoreStore();
    const { setOrder } = useOrderStore();

    // UI state for search and filters visibility
    const [showSearch, setShowSearch] = useState(false);
    const [showFilters, setShowFilters] = useState(false);

    // Custom hooks for data management and performance
    const ordersData = useOrdersData();
    const performance = useOrdersPerformance(ordersData.filter);

    // Memoized sellmax integration check
    const sellmax = store?.integrations?.find((integration) => integration.ref === 'sellmax');

    // Handler for modal success actions
    const handleModalSuccess = useCallback(() => {
        // Close the swipeable card if it's open
        performance.swipeableClose?.();
        performance.handleSetSwipeableClose(undefined);

        // Clear the state
        performance.handleSetModalVisible(false);
        performance.handleSetSelectedOrdersId([]);

        // Force a complete refresh after successful operations
        ordersData.handleRefresh();
    }, [performance, ordersData]);

    // Handler for modal cancel actions
    const handleModalCancel = useCallback(() => {
        performance.swipeableClose?.();
        performance.handleSetSwipeableClose(undefined);
        performance.handleSetModalVisible(false);
    }, [performance]);

    // Handler for creating new order
    const handleCreateOrder = useCallback(() => {
        setOrder({
            customer: {},
            cart: [],
            status: 'pending',
            total: {
                deliveryPrice: 0,
                deliveryCost: 0,
                totalPrice: 0,
            },
            deliveryCompany: 'none',
            note: '',
        });
        router.navigate('/createOrder');
    }, [setOrder]);

    return (
        <OrdersContext.Provider value={performance.contextValue}>
            <GestureHandlerRootView style={{ flex: 1 }}>
                {/* Search and Filters Section */}
                <View style={{ gap: 5, backgroundColor: colors.gray[50] }}>
                    <View style={{ flexDirection: 'row', paddingLeft: 10 }}>
                        {/* Search and Filter Icons with consistent spacing */}
                        <View style={{ flexDirection: 'row', gap: 5, paddingHorizontal: 5 }}>
                            {/* Search Toggle Button */}
                            <OrdersSearch
                                isVisible={showSearch}
                                searchValue={ordersData.searchValue}
                                disabled={ordersData.loading}
                                onToggleVisibility={setShowSearch}
                                onSearchChange={ordersData.handleSearchFilter}
                                renderOnlyIcon={true}
                            />

                            {/* Filter Toggle Button */}
                            <OrdersFilters
                                isFiltersVisible={showFilters}
                                activeFilterChip={ordersData.activeFilterChip}
                                activeDeliveryCompany={ordersData.activeDeliveryCompany}
                                activeProductItem={ordersData.activeProductItem}
                                disabled={ordersData.loading}
                                onToggleFiltersVisibility={setShowFilters}
                                onStatusFilterChange={ordersData.handleStatusFilter}
                                onDeliveryCompanyChange={ordersData.handleDeliveryCompanyFilter}
                                onProductChange={ordersData.handleProductFilter}
                                renderOnlyIcon={true}
                            />
                        </View>

                        {/* Status Filter Chips */}
                        <OrdersFilters
                            isFiltersVisible={showFilters}
                            activeFilterChip={ordersData.activeFilterChip}
                            activeDeliveryCompany={ordersData.activeDeliveryCompany}
                            activeProductItem={ordersData.activeProductItem}
                            disabled={ordersData.loading}
                            onToggleFiltersVisibility={setShowFilters}
                            onStatusFilterChange={ordersData.handleStatusFilter}
                            onDeliveryCompanyChange={ordersData.handleDeliveryCompanyFilter}
                            onProductChange={ordersData.handleProductFilter}
                            renderOnlyChips={true}
                        />
                    </View>

                    {/* Search Input Row - Only when visible */}
                    {showSearch && (
                        <View style={{ gap: 7, paddingHorizontal: 10 }}>
                            <OrdersSearch
                                isVisible={showSearch}
                                searchValue={ordersData.searchValue}
                                disabled={ordersData.loading}
                                onToggleVisibility={setShowSearch}
                                onSearchChange={ordersData.handleSearchFilter}
                                renderOnlyInput={true}
                            />
                        </View>
                    )}

                    {/* Advanced Filters Row - Only when visible */}
                    {showFilters && (
                        <View style={{ gap: 7, paddingHorizontal: 10 }}>
                            <OrdersFilters
                                isFiltersVisible={showFilters}
                                activeFilterChip={ordersData.activeFilterChip}
                                activeDeliveryCompany={ordersData.activeDeliveryCompany}
                                activeProductItem={ordersData.activeProductItem}
                                disabled={ordersData.loading}
                                onToggleFiltersVisibility={setShowFilters}
                                onStatusFilterChange={ordersData.handleStatusFilter}
                                onDeliveryCompanyChange={ordersData.handleDeliveryCompanyFilter}
                                onProductChange={ordersData.handleProductFilter}
                                renderOnlyAdvanced={true}
                            />
                        </View>
                    )}

                    <View style={{ paddingHorizontal: 10 }}>
                        <Divider />
                    </View>
                </View>

                {/* Orders List */}
                <OrdersList
                    orders={ordersData.orders}
                    loading={ordersData.loading}
                    noMore={ordersData.noMore}
                    selectionMode={performance.selectionMode}
                    selectedOrdersId={performance.selectedOrdersId}
                    filter={ordersData.filter}
                    onEndReached={ordersData.handleLoadMore}
                    onRefresh={ordersData.handleRefresh}
                    onSelectionChange={performance.handleSetSelectedOrdersId}
                    onDeleteOrder={(id) => performance.handleSetDeleteId(id, ordersData.orders)}
                    onModalVisibilityChange={performance.handleSetModalVisible}
                    onModalModeChange={performance.handleSetModalMode}
                    onSwipeableCloseSet={performance.handleSetSwipeableClose}
                />

                {/* Selection Mode Bottom Panel */}
                {performance.selectionMode && (
                    <View
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            paddingTop: 10,
                            paddingHorizontal: 15,
                            backgroundColor: colors.white,
                            opacity: performance.selectionMode ? 1 : 0,
                            borderTopLeftRadius: 10,
                            borderTopRightRadius: 10,
                            shadowOffset: { height: -2, width: 0 },
                            shadowColor: colors.gray[900],
                            shadowRadius: 5,
                            shadowOpacity: 0.15,
                            gap: 10,
                            elevation: 2,
                        }}
                    >
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '100%',
                                alignItems: 'center',
                            }}
                        >
                            <Text style={[typography.altText, typography.sm]}>
                                Selected Items: {performance.selectedOrdersId.length}
                            </Text>
                            <TouchableOpacity
                                onPress={() => performance.handleSetSelectedOrdersId([])}
                                style={{ flexDirection: 'row', gap: 5, alignItems: 'center' }}
                            >
                                <Text
                                    style={{
                                        color: colors.gray[600],
                                        fontFamily: typography.fontSemibold.fontFamily,
                                    }}
                                >
                                    Clear
                                </Text>
                                <CloseIcon size={20} color={colors.red[500]} />
                            </TouchableOpacity>
                        </View>
                        <View style={{ paddingHorizontal: 5 }}>
                            <Divider />
                        </View>
                    </View>
                )}

                {/* Selection Mode Action Buttons */}
                {performance.selectionMode && (
                    <View
                        style={{
                            position: 'absolute',
                            bottom: '10%',
                            right: 0,
                            backgroundColor: colors.white,
                            borderTopLeftRadius: 10,
                            borderBottomLeftRadius: 10,
                            shadowOffset: { height: -2, width: 0 },
                            shadowColor: colors.gray[900],
                            shadowRadius: 5,
                            shadowOpacity: 0.15,
                            padding: 10,
                            gap: 10,
                            elevation: 2,
                        }}
                    >
                        {/* Edit Button */}
                        {(user?.permissions === 'all' || user?.permissions?.order?.create) && (
                            <TouchableOpacity
                                style={{
                                    borderRadius: 100,
                                    padding: 5,
                                    gap: 2,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                                onPress={() => {
                                    performance.handleSetModalVisible(true);
                                    performance.handleSetModalMode('edit');
                                }}
                            >
                                <EditIcon size={28} color={colors.orange[400]} />
                                <Text
                                    style={{
                                        fontFamily: typography.altTextMedium.fontFamily,
                                        fontSize: typography.sm.fontSize,
                                        color: colors.orange[400],
                                    }}
                                >
                                    Edit
                                </Text>
                            </TouchableOpacity>
                        )}

                        {/* Send Button */}
                        {(sellmax?.lead === 'manual' || sellmax?.lead === 'auto') && (
                            <TouchableOpacity
                                style={{
                                    borderRadius: 100,
                                    padding: 5,
                                    gap: 2,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                                onPress={() => {
                                    performance.handleSetModalVisible(true);
                                    performance.handleSetModalMode('send');
                                }}
                            >
                                <SendIcon size={28} color={colors.blue[400]} />
                                <Text
                                    style={{
                                        fontFamily: typography.altTextMedium.fontFamily,
                                        fontSize: typography.sm.fontSize,
                                        color: colors.blue[400],
                                    }}
                                >
                                    Send
                                </Text>
                            </TouchableOpacity>
                        )}
                    </View>
                )}

                {/* Floating Action Button */}
                {(user?.permissions === 'all' || user?.permissions?.order?.create) && !performance.selectionMode && (
                    <View
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            right: 0,
                            zIndex: 1001,
                            elevation: 11,
                            pointerEvents: 'box-none',
                        }}
                    >
                        <FAB
                            color="orange"
                            size="sm"
                            height={0.6}
                            action={handleCreateOrder}
                        />
                    </View>
                )}

                {/* Orders List Modal */}
                <OrdersListModal
                    setLoading={() => {}} // Loading is handled by the hook now
                    visible={performance.modalVisible}
                    mode={performance.modalMode}
                    orderIds={performance.deleteId ? [performance.deleteId] : performance.selectedOrdersId}
                    setVisible={performance.handleSetModalVisible}
                    onSuccess={handleModalSuccess}
                    onClear={() => performance.handleSetSelectedOrdersId([])}
                    onCancel={handleModalCancel}
                />
            </GestureHandlerRootView>
        </OrdersContext.Provider>
    );
});

OrdersPage.displayName = 'OrdersPage';

export default OrdersPage;
