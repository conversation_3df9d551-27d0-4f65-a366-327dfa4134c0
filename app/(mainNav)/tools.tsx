import colors from "@styles/colors";
import React from "react";
import { ScrollView, View } from "react-native";
import ToolsPages from "@components/tools/ToolsPages";
import ToolsCard from "@components/tools/ToolsCard";
import { useStoreStore } from "../../store/storeStore";

const Tools = ({ navigation }: { navigation: any }) => {
    const { user, store } = useStoreStore();

    return (
        <View style={{ flexGrow: 1 }}>
            <ScrollView>
                <View
                    style={{
                        paddingHorizontal: 20,
                        paddingTop: 20,
                        gap: 24,
                        flexShrink: 0,
                        alignItems: "stretch",
                        backgroundColor: colors.gray[50],
                    }}
                >
                    <View style={{ gap: 10 }}>
                        {ToolsPages.filter((tool) => {
                            if (tool.route === "/presets") {
                                return (
                                    (user.permissions === "all" ||
                                        !tool.permission ||
                                        user.permissions[tool.permission]?.read) &&
                                    store.currency.code == "DZD"
                                );
                            }
                            return (
                                user.permissions === "all" ||
                                !tool.permission ||
                                user.permissions[tool.permission]?.read
                            );
                        }).map((card, key) => {
                            return (
                                <ToolsCard
                                    detail={card.content.detail}
                                    label={card.content?.label}
                                    order={key}
                                    variant={card.variant}
                                    key={key}
                                    navigation={navigation}
                                    route={card.route}
                                >
                                    {card.icon}
                                </ToolsCard>
                            );
                        })}
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

export default Tools;
