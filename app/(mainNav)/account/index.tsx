import { ScrollView, View, RefreshControl } from "react-native";
import AccountListContent from "@components/content/account/AccountListContent";
import BalanceCard from "@components/cards/account/BalanceCard";
import { useStoreStore } from "../../../store/storeStore";
import { useEffect } from "react";
import colors from "@styles/colors";
import { router } from "expo-router";

const account = ({ navigation }: { navigation: any }) => {
    const { store, getStore, auth } = useStoreStore();

    const getBalanceValue = () => {
        const { balance, outstandingBalance, billingRate } = store.paymentInfo;
        return ((balance - outstandingBalance) * (billingRate || 100)) / 100;
    };

    const refreshData = async () => {
        getStore();
    };

    useEffect(() => {
        if (auth === "failed") router.navigate("/network-error");
    }, [auth]);

    return (
        <ScrollView
            style={{
                backgroundColor: colors.gray[50],
            }}
            refreshControl={<RefreshControl refreshing={auth === "loading"} onRefresh={refreshData} />}
        >
            <View
                style={{
                    paddingHorizontal: 20,
                    paddingTop: 20,
                    gap: 24,
                    flexShrink: 0,
                    alignItems: "stretch",
                }}
            >
                <BalanceCard value={store.paymentInfo ? getBalanceValue() : 0} currency={store.currency?.code} />
                <AccountListContent navigation={navigation} />
            </View>
        </ScrollView>
    );
};

export default account;
