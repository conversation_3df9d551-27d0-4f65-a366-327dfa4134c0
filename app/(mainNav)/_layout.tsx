import MultiStoreSelector from "@components/MultiStoreSelector";
import AppHeader from "@components/Navigation/mainNav/AppHeader";
import BottomNav from "@components/Navigation/mainNav/BottomNav";
import colors from "@styles/colors";
import { Stack, Tabs } from "expo-router";
import React from "react";
import { Platform, View } from "react-native";
import { useStoreStore } from "../../store/storeStore";
import { useAuthStore } from "../../store/authStore";

export default function NavLayout() {
    return (
        <>
            <Stack.Screen
                options={{
                    headerShown: false,
                    // statusBarBackgroundColor: Platform.OS === "ios" ? undefined : colors.gray[50],
                }}
            />
            <View style={{ height: "100%" }}>
                <Tabs
                    initialRouteName="(home)"
                    screenOptions={{
                        sceneStyle: { backgroundColor: colors.gray[50] },
                        header: ({ navigation, route, layout, options }) => {
                            return (
                                // storeLoading ? null : (
                                <AppHeader layout={layout} navigation={navigation} options={options} route={route} />
                                // )
                            );
                        },
                    }}
                    tabBar={({ state, navigation, descriptors, insets }) => {
                        return (
                            <BottomNav
                                navigation={navigation}
                                state={state}
                                descriptors={descriptors}
                                insets={insets}
                            />
                        );
                    }}
                >
                    <Tabs.Screen name="scanner" options={{ headerShown: false }} />
                    <Tabs.Screen name="orders" />
                    <Tabs.Screen name="(home)" options={{ tabBarLabel: "home" }} />
                    <Tabs.Screen name="tools" />
                    <Tabs.Screen name="account" />
                </Tabs>
            </View>
            {/* Add the MultiStoreSelector component at the end of the AppHeader component */}
        </>
    );
}
