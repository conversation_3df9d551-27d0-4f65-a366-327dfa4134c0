import { ActivityIndicator, RefreshControl, ScrollView, StyleSheet, View, Text, AppState, Button } from "react-native";
import React, { useEffect, useState } from "react";
import colors from "@styles/colors";
import HomeCardBig from "@components/cards/home/<USER>";
import HomeCardSmall from "@components/cards/home/<USER>";
import HomeChartV2 from "@components/charts/HomeChart/HomeChartV2";
import api from "@api/api";
import { useStoreStore } from "../../../store/storeStore";
import StatsCard from "@components/stats/StatsCard";
import { Redirect, router } from "expo-router";
import Input from "@components/inputs/textInputs/Input";
import { typography } from "@styles/typography";
import Loading from "@components/pages/Loading";

export type HomeDataProps = {
    abandonedOrdersTotal: number;
    pendingOrdersTotal: number;
    totalDelivered: number;
    totalReturned: number;
    last7DaysDetailed: { date: Date; count: number; totalValue: number }[];
    dailyOrders: { count: number; totalValue: number };
    thisMonthOrders: { count: number; totalValue: number };
    thisWeekOrders: { count: number; totalValue: number };
    totalOrders: { count: number; totalValue: number };
};

const home = () => {
    const [appState, setAppState] = useState(AppState.currentState);
    const monthNames = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ];

    const [homeData, setHomeData] = useState({
        today: {
            totalValue: 0,
            count: 0,
        },
        yesterday: {
            totalValue: 0,
            count: 0,
        },
        thisWeek: {
            totalValue: 0,
            count: 0,
        },
        thisMonth: {
            totalValue: 0,
            count: 0,
        },
        total: {
            totalValue: 0,
            count: 0,
        },
        ordersData: [{ totalValue: 0, count: 0, date: new Date() }],
    });
    const [refreshing, setRefreshing] = useState(false);
    const [firstDate, setFirstDate] = useState<string>("");

    const { storeLoading, store, user } = useStoreStore();

    const getHomeData = () => {
        setRefreshing(() => true);
        api.get("/statistics/orders")
            .then((res: { success: boolean; data: HomeDataProps }) => {
                const { data } = res;
                const total = { ...data.totalOrders };
                const thisMonth = { ...data.thisMonthOrders };
                const thisWeek = { ...data.thisWeekOrders };

                const today = { ...data.dailyOrders };
                const ordersData = [...data.last7DaysDetailed];

                const yesterday =
                    ordersData.length !== 7
                        ? {
                              count: ordersData[ordersData.length - 1].count,
                              totalValue: ordersData[ordersData.length - 1].totalValue,
                          }
                        : {
                              count: ordersData[ordersData.length - 2].count,
                              totalValue: ordersData[ordersData.length - 2].totalValue,
                          };
                setFirstDate(
                    () => `${new Date(ordersData[0].date).getDate()}/${new Date(ordersData[0].date).getMonth() + 1}`
                );
                setHomeData({ total, thisMonth, thisWeek, yesterday, today, ordersData });
            })
            .catch((e) => {
                router.navigate({
                    pathname: "/network-error",
                    params: e,
                });
            })
            .finally(() => {
                setRefreshing(() => false);
            });
    };

    useEffect(() => {
        const subscription = AppState.addEventListener("change", (nextAppState) => {
            if (appState.match(/inactive|background/) && nextAppState === "active") {
                getHomeData(); // Refresh content when app comes to the foreground
            }
            setAppState(nextAppState);
        });

        return () => {
            subscription.remove();
        };
    }, [appState]);

    useEffect(() => {
        if (user?.permissions === "all" || user?.permissions?.statistics?.read) {
            getHomeData();
        }
    }, [user]);

    const refresh = () => {
        getHomeData();
    };

    // return <Redirect href={"/notificationSound"} />;

    return (
        <ScrollView
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={refresh}></RefreshControl>}
            overScrollMode="always"
            style={{ backgroundColor: colors.gray[50] }}
            contentContainerStyle={{
                minHeight: "100%",
                paddingHorizontal: 15,
                paddingVertical: 10,
                flexShrink: 0,
                gap: 20,
                alignItems: "stretch",
                backgroundColor: colors.gray[50],
            }}
        >
            {!refreshing && !storeLoading ? (
                user?.permissions === "all" || user?.permissions?.statistics?.read ? (
                    <>
                        <View
                            style={{
                                display: "flex",
                                gap: 10,
                                alignItems: "stretch",
                            }}
                        >
                            <HomeCardBig
                                title="Today"
                                date={`${new Date().getDate()}/${new Date().getMonth() + 1}`}
                                units={homeData.today.count}
                                value={homeData.today.totalValue}
                                currency={store.currency?.code}
                            />
                            <View
                                style={{
                                    display: "flex",
                                    flexDirection: "row",
                                    gap: 10,
                                }}
                            >
                                <HomeCardSmall
                                    title={"Yesterday"}
                                    date={`${new Date().getDate() - 1}/${new Date().getMonth() + 1}`}
                                    value={homeData.yesterday.totalValue}
                                    units={homeData.yesterday.count}
                                    currency={store.currency?.code}
                                    variant={"gray"}
                                />

                                <HomeCardSmall
                                    title="This week"
                                    date={`since ${firstDate}`}
                                    value={homeData.thisWeek.totalValue}
                                    units={homeData.thisWeek.count}
                                    currency={store.currency?.code}
                                    variant={"cyan"}
                                />
                            </View>
                            <View
                                style={{
                                    display: "flex",
                                    flexDirection: "row",
                                    gap: 10,
                                }}
                            >
                                <HomeCardSmall
                                    title={"This Month"}
                                    date={monthNames[new Date().getMonth()]}
                                    value={homeData.thisMonth.totalValue}
                                    units={homeData.thisMonth.count}
                                    currency={store.currency?.code}
                                    variant={"teal"}
                                />
                                <HomeCardSmall
                                    title={"Total"}
                                    date={""}
                                    value={homeData.total.totalValue}
                                    units={homeData.total.count}
                                    currency={store.currency?.code}
                                    variant={"orange"}
                                />
                            </View>
                        </View>
                        <StatsCard
                            style={{
                                borderWidth: 1,
                                borderColor: colors.gray[100],
                                padding: 10,
                                paddingTop: 15,
                                overflow: "visible",
                            }}
                        >
                            <HomeChartV2
                                axis="date"
                                dataSet={["count"]}
                                data={homeData.ordersData}
                                height={200}
                                nYticks={4}
                                columnWidth={42}
                                label={"Orders count for the last 7 days"}
                                colorsSet={["teal"]}
                            />
                        </StatsCard>
                        <StatsCard
                            style={{ borderWidth: 1, borderColor: colors.gray[100], padding: 10, overflow: "visible" }}
                        >
                            <HomeChartV2
                                axis="date"
                                dataSet={["totalValue"]}
                                data={homeData.ordersData}
                                height={200}
                                nYticks={4}
                                columnWidth={42}
                                label={"Orders total value for the last 7 days"}
                                colorsSet={["primary"]}
                            />
                        </StatsCard>
                    </>
                ) : (
                    <View style={{ height: "100%", justifyContent: "center", alignItems: "center" }}>
                        <Text style={{ textAlign: "center" }}>
                            You don't have the necessary permissions to view this page.
                        </Text>
                    </View>
                )
            ) : (
                <View style={{ height: "100%", justifyContent: "center", alignItems: "center" }}>
                    {/* <ActivityIndicator size="large" /> */}
                    <Loading />
                </View>
            )}
        </ScrollView>
    );
};

export default home;

const styles = StyleSheet.create({});
