import React, { useEffect, useState } from "react";
import ProfileForm from "@components/profile/ProfileForm";
import { useStoreStore } from "../../store/storeStore";
import { useAuthStore } from "../../store/authStore";
import { User } from "../../types/Store";

const Profile = () => {
    let { user } = useStoreStore();
    let { logout, updatePassword, updateProfile } = useAuthStore();

    return (
        <ProfileForm user={user as User} onSubmit={updateProfile} onPasswordSubmit={updatePassword} onLogout={logout} />
    );
};

export default Profile;
