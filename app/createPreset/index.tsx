import { View, Text, StyleSheet, ScrollView } from "react-native";
import React, { useCallback, useEffect, useState } from "react";
import Input from "@components/inputs/textInputs/Input";
import colors from "@styles/colors";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import { AddIcon } from "@components/icons/BudgetManagerIcons";
import Divider from "@components/dividers/Divider";
import { FlatList } from "react-native";
import { Stack, useFocusEffect, useLocalSearchParams } from "expo-router";
import Header from "@components/Navigation/Header";
import { Item } from "@app/editPreset";
import { useStoreStore } from "../../store/storeStore";
import { Preset, PresetsRecord } from "@app/presets";
import Toast from "react-native-toast-message";

export const names = [
    "",
    "Adrar",
    "Chlef",
    "Laghouat",
    "<PERSON>um El Bouaghi",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Tam<PERSON>rass<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Tlemcen",
    "Tiar<PERSON>",
    "Tizi Ouzou",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>lfa",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>if",
    "<PERSON><PERSON>",
    "Skikda",
    "<PERSON>i Bel <PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "Guelma",
    "<PERSON>",
    "<PERSON>dea",
    "<PERSON>aganem",
    "<PERSON>'<PERSON>la",
    "Mascara",
    "<PERSON>uarg<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON> <PERSON><PERSON>h",
    "<PERSON>li<PERSON>",
    "<PERSON>rdj <PERSON>u <PERSON><PERSON>idj",
    "<PERSON>umerdes",
    "El Tarf",
    "Tindouf",
    "Tissemsilt",
    "El Oued",
    "Khenchela",
    "Souk Ahras",
    "Tipaza",
    "Mila",
    "Ain Defla",
    "Naama",
    "Ain Temouchent",
    "Ghardaia",
    "Relizane",
    "Timimoun",
    "Bordj Badji Mokhtar",
    "Ouled Djellal",
    "Beni Abbes",
    "In Salah",
    "In Guezzam",
    "Touggourt",
    "Djanet",
    "El M ghaier",
    "El Meniaa",
];

const index = () => {
    const params: { preset: string; name: string } = useLocalSearchParams();
    const { store, updateStore } = useStoreStore();

    const [presetName, setPresetName] = useState(params.name ? params.name : "");
    const [preset, setPreset] = useState<Preset>();
    const [applyToAll, setapplyToAll] = useState<boolean>(false);
    const [applyValues, setApplyValues] = useState<{ stopdesk: number; delivery: number }>();

    const [data, setData] = useState<
        [
            string,
            {
                stopdesk: number;
                delivery: number;
            }
        ][]
    >([]);

    useEffect(() => {
        if (params.preset) {
            const _preset: Preset = JSON.parse(params.preset);
            const _data = [...Object.entries(_preset)];
            _data.unshift(["", { delivery: 0, stopdesk: 0 }]);
            setData([..._data]);
        } else {
            const _data = names.map((name) => {
                const result: [
                    string,
                    {
                        stopdesk: number;
                        delivery: number;
                    }
                ] = [name, { delivery: 0, stopdesk: 0 }];
                return result;
            });

            setData([..._data]);
        }
    }, []);

    const retransformData = () => {
        const _data = [...data];
        _data.shift();
        return Object.fromEntries(_data);
    };

    const updateData = (index: number, value: { stopdesk: number; delivery: number }) => {
        data[index] = [data[index][0], value];
        setData([...data]);
    };

    const update = async () => {
        if (presetName === "") throw new Error("Empty name");

        let integrations = [...store.integrations];
        let _index = 0;
        const presets: Record<string, PresetsRecord> = integrations.find((integration, index) => {
            _index = index;
            return integration.ref === "dz-wilaya-presets";
        }).fields?.presets;

        const presetAlreadyExist = Object.keys(presets).find((presetNameKey) => {
            return presetNameKey === presetName;
        });

        if (presetAlreadyExist) throw new Error("preset name already exisits, please select another name");

        integrations[_index].fields.presets[presetName] = retransformData();

        updateStore({ integrations, slug: store.slug });
    };

    return (
        <>
            <Stack.Screen
                options={{
                    headerTitle: "Create Preset",

                    header: (props) => {
                        return (
                            <Header
                                {...props}
                                modalButtonLabel="Save"
                                modalButtonAction={() => {
                                    update().catch((error) => {
                                        Toast.show({
                                            text1: "Error creating preset",
                                            text2: error.message,
                                            type: "error",
                                        });
                                    });
                                }}
                            />
                        );
                    },
                }}
            />
            <FlatList
                style={{ backgroundColor: colors.gray[50] }}
                contentContainerStyle={{ paddingHorizontal: 15, gap: 20 }}
                renderItem={({ item, index }) => {
                    return (
                        <Item
                            delivery={item[1].delivery}
                            index={index}
                            key={index}
                            name={presetName}
                            setPresetName={setPresetName}
                            stopdesk={item[1].stopdesk}
                            text={item[0]}
                            updateData={updateData}
                            setApplyValues={setApplyValues}
                            setapplyToAll={setapplyToAll}
                        />
                    );
                }}
                data={data}
            />
        </>
    );
};

export default index;

const styles = StyleSheet.create({
    label: {
        fontSize: 14,
        fontWeight: "bold",
        color: colors.gray[600],
        paddingBottom: 5,
    },
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        gap: 10,
        paddingVertical: 10,
    },
    column: {
        flex: 1,
    },
});
