import api from "@api/api";
import CategoryCard from "@components/cards/categories/CategoryCard";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import { FlatList, View } from "react-native";

const Categories = () => {
    const [categories, setCategories] = useState([]);

    useEffect(() => {
        api.get("/category")
            .then((res: any) => setCategories(res.data))
            .catch(() => {
                router.navigate("/network-error");
            });
    }, []);

    return (
        <View style={{ paddingLeft: 10, paddingRight: 10 }}>
            <FlatList
                data={categories}
                renderItem={({ item, index }) => <CategoryCard key={index} category={item} />}
                ItemSeparatorComponent={() => <View style={{ height: 10 }} />}
            />
        </View>
    );
};

export default Categories;
