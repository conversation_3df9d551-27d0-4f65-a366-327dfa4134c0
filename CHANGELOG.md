# Changelog

## [2.3.0] - 2025-01-23

### ✨ New Features

-   **Multi-Store Support**: Complete functionality for managing multiple stores with visual selector
-   **Order Restoration**: Restore deleted orders with real-time socket updates
-   **Enhanced Socket Connection**: Platform-specific reconnection strategies (iOS: exponential backoff, Android: 2s fixed delay)

### 🔧 Improvements

-   **Notifications**: Enhanced Android interceptor and store-specific preferences
-   **Block Customer**: Better UX with suspicious activity detection and order deletion options
-   **Order Management**: Improved state updates and list synchronization via socket events

### 🐛 Bug Fixes

-   Fixed socket reconnection reliability issues
-   Resolved multi-store notification synchronization problems
-   Fixed order restoration state update issues
-   Corrected Android notification interceptor problems
-   **Edit Product Modal**: Fixed input handling and state management issues
-   **Order Card**: Resolved performance and interaction problems
-   **Order Deletion**: Enhanced timeout handling and loading states

### 🛠️ Technical

-   Added comprehensive socket testing and debugging tools
-   Enhanced error handling and state management
-   Improved TypeScript types and component reusability
-   **OrderCard Optimization**: Implemented React.memo with custom comparison for better performance
-   **Code Cleanup**: Optimized component rendering and state management

---

## [2.2.7] - 2025-05-31

### ✨ New Features

-   **Block Customer**: Complete functionality to block customers and manage their orders
-   **Multi-Store Sellers**: Initial implementation of multi-store support
-   **iOS Autofill**: Password autofill support for iOS devices
-   **App Links**: Configured deep linking for Android and iOS (internal and external) [REQUIRES DEBUGGING]

### 🔧 Improvements

-   **Socket Connection**: Enhanced stability and reliability
-   **Order Management**: Improved socket updates for order operations (add, update, delete, bulk edit)
-   **Delivery Company**: Status-based enabling/disabling based on order status
-   **Cost Calculator**: Fixed bottom sheet functionality
-   **Network Error**: Improved background error handling

### 🐛 Bug Fixes

-   Resolved order list UI updates after order creation
-   Fixed confirmed order creation process
-   Corrected abandoned orders disappearance issue
-   Fixed password required characters validation
-   Resolved delivery company location issues
-   Fixed stats calculation problems
