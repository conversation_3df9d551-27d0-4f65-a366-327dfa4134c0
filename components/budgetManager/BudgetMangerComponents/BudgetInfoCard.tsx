import { CheckedIcon, EditIcon, UncheckedIcon, ViewAllIcon } from "@components/icons/BudgetManagerIcons";
import colors from "@styles/colors";
import React, { useEffect, useState } from "react";
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from "react-native";
import BudgetMangerModalContent from "./BudgetMangerModalContent";
import Modal from "@components/Navigation/ModalView/ModalView";
import useBudgetStore from "../../../store/budgetStore";
import budgetStore from "../../../store/budgetStore";
import BudgetEditInfoModalContent from "@components/budgetManager/BudgetMangerComponents/BudgetEditInfoModalContent";
import Input from "@components/inputs/textInputs/Input";
import { z } from "zod";
import { typography } from "@styles/typography";
import { Color } from "@components/charts/HomeChart/HomeChartV2";

const BudgetInfoCard = ({ budgetName, creationDate, budgetList, selectedBudget }) => {
    const [budgetListActiveModal, setBudgetListActiveModal] = useState(false);
    const [editBudgetActiveModal, setEditBudgetActiveModal] = useState(false);
    const [selectedBudgetState, setSelectedBudgetState] = useState(selectedBudget);
    const budgetStore = useBudgetStore();
    // const [initalBudgetError, setInitalBudgetError] = useState<string | null>(null);
    // const initialBudgetSchema = z.number().min(10,"Budget must be at least 10 TND");
    const [nameError, setNameError] = useState<string | null>(null);
    const [descriptionError, setDescriptionError] = useState<string | null>(null);
    return (
        <View style={styles.firstView} key={selectedBudget.name}>
            <Modal activeModal={budgetListActiveModal} setActiveModal={setBudgetListActiveModal}>
                <BudgetMangerModalContent
                    title="All Available Budgets"
                    setActiveModal={setBudgetListActiveModal}
                    action={() => {
                        budgetStore.setSelectedBudget(selectedBudgetState);
                    }}
                >
                    <ScrollView
                        style={{
                            width: "100%",
                        }}
                    >
                        <View
                            style={{
                                width: "100%",
                            }}
                        >
                            {budgetList.map((budget, key) => (
                                <View
                                    key={key}
                                    style={{
                                        flexDirection: "row",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        padding: 15,
                                        backgroundColor:
                                            selectedBudgetState._id === budget._id ? colors.gray[200] : "transparent",
                                        borderRadius: 10,
                                    }}
                                >
                                    <Text>{budget.name}</Text>
                                    <TouchableOpacity
                                        onPress={() => {
                                            setSelectedBudgetState(budget);
                                        }}
                                    >
                                        {selectedBudgetState._id === budget._id ? <CheckedIcon /> : <UncheckedIcon />}
                                    </TouchableOpacity>
                                </View>
                            ))}
                        </View>
                    </ScrollView>
                </BudgetMangerModalContent>
            </Modal>
            <Modal activeModal={editBudgetActiveModal} setActiveModal={setEditBudgetActiveModal}>
                <BudgetEditInfoModalContent
                    title={"Edit Budget Info"}
                    setActiveModal={setEditBudgetActiveModal}
                    action={async () => {
                        await budgetStore
                            .updateBudget(budgetStore.selectedBudget, {
                                ...selectedBudgetState,
                            })
                            .finally(() => {});
                    }}
                >
                    <View
                        style={{
                            width: "100%",
                            flex: 1,
                            flexDirection: "column",
                        }}
                    >
                        <Input
                            isValid={nameError === null}
                            onChange={(text) => {
                                const nameSchema = z.string().min(3, "Name must be at least 3 characters long");
                                const result = nameSchema.safeParse(text);
                                if (!result.success) {
                                    setNameError(result.error.errors[0].message);
                                } else {
                                    setNameError(null);
                                    setSelectedBudgetState({
                                        ...selectedBudgetState,
                                        name: text,
                                    });
                                }
                            }}
                            placeholder={"Name: " + selectedBudget.name}
                        />
                        <View style={{ opacity: nameError ? 1 : 0, paddingVertical: 2 }}>
                            <Text style={{ color: colors.red[500], fontSize: typography.xs.fontSize }}>
                                {nameError}
                            </Text>
                        </View>
                        <Input
                            isValid={descriptionError === null}
                            onChange={(text) => {
                                const dscriptionSchema = z
                                    .string()
                                    .min(3, "Description must be at least 3 characters long");
                                const result = dscriptionSchema.safeParse(text);
                                if (!result.success) {
                                    setDescriptionError(result.error.errors[0].message);
                                } else {
                                    setDescriptionError(null);
                                    setSelectedBudgetState({
                                        ...selectedBudgetState,
                                        description: text,
                                    });
                                }
                            }}
                            placeholder={"Description: " + selectedBudget.description}
                        />
                        <View style={{ opacity: descriptionError ? 1 : 0, paddingVertical: 2 }}>
                            <Text style={{ color: colors.red[500], fontSize: typography.xs.fontSize }}>
                                {descriptionError}
                            </Text>
                        </View>
                    </View>
                </BudgetEditInfoModalContent>
            </Modal>
            <View style={styles.column}>
                <View style={{ alignSelf: "stretch" }}>
                    <View style={styles.row}>
                        <Text style={styles.budgetDate}>{"Budget Name"}</Text>
                        <Text style={styles.budgetName}>{budgetName}</Text>
                    </View>
                    <View style={styles.row}>
                        <Text style={styles.budgetDate}>{"Creation Date"}</Text>
                        <Text style={styles.budgetName}>{creationDate}</Text>
                    </View>
                </View>
                <View style={{ flexDirection: "row", justifyContent: "space-between", width: "100%", gap: 20 }}>
                    <FirstCardButton
                        color="gray"
                        Icon={({ color, size }) => <EditIcon color={color} />}
                        label="Edit Details"
                        onPress={() => {
                            setEditBudgetActiveModal(true);
                        }}
                    />
                    <FirstCardButton
                        color="gray"
                        Icon={({ color, size }) => <ViewAllIcon color={color} />}
                        label="Budgets"
                        onPress={() => {
                            setBudgetListActiveModal(true);
                        }}
                    />
                </View>
            </View>
        </View>
    );
};

const FirstCardButton = ({
    Icon,
    label,
    color,
    onPress,
}: {
    Icon: React.FC<{ color?: string; size?: number }>;
    label: string;
    color: Color;
    onPress: () => any;
}) => {
    return (
        <TouchableOpacity
            style={{
                flexDirection: "row",
                flex: 1,
                borderRadius: 5,
                backgroundColor: colors[color][200],
                // borderWidth: 1,
                gap: 10,
                paddingLeft: 15,
                paddingRight: 10,
                paddingVertical: 5,
                alignItems: "center",
                justifyContent: "space-between",
            }}
            onPress={onPress}
        >
            <Text style={[styles.buttonLabel, { color: colors[color][600] }]}>{label}</Text>
            <Icon color={colors[color][600]} />
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "baseline",
        alignSelf: "stretch",
    },
    column: {
        flexDirection: "column",
        justifyContent: "space-between",
        alignItems: "flex-start",
        gap: 10,
    },
    firstView: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderColor: colors.gray[400],
        borderWidth: 1,
        borderRadius: 10,
    },
    budgetName: {
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.lg.fontSize,
        color: colors.gray[600],
    },
    budgetDate: {
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: typography.sm.fontSize,
        color: colors.gray[600],
    },
    buttonLabel: {
        textTransform: "capitalize",
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: typography.sm.fontSize,
        color: colors.gray[600],
    },
});

export default BudgetInfoCard;
