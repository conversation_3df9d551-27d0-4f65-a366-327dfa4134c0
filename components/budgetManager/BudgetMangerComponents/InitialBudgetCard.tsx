import React, { useState } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import BudgetMangerModalContent from "./BudgetMangerModalContent";
import Modal from "@components/Navigation/ModalView/ModalView";
import Input from "@components/inputs/textInputs/Input";
import { EditIcon } from "@components/icons/BudgetManagerIcons";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import budgetStore from "../../../store/budgetStore";
import { z } from "zod";
import { useStoreStore } from "../../../store/storeStore";

const InitialBudgetCard = ({
    initialBudget,
    currency,
    handleAction,
}: {
    initialBudget: number;
    currency: string;
    handleAction: (value: any) => void;
}) => {
    const [initialBudgetActiveModal, setinitialBudgetActiveModal] = useState(false);
    const [initialBudgetError, setinitialBudgetError] = useState<string | null>(null);
    const { store } = useStoreStore();
    return (
        <>
            <View style={styles.card}>
                <Modal activeModal={initialBudgetActiveModal} setActiveModal={setinitialBudgetActiveModal}>
                    <BudgetMangerModalContent
                        title="Edit Initial Budget"
                        setActiveModal={setinitialBudgetActiveModal}
                        action={() => {
                            handleAction(budgetStore.getState().initialBudget);
                        }}
                    >
                        <Input
                            isValid={initialBudgetError === null}
                            onChange={(e) => {
                                const inittialBudgetSchma = z
                                    .number()
                                    .min(0, { message: "Initial Budget must be greater than 0" });
                                let result = inittialBudgetSchma.safeParse(Number.parseFloat(e));
                                if (!result.success) {
                                    setinitialBudgetError(result.error.errors[0].message);
                                } else {
                                    setinitialBudgetError(null);
                                    budgetStore.setState({ initialBudget: parseInt(e) });
                                }
                            }}
                            label="Value"
                            placeholder={`${initialBudget}`}
                            icon={
                                <Text
                                    style={{
                                        fontFamily: typography.fontSemibold.fontFamily,
                                        fontSize: typography.xs.fontSize,
                                        color: colors.gray[600],
                                    }}
                                >
                                    {store.currency?.code}
                                </Text>
                            }
                            inputProps={{
                                inputMode: "decimal",
                            }}
                        />
                        <Text
                            style={{
                                fontFamily: typography.fontMedium.fontFamily,
                                fontSize: typography.xs.fontSize,
                                color: colors.red[500],
                                paddingVertical: 10,
                            }}
                        >
                            {initialBudgetError}
                        </Text>
                    </BudgetMangerModalContent>
                </Modal>
                <View>
                    <Text style={styles.content}>Initial Budget</Text>
                    <Text style={[styles.content, { fontSize: 20 }]}>
                        {initialBudget}
                        <Text style={styles.currency}>{currency}</Text>
                    </Text>
                </View>
                <TouchableOpacity
                    onPress={() => {
                        setinitialBudgetActiveModal(true);
                    }}
                >
                    <EditIcon></EditIcon>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default InitialBudgetCard;

const styles = StyleSheet.create({
    card: {
        flexDirection: "row",
        paddingHorizontal: 20,
        paddingVertical: 15,
        justifyContent: "space-between",
        alignItems: "center",
        borderColor: colors.blue[400],
        borderWidth: 1,
        borderRadius: 10,
    },
    content: {
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.lg.fontSize,
        color: colors.blue[500],
    },
    currency: {
        fontSize: typography.sm.fontSize,
    },
});
