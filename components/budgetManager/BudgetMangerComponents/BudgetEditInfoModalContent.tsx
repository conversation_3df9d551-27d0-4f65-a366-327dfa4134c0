import Button from "@components/inputs/buttons/Button";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

const BudgetEditInfoModalContent = ({
    title,
    type,
    setActiveModal,
    children,
    variant = "normal",
    action = () => {},
}: {
    title: string;
    type?: "edit" | "add";
    setActiveModal: React.Dispatch<React.SetStateAction<boolean>>;
    children: React.ReactNode;
    variant?: "delete" | "normal";
    action?: () => void;
}) => {
    return (
        <View style={styles.container}>
            <Text style={styles.title}>{title}</Text>
            {children}
            <View style={styles.actions}>
                <Button
                    label="cancel"
                    type="text"
                    variant="gray"
                    action={() => {
                        setActiveModal(false);
                    }}
                />
                {variant === "normal" && (
                    <Button
                        label="confirm"
                        type="filled"
                        variant="primary"
                        action={() => {
                            action();
                            setActiveModal(false);
                        }}
                    />
                )}
                {variant === "delete" && (
                    <Button
                        label="delete"
                        type="filled"
                        variant="red"
                        action={() => {
                            action();
                            setActiveModal(false);
                        }}
                    />
                )}
            </View>
        </View>
    );
};

export default BudgetEditInfoModalContent;

const styles = StyleSheet.create({
    container: {
        alignItems: "center",
        gap: 20,
        justifyContent: "space-between",
    },
    title: {
        color: colors.gray[600],
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.md.fontSize,
        lineHeight: 24,
        textTransform: "capitalize",
    },
    actions: {
        flexDirection: "row",
        alignSelf: "stretch",
        justifyContent: "flex-end",
        gap: 5,
    },
});
