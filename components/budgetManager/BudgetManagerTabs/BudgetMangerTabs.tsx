import React, { useState } from "react";
import { StyleSheet, View } from "react-native";

import TabsContent from "./TabsComponents/TabsContent";
import TabsHeader from "./TabsComponents/TabsHeader";
import useBudgetStore from "../../../store/budgetStore";

type BudgetMangerTabsProps = {
    children?: React.ReactNode;
};

const BudgetMangerTabs = ({ children }: BudgetMangerTabsProps) => {
    const budgetState = useBudgetStore();

    return (
        <View style={styles.tabs} key={budgetState.selectedBudget._id}>
            <TabsHeader activeTab={budgetState.activeTab} />
            {children}
            <TabsContent activeTab={budgetState.activeTab} />
        </View>
    );
};

export default BudgetMangerTabs;

const styles = StyleSheet.create({
    tabs: { gap: 10, paddingBottom: "60%" },
});
