import React, { useState } from "react";
import { StyleSheet, View, Text } from "react-native";
import TabsContentRow from "./TansContentRow";
import ModalView from "@components/Navigation/ModalView/ModalView";
import BudgetMangerModalContent from "@components/budgetManager/BudgetMangerComponents/BudgetMangerModalContent";
import Input from "@components/inputs/textInputs/Input";
import Button from "@components/inputs/buttons/Button";
import Divider from "@components/dividers/Divider";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import useBudgetStore from "../../../../store/budgetStore";
import budgetStore from "../../../../store/budgetStore";
import { z } from "zod";

type TabsContentProps = {
    activeTab: number;
};

const TabsContent = ({ activeTab }: TabsContentProps) => {
    const [rowAddModal, setRowAddModal] = useState(false);

    const cashflow =
        budgetStore.getState().activeTab === 0
            ? budgetStore.getState().selectedBudget.revenues
            : budgetStore.getState().selectedBudget.expenses;

    const [newCategoryName, setCategoryName] = useState("");

    const budgetState = useBudgetStore();

    const categoryNameSchema = z
        .string()
        .min(5, "Category name must be at least 5 characters long")
        .max(20, "Category name must be at most 20 characters long");

    const [categoryNameError, setCategoryNameError] = useState<string | null>(null);

    return (
        <View style={styles.contentCard} key={budgetStore.getState().activeTab}>
            <ModalView activeModal={rowAddModal} setActiveModal={setRowAddModal}>
                <BudgetMangerModalContent
                    title="Add Category"
                    setActiveModal={setRowAddModal}
                    action={() => {
                        const updatedBudget = { ...budgetState.selectedBudget };
                        const category: any = {
                            name: newCategoryName,
                            subCategories: [],
                        };

                        if (budgetState.activeTab === 0) {
                            updatedBudget.revenues = [...updatedBudget.revenues, category];
                        } else {
                            updatedBudget.expenses = [...updatedBudget.expenses, category];
                        }
                        budgetState.updateBudget(budgetState.selectedBudget, {
                            ...updatedBudget,
                        });
                    }}
                >
                    <Input
                        isValid={categoryNameError === null}
                        label="Name"
                        placeholder={`Enter Category Name`}
                        inputProps={{ inputMode: "text" }}
                        onChange={(e) => {
                            const result = categoryNameSchema.safeParse(e);
                            if (!result.success) {
                                setCategoryNameError(result.error.errors[0].message);
                            } else {
                                setCategoryNameError(null);
                            }
                            setCategoryName(e);
                        }}
                    />
                    {categoryNameError && (
                        <View style={{ opacity: categoryNameError ? 1 : 0, paddingVertical: 2 }}>
                            <Text style={{ color: colors.red[500], fontSize: typography.xs.fontSize }}>
                                {categoryNameError}
                            </Text>
                        </View>
                    )}
                </BudgetMangerModalContent>
            </ModalView>
            {cashflow?.map((Category, index) => {
                return (
                    <View key={index}>
                        <TabsContentRow category={Category} indexRow={index} row={Category} />

                        {index + 1 < cashflow?.length && <Divider></Divider>}
                    </View>
                );
            })}
            <Button
                label="Add Category"
                action={() => {
                    setRowAddModal(true);
                }}
                type="outlined"
                variant="primary"
            />
        </View>
    );
};

export default TabsContent;
const styles = StyleSheet.create({
    headerCell: { flex: 1, alignItems: "center" },
    headerLabel: {
        color: colors.green[600],
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.md.fontSize,
    },
    contentCard: {
        gap: 5,
        paddingHorizontal: 15,
        paddingVertical: 10,
        alignItems: "stretch",

        alignSelf: "stretch",
        borderRadius: 10,
        borderWidth: 1,
        borderColor: colors.gray[300],
        backgroundColor: colors.white,
    },
    contentRow: {
        flexDirection: "row",
        alignSelf: "stretch",
        paddingVertical: 5,
    },
});
