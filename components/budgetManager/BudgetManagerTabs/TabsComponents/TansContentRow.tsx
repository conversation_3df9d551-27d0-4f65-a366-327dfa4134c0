import React, { useState } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import TabsContentRowCell from "./TabsContentRowCell";
import ModalView from "@components/Navigation/ModalView/ModalView";
import BudgetMangerModalContent from "@components/budgetManager/BudgetMangerComponents/BudgetMangerModalContent";
import Input from "@components/inputs/textInputs/Input";
import { typography } from "@styles/typography";
import colors from "@styles/colors";
import {
    CalendarDayIcon,
    TabsContentCellDelete,
    TabsContentRowAdd,
    TabsRowExpandIcon,
} from "@components/icons/BudgetManagerIcons";
import useBudgetStore from "../../../../store/budgetStore";
import { z } from "zod";
import { useStoreStore } from "../../../../store/storeStore";

export type Budget = {
    expenses?: Expense[];
    revenues?: Revenue[];
};

export type Expense = {
    name: string;
    subCategories: SubCategory[];
};

export type Revenue = {
    name: string;
    subCategories: SubCategory[];
};

export type SubCategory = {
    name: string;
    amount: number;
    createdAt: string; // Changed to string to match the input format
};

const TabsContentRow = ({
    indexRow,
    row,
    currency = "TND",
    category,
}: {
    indexRow: number;
    row: any;
    currency?: string;
    category: any;
}) => {
    const { store } = useStoreStore();
    const [collapsed, setCollapsed] = useState(true);
    const [rowDeleteModal, setRowDeleteModal] = useState(false);
    const [rowAddModal, setRowAddModal] = useState(false);
    const [newSubCategory, setNewSubCategory] = useState<SubCategory>({
        name: "",
        amount: 0,
        createdAt: "",
    });
    const budgetStore = useBudgetStore();

    const subCategorySchema = z.object({
        name: z.string().min(5, "Subcategory name must be at least 5 characters long"),
        amount: z.number().min(0, "Subcategory value must be at least 0"),
        createdAt: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Subcategory date must be in the format YYYY-MM-DD"),
    });

    const [subCategoryErrors, setSubCategoryErrors] = useState<{
        name?: string;
        amount?: string;
        createdAt?: string;
    }>({});

    const handleInputChange = (field, value) => {
        setNewSubCategory((prevState) => ({
            ...prevState,
            [field]: value,
        }));

        const result = subCategorySchema.safeParse({ ...newSubCategory, [field]: value });

        if (!result.success) {
            const error = result.error.errors.find((error) => error.path[0] === field);
            setSubCategoryErrors((prevState) => ({
                ...prevState,
                [field]: error?.message,
            }));
        } else {
            setSubCategoryErrors((prevState) => ({
                ...prevState,
                [field]: undefined,
            }));
        }
    };

    const handleAddSubCategory = async () => {
        if (
            subCategoryErrors.name ||
            subCategoryErrors.amount ||
            subCategoryErrors.createdAt ||
            !newSubCategory.createdAt
        ) {
            return;
        }

        let found = null;

        const updatedBudget = { ...budgetStore.selectedBudget };

        if (budgetStore.activeTab === 0) {
            found = updatedBudget.revenues.find((element) => element._id == category._id);
        } else {
            found = updatedBudget.expenses.find((element) => element._id == category._id);
        }

        if (budgetStore.activeTab === 0) {
            updatedBudget.revenues = updatedBudget.revenues.map((revenue) => {
                if (revenue._id === found._id) {
                    return {
                        ...revenue,
                        subCategories: [
                            ...revenue.subCategories,
                            {
                                name: newSubCategory.name,
                                amount: newSubCategory.amount,
                                createdAt: newSubCategory.createdAt,
                            },
                        ],
                    };
                }
                return revenue;
            });
        } else {
            updatedBudget.expenses = updatedBudget.expenses.map((expense) => {
                if (expense._id === found._id) {
                    return {
                        ...expense,
                        subCategories: [
                            ...expense.subCategories,
                            {
                                name: newSubCategory.name,
                                amount: newSubCategory.amount,
                                createdAt: newSubCategory.createdAt,
                            },
                        ],
                    };
                }
                return expense;
            });
        }

        await budgetStore.updateBudget(budgetStore.selectedBudget, {
            ...updatedBudget,
        });

        setNewSubCategory({
            name: "",
            amount: 0,
            createdAt: "",
        });
    };

    const handleDeleteCategory = async () => {
        try {
            const updatedBudget = { ...budgetStore.selectedBudget };
            if (budgetStore.activeTab === 0) {
                updatedBudget.revenues = updatedBudget.revenues.filter((r) => r._id !== category._id);
            } else {
                updatedBudget.expenses = updatedBudget.expenses.filter((e) => e._id !== category._id);
            }
            await budgetStore.updateBudget(updatedBudget, updatedBudget);
            setRowDeleteModal(false);
        } catch (error) {
            console.error("Error Deleting Category", error);
        }
    };

    return (
        <View style={{ alignSelf: "stretch", justifyContent: "flex-start" }}>
            <ModalView activeModal={rowDeleteModal} setActiveModal={setRowDeleteModal}>
                <BudgetMangerModalContent
                    title="Delete Category"
                    setActiveModal={setRowDeleteModal}
                    variant="delete"
                    action={handleDeleteCategory}
                >
                    <Text style={{ flex: 1, height: "100%" }}>Are you sure you want to delete the Category?</Text>
                </BudgetMangerModalContent>
            </ModalView>
            <ModalView activeModal={rowAddModal} setActiveModal={setRowAddModal}>
                <BudgetMangerModalContent
                    title="Add SubCategory"
                    setActiveModal={setRowAddModal}
                    action={handleAddSubCategory}
                >
                    <Input
                        isValid={subCategoryErrors.name === undefined}
                        label="Name"
                        placeholder="Enter SubCategory Name"
                        onChange={(e) => handleInputChange("name", e)}
                        inputProps={{ inputMode: "text" }}
                    />
                    <View style={{ paddingVertical: 1 }}>
                        <Text
                            style={{
                                fontFamily: typography.fontMedium.fontFamily,
                                fontSize: typography.xs.fontSize,
                                color: colors.red[500],
                            }}
                        >
                            {subCategoryErrors.name}
                        </Text>
                    </View>
                    <Input
                        isValid={subCategoryErrors.amount === undefined}
                        label="Value"
                        placeholder="Enter SubCategory Value"
                        onChange={(e) => handleInputChange("amount", parseFloat(e))}
                        inputProps={{ inputMode: "numeric" }}
                        icon={
                            <Text
                                style={{
                                    fontFamily: typography.fontSemibold.fontFamily,
                                    fontSize: typography.xs.fontSize,
                                    color: colors.gray[600],
                                }}
                            >
                                {store.currency?.code}
                            </Text>
                        }
                    />
                    <View style={{ paddingVertical: 2 }}>
                        <Text
                            style={{
                                fontFamily: typography.fontMedium.fontFamily,
                                fontSize: typography.xs.fontSize,
                                color: colors.red[500],
                            }}
                        >
                            {subCategoryErrors.amount}
                        </Text>
                    </View>
                    <Input
                        isValid={subCategoryErrors.createdAt === undefined}
                        label="Date"
                        placeholder="YYYY-MM-DD"
                        onChange={(e) => handleInputChange("createdAt", e)}
                        inputProps={{ inputMode: "text" }}
                        icon={<CalendarDayIcon color={colors.gray[600]} />}
                    />
                    <View style={{ paddingVertical: 2 }}>
                        <Text
                            style={{
                                fontFamily: typography.fontMedium.fontFamily,
                                fontSize: typography.xs.fontSize,
                                color: colors.red[500],
                            }}
                        >
                            {subCategoryErrors.createdAt}
                        </Text>
                    </View>
                </BudgetMangerModalContent>
            </ModalView>
            <TouchableOpacity
                style={{
                    alignSelf: "stretch",
                    alignItems: "flex-end",
                }}
                onPress={() => setCollapsed(!collapsed)}
            >
                <View style={styles.contentRow}>
                    <View
                        style={{
                            flex: 1,
                            gap: 10,
                            flexDirection: "row",
                            alignItems: "center",
                        }}
                    >
                        <TabsRowExpandIcon />
                        <Text
                            style={{
                                fontFamily: typography.fontMedium.fontFamily,
                                fontSize: typography.sm.fontSize,
                                color: colors.gray[900],
                            }}
                        >
                            {row.name}
                        </Text>
                    </View>
                    {collapsed ? (
                        <Text
                            style={{
                                paddingVertical: 4,
                                fontFamily: typography.fontMedium.fontFamily,
                                fontSize: typography.sm.fontSize,
                                color: colors.gray[900],
                            }}
                        >
                            {row.subCategories?.reduce((acc, subCategory) => acc + subCategory.amount, 0)}
                            <Text style={{ fontSize: typography.xxs.fontSize }}>{currency}</Text>
                        </Text>
                    ) : (
                        <TouchableOpacity
                            onPress={() => setRowAddModal(true)}
                            style={{
                                borderColor: colors.primary[600],
                                borderWidth: 1,
                                borderRadius: 5,
                                paddingHorizontal: 8,
                                paddingVertical: 4,
                                gap: 5,
                                flexDirection: "row",
                                alignItems: "center",
                            }}
                        >
                            <TabsContentRowAdd />
                            <Text
                                style={{
                                    fontFamily: typography.fontSemibold.fontFamily,
                                    color: colors.primary[600],
                                    textTransform: "capitalize",
                                }}
                            >
                                Add
                            </Text>
                        </TouchableOpacity>
                    )}
                </View>
            </TouchableOpacity>
            {!collapsed && (
                <View style={{ paddingHorizontal: 5, paddingBottom: 5, gap: 10 }}>
                    {row.subCategories.map((subCategory, indexCell) => (
                        <TabsContentRowCell
                            indexCell={indexCell}
                            indexRow={indexRow}
                            subCategory={subCategory}
                            key={indexCell}
                            category={category}
                        />
                    ))}
                    <View
                        style={{
                            flexDirection: "row",
                            alignSelf: "stretch",
                            alignItems: "center",
                        }}
                    >
                        <TouchableOpacity
                            onPress={() => setRowDeleteModal(true)}
                            style={{
                                padding: 5,
                                alignItems: "center",
                                justifyContent: "center",
                            }}
                        >
                            <TabsContentCellDelete />
                        </TouchableOpacity>
                        <Text
                            style={{
                                flex: 1,
                                fontFamily: typography.fontSemibold.fontFamily,
                                fontSize: typography.sm.fontSize,
                                color: colors.gray[900],
                            }}
                        >
                            Total
                        </Text>
                        <Text
                            style={{
                                fontFamily: typography.fontSemibold.fontFamily,
                                fontSize: typography.sm.fontSize,
                                color: colors.gray[900],
                            }}
                        >
                            {row.subCategories?.reduce((acc, subCategory) => acc + subCategory.amount, 0)}
                            <Text
                                style={{
                                    fontSize: typography.xxs.fontSize,
                                    fontFamily: typography.fontMedium.fontFamily,
                                }}
                            >
                                {store.currency?.code}
                            </Text>
                        </Text>
                    </View>
                </View>
            )}
        </View>
    );
};

export default TabsContentRow;

const styles = StyleSheet.create({
    headerCell: { flex: 1, alignItems: "center" },
    headerLabel: {
        color: colors.green[600],
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.md.fontSize,
    },
    contentCard: {
        gap: 5,
        paddingHorizontal: 15,
        paddingVertical: 10,
        alignItems: "stretch",
        alignSelf: "stretch",
        borderRadius: 10,
        borderWidth: 1,
        borderColor: colors.gray[300],
        backgroundColor: colors.white,
    },
    contentRow: {
        flexDirection: "row",
        alignSelf: "stretch",
        paddingVertical: 5,
    },
});
