import React, { useEffect, useState } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import ModalView from "@components/Navigation/ModalView/ModalView";
import BudgetMangerModalContent from "@components/budgetManager/BudgetMangerComponents/BudgetMangerModalContent";
import { typography } from "@styles/typography";
import colors from "@styles/colors";
import { CalendarDayIcon, TabsContentCellDelete } from "@components/icons/BudgetManagerIcons";
import useBudgetStore, { CashFlowProps, CategoryProps, SubCategoryProps } from "../../../../store/budgetStore";
import budgetStore from "../../../../store/budgetStore";

const TabsContentRowCell = ({
    indexCell,
    indexRow,
    subCategory,
    currency = "TND",
    category,
}: {
    indexCell: number;
    indexRow: number;
    subCategory: any;
    currency?: string;
    category: any;
}) => {
    const [cellActiveModal, setCellActiveModal] = useState(false);
    const budgetState = useBudgetStore();
    return (
        <View
            style={{
                flexDirection: "row",
                alignSelf: "stretch",
                alignItems: "center",
                justifyContent: "space-between",
            }}
        >
            <ModalView activeModal={cellActiveModal} setActiveModal={setCellActiveModal}>
                <BudgetMangerModalContent
                    title="Delete Category"
                    setActiveModal={setCellActiveModal}
                    variant="delete"
                    action={async () => {
                        const updatedBudget = { ...budgetState.selectedBudget };
                        const updatedCategory = { ...category };
                        updatedCategory.subCategories = updatedCategory.subCategories.filter(
                            (subCategory: any) => subCategory._id !== subCategory._id
                        );
                        updatedBudget.revenues = updatedBudget.revenues.map((category: any) =>
                            category._id === updatedCategory._id ? updatedCategory : category
                        );
                        updatedBudget.expenses = updatedBudget.expenses.map((category: any) =>
                            category._id === updatedCategory._id ? updatedCategory : category
                        );
                        await budgetState.updateBudget(budgetState.selectedBudget, {
                            ...updatedBudget,
                        });
                    }}
                >
                    <Text style={{ flex: 1, height: "100%" }}>
                        Are you sure you want to delete the the SubCategory?
                    </Text>
                </BudgetMangerModalContent>
            </ModalView>
            <View>
                <Text
                    style={{
                        fontFamily: typography.fontMedium.fontFamily,
                        fontSize: typography.sm.fontSize,
                        color: colors.gray[900],
                    }}
                >
                    {subCategory.name}
                </Text>
                <View
                    style={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 5,
                        paddingVertical: 5,
                    }}
                >
                    <CalendarDayIcon color={colors.gray[500]} />

                    <Text
                        style={{
                            fontFamily: typography.fontNormal.fontFamily,
                            fontSize: typography.sm.fontSize,
                            color: colors.gray[500],
                        }}
                    >
                        {subCategory.createdAt ? new Date(subCategory.createdAt).toLocaleDateString() : "-"}
                    </Text>
                </View>
            </View>
            <View style={{ flexDirection: "row", gap: 5, alignItems: "center" }}>
                <Text
                    style={{
                        fontFamily: typography.fontMedium.fontFamily,
                        fontSize: typography.sm.fontSize,
                        color: colors.gray[900],
                    }}
                >
                    {subCategory.value}
                    <Text style={{ fontSize: typography.xxs.fontSize }}>{currency}</Text>
                </Text>
                <TouchableOpacity
                    onPress={() => {
                        setCellActiveModal(true);
                    }}
                >
                    <TabsContentCellDelete />
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default TabsContentRowCell;
const styles = StyleSheet.create({
    headerCell: { flex: 1, alignItems: "center" },
    headerLabel: {
        color: colors.green[600],
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.md.fontSize,
    },
    contentCard: {
        gap: 5,
        paddingHorizontal: 15,
        paddingVertical: 10,
        alignItems: "stretch",

        alignSelf: "stretch",
        borderRadius: 10,
        borderWidth: 1,
        borderColor: colors.gray[300],
        backgroundColor: colors.white,
    },
    contentRow: {
        flexDirection: "row",
        alignSelf: "stretch",
        paddingVertical: 5,
    },
});
