import { ExpenseIcon, RevenueIcon } from "@components/icons/BudgetManagerIcons";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Animated, { Easing, useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated";
import useBudgetStore from "../../../../store/budgetStore";

const TabsHeader = ({ activeTab }: { activeTab: number }) => {
    const width = useSharedValue("0%");
    const indicatorAnimatedStyles = useAnimatedStyle(() => ({
        backgroundColor: colors[activeTab === 0 ? "green" : "red"][500],
    }));
    indicatorAnimatedStyles;
    const budgetState = useBudgetStore();

    const handlePress = (tab: number) => () => {
        if (budgetState.activeTab !== tab) {
            if (activeTab === 0) {
                budgetState.setActiveTab(1);
                width.value = withTiming("50%", {
                    duration: 200,
                    easing: Easing.inOut(Easing.cubic),
                });
            } else {
                budgetState.setActiveTab(0);
                width.value = withTiming("0%", {
                    duration: 200,
                    easing: Easing.inOut(Easing.cubic),
                });
            }
        }
    };
    return (
        <View style={{ gap: 5, paddingTop: 10 }}>
            <View style={{ flexDirection: "row" }}>
                <TouchableOpacity activeOpacity={0.5} style={styles.cell} onPress={handlePress(0)}>
                    <RevenueIcon color={colors.green[500]} />
                    <Text style={[styles?.label, { color: colors.green[500] }]}>Revenues</Text>
                </TouchableOpacity>

                <TouchableOpacity activeOpacity={0.5} style={styles.cell} onPress={handlePress(1)}>
                    <ExpenseIcon color={colors.red[500]}></ExpenseIcon>
                    <Text style={[styles?.label, { color: colors.red[500] }]}>Expenses</Text>
                </TouchableOpacity>
            </View>
            <View style={styles.idicatorContainer}>
                <Animated.View
                    style={{
                        width,
                        alignItems: "center",
                    }}
                />
                <Animated.View
                    style={{
                        width: "50%",
                        alignItems: "center",
                    }}
                >
                    <Animated.View style={[styles.indicator, indicatorAnimatedStyles]} />
                </Animated.View>
            </View>
        </View>
    );
};

export default TabsHeader;

const styles = StyleSheet.create({
    cell: {
        flex: 1,
        alignItems: "center",
    },
    label: {
        color: colors.green[600],
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.md.fontSize,
    },
    idicatorContainer: {
        flexDirection: "row",
        width: "100%",
        borderBottomWidth: 1,
        borderBottomColor: colors.gray[200],
    },
    indicator: {
        height: 5,
        width: 30,
        borderTopEndRadius: 20,
        borderTopStartRadius: 20,
    },
});
