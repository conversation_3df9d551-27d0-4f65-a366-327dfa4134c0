import React, { useEffect, useRef, useState, useCallback } from "react";
import { ActivityIndicator, Image, Text, TouchableOpacity, View, Platform, Dimensions } from "react-native";
import BottomSheet, { BottomSheetBackdrop } from "@gorhom/bottom-sheet";
import { useStoreStore } from "../store/storeStore";
import { useAuthStore } from "../store/authStore";
import { Store } from "../types/Store";
import { typography } from "@styles/typography";
import colors from "@styles/colors";
import { FlatList, GestureHandlerRootView, RefreshControl } from "react-native-gesture-handler";
import { CheckIcon, NotificationActiveIcon, NotificationDisabledIcon } from "./icons/AccountListIcons";
import { Easing } from "react-native-reanimated";
import { isDeviceTokenRegistered } from "../utils/notificationsUtils";

type MultiStoreSelectorProps = {
    isVisible: boolean;
    onClose: () => void;
};

const MultiStoreSelector = ({ isVisible, onClose }: MultiStoreSelectorProps) => {
    const { storeLoading, user, setUser, store, setStore, setStores, stores, fetchStores, handleStoreSelect } =
        useStoreStore();
    const { auth, setAuth } = useAuthStore();
    const bottomSheetRef = useRef<BottomSheet>(null);
    const [isDeviceRegistered, setIsDeviceRegistered] = useState<boolean | null>(null);

    // Check if device is registered for notifications
    useEffect(() => {
        if (isVisible && auth === "auth") {
            const checkDeviceRegistration = async () => {
                const registered = await isDeviceTokenRegistered();
                setIsDeviceRegistered(registered);
            };
            checkDeviceRegistration();
        }
    }, [isVisible, auth]);

    // Calculate snapPoints based on number of stores
    const getSnapPoints = useCallback(() => {
        if (!stores || !Array.isArray(stores)) return ["50%"];

        const storeCount = stores.length;
        // console.log("storeCount => ", user?.username);
        if (storeCount === 1) return Platform.OS === "ios" ? ["15%"] : ["17%"];
        if (storeCount === 2) return Platform.OS === "ios" ? ["25%"] : ["27%"];
        if (storeCount === 3) return Platform.OS === "ios" ? ["35%"] : ["37%"];
        if (storeCount === 4) return Platform.OS === "ios" ? ["45%"] : ["47%"];
        return ["50%"]; // 4+ stores
    }, [stores]);

    const snapPoints = getSnapPoints();

    useEffect(() => {
        // console.log("snapPoints => ", snapPoints);
        // console.log("userstores count => ",user?.stores.length,"stores count => ", stores.length);
        if (isVisible) {
            bottomSheetRef.current?.expand();
            fetchStores();
        } else {
            bottomSheetRef.current?.close();
        }
    }, [isVisible]);

    // // Reset active store when auth state changes to unauth
    // useEffect(() => {
    //   if (auth === "unauth") {
    //     // Reset store to empty state when logged out
    //     setStore({
    //       _id: "",
    //     });
    //     setUser(undefined);
    //     setStores([]);
    //     console.log("user => ", user);
    //     console.log("stores => ", stores);
    //   }
    // }, [auth]);

    const renderBackdrop = useCallback(
        (props) => (
            <BottomSheetBackdrop
                {...props}
                disappearsOnIndex={-1}
                appearsOnIndex={0}
                opacity={0.5}
                style={{
                    backgroundColor: "rgba(0, 0, 0, 0.5)",
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    width: Dimensions.get("screen").width,
                    height: Platform.OS === "android" ? Dimensions.get("screen").height : "100%",
                }}
            />
        ),
        []
    );

    const handleSheetChanges = useCallback(
        (index: number) => {
            if (index === -1) {
                onClose();
                if (auth !== "auth") {
                    setAuth("unauth");
                }
            }
        },
        [onClose, auth, setAuth]
    );

    const renderStoreItem = ({ item, index }: { item: Store; index: number }) => {
        // Only show active state if authenticated
        const isActive = auth === "auth" && item._id === store?._id;
        const notificationsEnabled = user?.preferences?.notificationStores?.includes(item._id);

        return (
            <TouchableOpacity
                key={index}
                style={{
                    paddingHorizontal: 20,
                    flexDirection: "row",
                    alignItems: "center",
                    paddingVertical: 15,
                    backgroundColor: "white",
                    borderRadius: 12,
                    marginBottom: 12,
                    borderWidth: isActive ? 2 : 1,
                    borderColor: isActive ? colors.primary[500] : colors.gray[300],
                    justifyContent: "space-between",
                }}
                onPress={() => {
                    if (isActive) {
                        onClose();
                        return;
                    }
                    handleStoreSelect(item);
                    // onClose();
                }}
            >
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                    {item.logo?.sm ? (
                        <Image
                            source={{ uri: item.logo.sm }}
                            style={{
                                width: 42,
                                height: 42,
                                borderRadius: 20,
                                marginRight: 16,
                                borderWidth: 1,
                                borderColor: colors.gray[300],
                            }}
                        />
                    ) : (
                        <View
                            style={{
                                width: 40,
                                height: 40,
                                borderRadius: 20,
                                backgroundColor: colors.gray[100],
                                marginRight: 16,
                                justifyContent: "center",
                                alignItems: "center",
                            }}
                        >
                            <Text style={[typography.fontMedium, { color: colors.gray[600] }]}>
                                {(item.name || item.slug || "").charAt(0).toUpperCase()}
                            </Text>
                        </View>
                    )}
                    <View>
                        <Text
                            style={[
                                typography.fontMedium,
                                typography.md,
                                { color: colors.gray[800], textTransform: "capitalize" },
                            ]}
                        >
                            {item?.name || item?.slug || ""}
                        </Text>
                        <View style={{ flexDirection: "row", alignItems: "center", marginTop: 4 }}>
                            {notificationsEnabled ? (
                                <>
                                    <NotificationActiveIcon size={16} color={colors.primary[500]} />
                                    <Text style={[typography.sm, { color: colors.primary[500], marginLeft: 4 }]}>
                                        Notifications on
                                    </Text>
                                </>
                            ) : (
                                <>
                                    <NotificationDisabledIcon size={16} color={colors.gray[400]} />
                                    <Text style={[typography.sm, { color: colors.gray[400], marginLeft: 4 }]}>
                                        Notifications off
                                    </Text>
                                </>
                            )}
                        </View>
                    </View>
                </View>

                {isActive && <CheckIcon color={colors.primary[500]} size={24} />}
            </TouchableOpacity>
        );
    };

    // Only render the component when it's visible
    if (!isVisible) return null;

    return (
        <GestureHandlerRootView
            style={{
                position: "absolute",
                left: 0,
                right: 0,
                bottom: 0,
                top: 0,
                zIndex: 1000,
                width: "100%",
                height: "100%",
            }}
        >
            <BottomSheet
                ref={bottomSheetRef}
                snapPoints={snapPoints}
                enablePanDownToClose
                onClose={onClose}
                onChange={handleSheetChanges}
                index={0}
                backdropComponent={renderBackdrop}
                handleIndicatorStyle={{ backgroundColor: colors.gray[400], width: 40 }}
                animateOnMount={true}
                activeOffsetY={[-10, 10]}
                animationConfigs={{
                    duration: 300,
                    easing: Easing.inOut(Easing.ease),
                }}
            >
                <View style={{ flex: 1, padding: 16, paddingTop: 10 }}>
                    {storeLoading ? (
                        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                            <ActivityIndicator size="large" color={colors.primary[500]} />
                        </View>
                    ) : (
                        <FlatList
                            data={stores && Array.isArray(stores) ? [...stores] : []}
                            renderItem={({ item, index }) => {
                                return renderStoreItem({ item: item as Store, index });
                            }}
                            keyExtractor={(item: Store, index) => {
                                return item._id || index.toString();
                            }}
                            contentContainerStyle={{ paddingBottom: 15, paddingTop: 5 }}
                            showsVerticalScrollIndicator={true}
                            scrollEnabled={stores && Array.isArray(stores) && stores.length > 3}
                            waitFor={bottomSheetRef}
                            simultaneousHandlers={bottomSheetRef}
                        />
                    )}
                </View>
            </BottomSheet>
        </GestureHandlerRootView>
    );
};

export default MultiStoreSelector;
