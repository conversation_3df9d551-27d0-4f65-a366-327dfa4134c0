import { ScrollView, Text, View } from "react-native";

import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { useEffect, useRef } from "react";

import Background from "./HomeChartComponent/Background";
import Debug from "./HomeChartComponent/Debug";
import Label from "./HomeChartComponent/Label";
import XBarContainer from "./HomeChartComponent/XBarContainer";
import XProp from "./HomeChartComponent/XProp";
import { camelCaseToNormalText, metaProps, normalizeData, setMetaData } from "./HomeCharUtils/utils";

const XColumn = ({
    correctKey,
    axis,
    item,
    dataSet,
    columnWidth,
    metaData,
}: {
    correctKey: string;
    axis: string;
    item: any;
    dataSet: string[];
    columnWidth: number;
    metaData: metaProps;
}) => {
    return (
        <View
            style={{
                width: columnWidth,
                alignItems: "center",
            }}
        >
            {item && (
                <XBarContainer
                    item={item}
                    columnWidth={columnWidth}
                    heighestLineHeight={metaData[correctKey].heighestLineHeight}
                    heighestLineValue={metaData[correctKey].heighestLineValue}
                    dataSet={dataSet}
                    metaData={metaData}
                />
            )}
            {item && <XProp prop={item[axis]} />}
        </View>
    );
};

const Content = ({
    correctKey,
    normalizedData,
    axis,
    dataSet,
    xAxisPadding,
    columnWidth,
    metaData,
}: {
    correctKey: string;

    normalizedData: Object[];
    axis: string;
    dataSet: string[];
    xAxisPadding: number;
    columnWidth: number;
    metaData: metaProps;
}) => {
    return (
        <View
            style={[
                {
                    flexDirection: "row",
                    paddingHorizontal: xAxisPadding,
                },
                normalizedData.length > 5
                    ? { justifyContent: "flex-start", gap: columnWidth / 5 }
                    : { justifyContent: "space-evenly" },
            ]}
        >
            {normalizedData.map((item, index) => {
                return (
                    <XColumn
                        correctKey={correctKey}
                        axis={axis}
                        metaData={metaData}
                        columnWidth={columnWidth}
                        item={item}
                        key={index}
                        dataSet={dataSet}
                    />
                );
            })}
        </View>
    );
};

export type Color =
    | "primary"
    | "secondary"
    | "orange"
    | "blue"
    | "green"
    | "teal"
    | "pink"
    | "red"
    | "gray"
    | "cyan"
    | "black"
    | "blackAlpha"
    | "white"
    | "whiteAlpha";

const HomeChartV2 = ({
    data,
    axis,
    dataSet,
    height,
    nYticks,
    columnWidth,
    label,
    debug = false,
    colorsSet,
    xAxisPadding = 30,
}: {
    debug?: boolean;
    data: Object[];
    axis: string;
    dataSet: string[];
    colorsSet: Color[];
    height: number;
    nYticks: number;
    columnWidth: number;
    label: string;
    xAxisPadding?: number;
}) => {
    const normalizedData: object[] = normalizeData(data, [axis, ...dataSet]);
    const backgroundHeight = (height * 9) / 10;
    const metaData = setMetaData(height, normalizedData, dataSet, nYticks, backgroundHeight, colorsSet);
    const chooseNormalizingDataSetItem = (metaData: any, dataSet: any) => {
        let max = 0;
        let result = dataSet[0];
        for (const key in metaData) {
            if (metaData[key].max > max) {
                max = metaData[key].max;
                result = key;
            }
        }
        return result;
    };
    let correctKey = chooseNormalizingDataSetItem(metaData, dataSet);
    const scrollRef: any = useRef(null);
    useEffect(() => {
        // if (scrollRef.current)
        //     scrollRef.current.scrollToEnd({ animated: false });
    }, []);
    return (
        <View style={{ gap: 5 }}>
            {debug && <Debug metaData={metaData} />}
            <Label label={label} />
            {data.length !== 0 ? (
                <>
                    <View>
                        <Background
                            firstDigetPosition={metaData[correctKey].firstDigetPosition}
                            fixedTickSize={metaData[correctKey].fixedTickSize}
                            nYticks={nYticks}
                            yStepSize={metaData[correctKey].yStepSize}
                        />
                        {normalizedData.length > 5 ? (
                            <ScrollView horizontal ref={scrollRef}>
                                <Content
                                    correctKey={correctKey}
                                    normalizedData={normalizedData}
                                    columnWidth={columnWidth}
                                    metaData={metaData}
                                    xAxisPadding={xAxisPadding}
                                    axis={axis}
                                    dataSet={dataSet}
                                />
                            </ScrollView>
                        ) : (
                            <Content
                                correctKey={correctKey}
                                normalizedData={normalizedData}
                                columnWidth={columnWidth}
                                metaData={metaData}
                                xAxisPadding={xAxisPadding}
                                axis={axis}
                                dataSet={dataSet}
                            />
                        )}
                    </View>
                    <View
                        style={{
                            flexDirection: "row",
                            alignSelf: "stretch",
                            justifyContent: "space-evenly",
                            alignContent: "space-between",
                            flexWrap: "wrap",
                        }}
                    >
                        {dataSet.map((key, index) => (
                            <View
                                key={index}
                                style={{
                                    flexDirection: "row",
                                    alignItems: "center",
                                    gap: 5,
                                    paddingHorizontal: 5,
                                }}
                            >
                                <View
                                    style={{
                                        height: 8,
                                        width: 8,
                                        borderRadius: 300,
                                        backgroundColor: colors[colorsSet[index]][500],
                                    }}
                                ></View>
                                <Text
                                    style={{
                                        fontFamily: typography.fontSemibold.fontFamily,
                                        color: colors.gray[800],
                                        textTransform: "capitalize",
                                        fontSize: typography.sm.fontSize,
                                    }}
                                >
                                    {camelCaseToNormalText(key)}
                                </Text>
                            </View>
                        ))}
                    </View>
                </>
            ) : (
                <View style={{ height, justifyContent: "center" }}>
                    <Text
                        style={{
                            color: colors.gray[700],
                            fontFamily: typography.fontMedium.fontFamily,
                            textAlign: "center",
                        }}
                    >
                        No Data
                    </Text>
                </View>
            )}
        </View>
    );
};

export default HomeChartV2;
