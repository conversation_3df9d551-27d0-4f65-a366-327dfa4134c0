import React from "react";
import { Text, View } from "react-native";

import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { barValue } from "../HomeCharUtils/utils";

const XBar = ({
    value,
    color,
    textVisible,
    heighestLineHeight,
    heighestLineValue,
    barWidth,
}: {
    value: number;
    color: string;
    textVisible: boolean;
    heighestLineHeight: number;
    heighestLineValue: number;
    barWidth: number;
}) => {
    return (
        <View
            style={{
                alignItems: "center",
                width: 20,
                gap: 5,
                overflow: "visible",
            }}
        >
            <View style={{ overflow: "visible", alignItems: "center" }}>
                {textVisible && (
                    <Text
                        style={{
                            position: "relative",
                            width: 300,
                            height: 16,
                            overflow: "visible",
                            textAlign: "center",
                            zIndex: 100,
                            color: colors.gray[700],
                            fontFamily: typography.fontSemibold.fontFamily,
                            fontSize: typography.xs.fontSize,
                        }}
                    >
                        {value}
                    </Text>
                )}
            </View>

            <View
                style={{
                    height: barValue(value, heighestLineHeight, heighestLineValue),
                    width: barWidth,
                    backgroundColor: colors[color][500], //! ignore it please
                    borderTopEndRadius: 3,
                    borderTopStartRadius: 3,
                }}
            />
        </View>
    );
};

export default XBar;
