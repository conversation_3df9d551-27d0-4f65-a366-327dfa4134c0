import React from "react";
import { ScrollView, Text, View } from "react-native";

const Debug = ({ metaData }: { metaData: any }) => {
    let result: Array<any> = [];
    for (const key in metaData) {
        result.push(
            <View key={key}>
                <Text>{key}</Text>
                {(() => {
                    let result: Array<any> = [];
                    for (const secondKey in metaData[key]) {
                        result.push(<Text>{`${secondKey}: ${metaData[key][secondKey]}`}</Text>);
                    }
                    return result;
                })()}
            </View>
        );
    }
    return (
        <ScrollView horizontal contentContainerStyle={{ flexDirection: "row" }}>
            {result}
        </ScrollView>
    );
};

export default Debug;
