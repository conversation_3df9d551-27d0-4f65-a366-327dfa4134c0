import { Text } from "react-native";
import React from "react";
import { typography } from "@styles/typography";
import colors from "@styles/colors";

const Label = ({ label }: { label: string }) => {
    return (
        <Text
            style={{
                alignSelf: "center",
                fontFamily: typography.fontMedium.fontFamily,
                // fontSize: typography.lg.fontSize,
                fontSize: 16,
                fontWeight: "700",
                color: colors.gray[700],
            }}
        >
            {label}
        </Text>
    );
};

export default Label;
