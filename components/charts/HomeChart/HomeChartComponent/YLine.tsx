import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { StyleSheet, Text, View } from "react-native";

type YLineProps = {
    tickValue: number;
    firstDigetPosition: number;
    yStepSize: number;
    fixedTickSize: number;
};

const YLine = ({ fixedTickSize, tickValue, firstDigetPosition, yStepSize }: YLineProps) => {
    return (
        <View style={[style.line, { height: yStepSize }]}>
            <Text style={style.lineValue}>
                {tickValue < 0
                    ? tickValue.toPrecision(Math.abs(firstDigetPosition))
                    : fixedTickSize < 1
                    ? tickValue?.toFixed(2)
                    : tickValue
                    ? tickValue
                    : "-"}
            </Text>
        </View>
    );
};

const style = StyleSheet.create({
    line: {
        borderBottomWidth: 1,
        borderColor: colors.gray[300],
    },
    lineValue: {
        zIndex: 1000,
        position: "absolute",
        color: colors.gray[400],
        fontFamily: typography.fontMedium.fontFamily,
        // fix maybe
    },
});

export default YLine;
