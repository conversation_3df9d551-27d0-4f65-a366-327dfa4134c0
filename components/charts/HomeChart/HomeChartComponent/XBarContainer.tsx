import React, { useState } from "react";
import { TouchableOpacity } from "react-native";
import { metaProps } from "../HomeCharUtils/utils";
import XBar from "./XBar";

const XBarContainer = ({
    item,
    columnWidth,
    heighestLineHeight,
    heighestLineValue,
    dataSet,
    metaData,
}: {
    item: any;
    columnWidth: number;
    heighestLineHeight: number;
    heighestLineValue: number;
    dataSet: string[];
    metaData: metaProps;
}) => {
    const [visible, setVisile] = useState(false);
    const barWidth = (columnWidth * 0.5) / dataSet.length;
    return (
        <TouchableOpacity
            onPress={() => {
                setVisile(!visible);
            }}
            style={{
                height: heighestLineHeight,
                width: columnWidth,

                justifyContent: "space-evenly",
                flexDirection: "row",
                alignItems: "flex-end",
            }}
        >
            {dataSet.map((key, index) => {
                return (
                    <XBar
                        barWidth={barWidth}
                        textVisible={visible}
                        heighestLineHeight={heighestLineHeight}
                        heighestLineValue={heighestLineValue}
                        key={index}
                        value={item[key]}
                        color={metaData[key].color}
                    />
                );
            })}
        </TouchableOpacity>
    );
};

export default XBarContainer;
