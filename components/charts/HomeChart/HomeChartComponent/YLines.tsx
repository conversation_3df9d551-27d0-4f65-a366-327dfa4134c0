import React from "react";

import YLine from "./YLine";

const YLines = ({
    fixedTickSize,
    nYticks,
    yStepSize,
    firstDigetPosition,
}: {
    fixedTickSize: number;
    nYticks: number;
    yStepSize: number;
    firstDigetPosition: number;
}) => {
    let Ylines: Array<React.ReactElement> = [];
    let tickValue = fixedTickSize;
    for (let i = 0; i < nYticks; i++) {
        Ylines.push(
            <YLine
                fixedTickSize={fixedTickSize}
                yStepSize={yStepSize}
                firstDigetPosition={firstDigetPosition}
                tickValue={tickValue}
                key={i}
            />
        );
        tickValue = tickValue + fixedTickSize;
    }
    return Ylines;
};

export default YLines;
