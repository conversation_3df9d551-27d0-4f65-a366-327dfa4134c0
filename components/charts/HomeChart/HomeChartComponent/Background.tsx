import React from "react";
import { View } from "react-native";

import colors from "@styles/colors";
import YLines from "./YLines";

const Background = ({
    fixedTickSize,
    nYticks,
    yStepSize,
    firstDigetPosition,
}: {
    fixedTickSize: number;
    nYticks: number;
    yStepSize: number;
    firstDigetPosition: number;
}) => {
    return (
        <View
            style={{
                position: "absolute",
                width: "100%",
                flexDirection: "column-reverse",
                borderTopColor: colors.gray[300],
                borderTopWidth: 1,
            }}
        >
            {YLines({ fixedTickSize, nYticks, yStepSize, firstDigetPosition })}
        </View>
    );
};

export default Background;
