import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { Text, View } from "react-native";
import { formatDate } from "../HomeCharUtils/utils";

const XProp = ({ prop }: { prop: string }) => {
    return (
        <View>
            <Text
                style={{
                    fontFamily: typography.fontSemibold.fontFamily,
                    color: colors.gray[600],
                    textAlign: "center",
                    fontSize: typography.xs.fontSize,
                }}
            >
                {formatDate(prop)}
            </Text>
        </View>
    );
};

export default XProp;
