import { Color } from "../HomeChartV2";

export let maxData = (data: Array<any>, key: string) => {
    return Math.max.apply(
        null,
        data.map((obj) => obj[key])
    );
};
export let minData = (data: Array<any>) => {
    return Math.max.apply(
        null,
        data.map((obj) => obj.totalValue)
    );
};

export const unadjestedTick = (max: number, numberYticks: number) => max / numberYticks;

export const adjustTickSize = (unadjestedTickSize: number) => {
    // transform to sicentific notation (x=y10^(z); 0<y<10, z:int) returns y
    let num = unadjestedTickSize;
    let exponent = getFirstDigetPosition(num);
    let x = num / Math.pow(10, exponent);
    let adjestedTickSize = Math.round(x) * Math.pow(10, exponent);
    return adjestedTickSize;
};

export const fixTicksSize = (adjestedTickSize: number, numberYticks: number, maxData: number) => {
    if (adjestedTickSize * numberYticks < maxData + maxData / 10) {
        return (adjestedTickSize = adjestedTickSize + Math.pow(10, getFirstDigetPosition(adjestedTickSize)));
    }

    return adjestedTickSize;
};
export let getFirstDigetPosition = (num: number) => {
    return Math.floor(Math.log10(Math.abs(num)));
};

export let calcTickPosStep = (fixedTickSize: number, canvasHeight: number, maxData: number) => {
    return (fixedTickSize * canvasHeight) / maxData;
};

export const formatDate = (date: string) => {
    let formatedDate = new Date(date);
    return `${formatedDate.getDate().toString().padStart(2, "0")}/${(formatedDate.getMonth() + 1)
        .toString()
        .padStart(2, "0")}`;
};

export const barValue = (value: number, heighestLineHeight: number, heighestLineValue: number) => {
    return (value * heighestLineHeight) / heighestLineValue ? (value * heighestLineHeight) / heighestLineValue : 0;
};

export function isObject<T>(variable: T): variable is T & object {
    return typeof variable === "object" && variable !== null && !Array.isArray(variable);
}

export function findPath(obj: any, target: string): string[] {
    const results: string[] = [];

    function search(currentObj: any, path: Array<any> = []): void {
        for (const key in currentObj) {
            const fullpath = path.length === 0 ? [key] : [...path, key];
            if (key === target) {
                results.push(...fullpath);
                return;
            } else if (typeof currentObj[key] === "object" && currentObj[key] !== null) {
                search(currentObj[key], [...fullpath]);
            }
        }
    }

    search(obj);
    return results;
}

export function findPaths(obj: any, targets: string[]): Object {
    let result: any = {};
    targets.map((target, index) => {
        let path = findPath(obj, target);
        if (path.length === 0) return;
        result[target] = path;
    });

    return result;
}
export function reachLastElement(obj: any, path: string[]): any {
    if (path.length === 0) {
        return obj;
    }

    const key = path[0];
    if (!obj || typeof obj !== "object" || !Object.hasOwnProperty.call(obj, key)) {
        return undefined;
    }

    return reachLastElement(obj[key], path.slice(1));
}

export const normalizeData = (data: Array<Object>, targets: Array<string>) => {
    let Path = findPaths(data, targets);
    let result: Array<Object> = [];
    data.map((item, index) => {
        result.push(findPaths(item, targets));
    });
    result.map((item: any, index) => {
        for (const key in item) {
            item[key] = reachLastElement(data[index], item[key]);
        }
    });

    return result;
};

// export type SetMeta = [
//     { height: number },
//     { nYticks: number },
//     { max: number },
//     { unadjestedTickSize: number },
//     { adjestedTickSize: number },
//     { firstDigetPosition: number },
//     { fixedTickSize: number },
//     { heighestLineValue: number },
//     { backgroundHeight: number },
//     { yStepSize: number },
//     { heighestLineHeight: number },
// ];

export type SetMeta = {
    height: number;
    nYticks: number;
    max: number;
    unadjestedTickSize: number;
    adjestedTickSize: number;
    firstDigetPosition: number;
    fixedTickSize: number;
    heighestLineValue: number;
    backgroundHeight: number;
    yStepSize: number;
    heighestLineHeight: number;
    color: Color;
};
export type metaProps = Record<string, SetMeta>;

export const setMetaData = (
    height: number,
    normalizedData: Array<Object>,
    dataSet: Array<string>,
    nYticks: number,
    backgroundHeight: number,
    colors: Color[]
) => {
    let metaData: metaProps = {};
    dataSet.map((key: string, index) => {
        let max = maxData(normalizedData, key);
        let unadjestedTickSize = unadjestedTick(max, nYticks);
        let adjestedTickSize = adjustTickSize(unadjestedTickSize);
        let firstDigetPosition = getFirstDigetPosition(adjestedTickSize);
        let fixedTickSize = fixTicksSize(adjestedTickSize, nYticks, max);
        let heighestLineValue = fixedTickSize * nYticks;
        let yStepSize = backgroundHeight / (nYticks + 1);
        let heighestLineHeight = yStepSize * nYticks;
        let color: any = colors[index];
        metaData[key] = {
            height,
            nYticks,
            max,
            unadjestedTickSize,
            adjestedTickSize,
            firstDigetPosition,
            fixedTickSize,
            heighestLineValue,
            backgroundHeight,
            yStepSize,
            heighestLineHeight,
            color,
        };
    });
    return metaData;
};

export function camelCaseToNormalText(str: string): string {
    return str.replace(/([A-Z])/g, " $1").replace(/^ /, "");
}
