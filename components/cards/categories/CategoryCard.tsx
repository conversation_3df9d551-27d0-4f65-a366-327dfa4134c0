import React from "react";
import { StyleSheet, Text, TouchableOpacity, View, Image } from "react-native";

import colors from "@styles/colors";
import { Path, Svg } from "react-native-svg";

const CategoryCard = ({ category }: any) => {
    return (
        <TouchableOpacity style={styles.container} activeOpacity={1}>
            <CategoryBanner image={category.banner && category.banner?.sm ? category.banner.sm : ""} />
            <View style={{ position: "absolute", top: 60, left: 20 }}>
                <CategoryImage image={category.image && category.image?.sm ? category.image.sm : ""} />
            </View>
            <View style={{ padding: 10 }}>
                <View
                    style={{
                        flexDirection: "row",
                        height: 50,
                        alignItems: "flex-start",
                        justifyContent: "space-between",
                    }}
                >
                    <View style={{ width: 100 }} />
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "400",
                        }}
                    >
                        Category:
                    </Text>
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "700",
                            maxWidth: 150,
                        }}
                    >
                        {category.name}
                    </Text>
                </View>
            </View>
        </TouchableOpacity>
    );
};

export default CategoryCard;

const CategoryImage = ({ image }: any) => {
    return (
        <View
            style={{
                width: 110,
                height: 110,
                overflow: "hidden",
                backgroundColor: "white",
                justifyContent: "center",
                alignItems: "center",
                borderRadius: 60,
            }}
        >
            <Image
                source={{ uri: image }}
                style={{
                    width: "100%",
                    height: "100%",
                    resizeMode: "cover",
                }}
            />
        </View>
    );
};

const PlaceholderIcon = () => {
    return (
        <Svg height="48" viewBox="0 -960 960 960" width="48">
            <Path
                d="M200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm40-80h480L570-480 450-320l-90-120-120 160Zm-40 80v-560 560Zm140-360q25 0 42.5-17.5T400-620q0-25-17.5-42.5T340-680q-25 0-42.5 17.5T280-620q0 25 17.5 42.5T340-560Z"
                fill={"white"}
            />
        </Svg>
    );
};

const CategoryBanner = ({ image }: any) => {
    return (
        <View
            style={{
                width: "100%",
                aspectRatio: 4 / 1,
                overflow: "hidden",
                backgroundColor: colors.gray[300],
                justifyContent: "center",
                alignItems: "center",
                borderTopLeftRadius: 12,
                borderTopRightRadius: 12,
            }}
        >
            <PlaceholderIcon />
        </View>
    );
};

// const Tag = ({ status }: any) => {
//     if (status === 'hidden') {
//         return (
//             <View
//                 style={{
//                     ...styles.tagContainer,
//                     backgroundColor: colors.orange[100],
//                 }}>
//                 <Text
//                     style={{
//                         ...styles.tagText,
//                         color: colors.orange[800],
//                     }}>
//                     Hidden
//                 </Text>
//             </View>
//         );
//     }

//     return (
//         <View
//             style={{
//                 ...styles.tagContainer,
//                 backgroundColor: colors.green[100],
//             }}>
//             <Text
//                 style={{
//                     ...styles.tagText,
//                     color: colors.green[800],
//                 }}>
//                 Shown
//             </Text>
//         </View>
//     );
// };

const styles = StyleSheet.create({
    container: {
        width: "100%",
        borderWidth: 1,
        borderColor: colors.primary[50],
        borderRadius: 12,
        backgroundColor: colors.gray[50],
        alignSelf: "center",
    },
    leftContainer: {
        width: "35%",
        gap: 10,
    },
    rightContainer: {
        width: "63%",
        gap: 10,
        padding: 5,
    },
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    line: {
        width: "100%",
        height: 1,
        backgroundColor: colors.primary[50],
    },
    tagContainer: {
        paddingTop: 5,
        paddingBottom: 5,
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 6,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        gap: 5,
    },
    tagText: {
        fontSize: 14,
        fontWeight: "500",
    },
});
