import { StyleSheet } from 'react-native';
import colors from '@styles/colors';
import { typography } from '@styles/typography';

/**
 * Optimized styles for OrderCard component
 * Extracted from inline styles to prevent recalculations
 */
export const orderCardStyles = StyleSheet.create({
    // Main container styles
    container: {
        marginHorizontal: 10,
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 10,
        gap: 25,
        borderWidth: 1,
        borderColor: colors.gray[300],
        backgroundColor: colors.blue[50],
    },

    // Header section styles
    headerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    
    headerLeft: {
        flex: 1,
    },

    referenceContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 5,
    },

    referenceText: {
        fontFamily: typography.fontBold.fontFamily,
        color: colors.primary[500],
        fontSize: 16,
    },

    dateText: {
        color: colors.gray[500],
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: 12,
        marginRight: 10,
    },

    // Content section styles
    contentContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
    },

    customerContainer: {
        flex: 2,
    },

    customerName: {
        fontSize: 18,
        textTransform: 'capitalize',
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.gray[600],
    },

    customerPhone: {
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.gray[500],
        fontSize: 14,
        marginRight: 10,
    },

    // Price section styles
    priceContainer: {
        textAlign: 'right',
        color: colors.primary[600],
        fontFamily: typography.fontBold.fontFamily,
        fontSize: 24,
        flex: 1,
    },

    priceDecimal: {
        fontSize: 16,
    },

    priceCurrency: {
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: 14,
        marginLeft: 2,
    },

    // Swipeable action styles
    leftActionContainer: {
        width: '40%',
        marginLeft: 10,
        marginRight: -30,
        borderRadius: 10,
        overflow: 'hidden',
    },

    rightActionContainer: {
        width: '40%',
        marginRight: 10,
        marginLeft: -30,
        borderRadius: 10,
        overflow: 'hidden',
    },

    actionContent: {
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 2,
    },

    editAction: {
        backgroundColor: colors.orange[400],
        paddingRight: 15,
    },

    deleteAction: {
        backgroundColor: colors.red[500],
        paddingLeft: 15,
    },

    restoreAction: {
        backgroundColor: colors.green[500],
        paddingLeft: 15,
    },

    actionLabel: {
        fontFamily: typography.altTextMedium.fontFamily,
        fontSize: typography.md.fontSize,
        color: colors.white,
    },

    actionLabelCenter: {
        textAlign: 'center',
    },
});
