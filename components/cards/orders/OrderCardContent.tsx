import React, { memo } from 'react';
import { Text, View } from 'react-native';
import { Tag } from '@components/inputs/chips/Tag/Tag';
import { CheckIcon } from '@components/icons/AccountListIcons';
import { Order } from '@components/orders/types';
import { useStoreStore } from '../../../store/storeStore';
import { orderCardStyles } from './OrderCardStyles';
import colors from '@styles/colors';

// Utility functions (moved from main component)
export const formatDate = (date: string) => {
    const createdAtDate = new Date(date);
    const formattedDate = `${createdAtDate.getDate().toString().padStart(2, '0')}/${(
        createdAtDate.getMonth() + 1
    )
        .toString()
        .padStart(2, '0')}/${createdAtDate.getFullYear()} ${createdAtDate
        .getHours()
        .toString()
        .padStart(2, '0')}:${createdAtDate.getMinutes().toString().padStart(2, '0')}`;
    return formattedDate;
};

export const getInt = (number: number) => {
    return Math.trunc(number);
};

export const getDecimal = (number: number) => {
    return number - getInt(number);
};

export const getDecimalString = (number: number, length: number) => {
    const decimal = getDecimal(number);
    let res = decimal.toFixed(length);
    return res.replace(/^0\./, '');
};

interface OrderCardContentProps {
    order: Order;
    selected: boolean;
}

/**
 * Pure UI component for OrderCard content
 * Handles only the visual representation of order data
 */
const OrderCardContent = memo(({ order, selected }: OrderCardContentProps) => {
    const { store } = useStoreStore();

    return (
        <>
            {/* Header Section */}
            <View style={orderCardStyles.headerContainer}>
                <View style={orderCardStyles.headerLeft}>
                    <View style={orderCardStyles.referenceContainer}>
                        <Text style={orderCardStyles.referenceText}>
                            #{order?.reference}
                        </Text>
                        {selected && (
                            <CheckIcon color={colors.primary[500]} size={20} />
                        )}
                    </View>
                    <Text style={orderCardStyles.dateText}>
                        {formatDate(String(order.createdAt))}
                    </Text>
                </View>
                {order.isTest && (order?.status === 'pending' || order?.status === 'abandoned') ? (
                    <Tag status={'test'} />
                ) : order.duplicated && (order?.status === 'pending' || order?.status === 'abandoned') ? (
                    <Tag status={'duplicated'} />
                ) : (
                    <Tag status={order?.status} attempt={order.attempt} />
                )}
            </View>

            {/* Content Section */}
            <View style={orderCardStyles.contentContainer}>
                <View style={orderCardStyles.customerContainer}>
                    <Text style={orderCardStyles.customerName}>
                        {order?.customer.name}
                    </Text>
                    <Text style={orderCardStyles.customerPhone}>
                        {order?.customer.phone}
                    </Text>
                </View>
                <Text style={orderCardStyles.priceContainer}>
                    <Text>{getInt(order?.total.totalPrice)}</Text>
                    <Text style={orderCardStyles.priceDecimal}>
                        .{getDecimalString(order?.total.totalPrice, 2)}
                    </Text>
                    <Text style={orderCardStyles.priceCurrency}>
                        {store.currency?.code}
                    </Text>
                </Text>
            </View>
        </>
    );
});

OrderCardContent.displayName = 'OrderCardContent';

export default OrderCardContent;
