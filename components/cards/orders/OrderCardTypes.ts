import { Order } from '@components/orders/types';

/**
 * TypeScript interfaces for OrderCard component and related hooks
 * Replaces 'any' types with explicit interfaces for better type safety
 */

export interface SwipeableRef {
    close: () => void;
}

export type ModalMode = 'edit' | 'delete' | 'send' | 'restore';

export interface OrderCardProps {
    order: Order;
    selectionMode: boolean;
    setDeleteId: (id: string) => void;
    selectedOrdersId: string[];
    setSelectedOrdersId: (ids: string[]) => void;
    setSwipeableClose: (callback: () => () => void) => void;
    setModalVisible: (visible: boolean) => void;
    setModalMode: (mode: ModalMode) => void;
    disableInteractions?: boolean;
    isBlockCustomerView?: boolean;
}

export interface OrderCardMemoComparisonProps {
    order: Order;
    selectionMode: boolean;
    selectedOrdersId: string[];
    disableInteractions?: boolean;
    isBlockCustomerView?: boolean;
}

export interface SwipeDirection {
    direction: 'left' | 'right';
}
