import React from 'react';
import { Text, View } from 'react-native';
import { EditIcon } from '@components/icons/BudgetManagerIcons';
import { TrashIcon, RestoreIcon } from '@components/icons/AccountListIcons';
import { Order } from '@components/orders/types';
import { orderCardStyles } from './OrderCardStyles';
import colors from '@styles/colors';

interface OrderCardActionsProps {
    order: Order;
    disableInteractions: boolean;
    selectionMode: boolean;
}

/**
 * Hook for OrderCard swipeable actions
 * Returns render functions for left (edit) and right (delete/restore) actions
 */
export const useOrderCardActions = ({ order, disableInteractions, selectionMode }: OrderCardActionsProps) => {
    const renderLeftActions = () => {
        if (disableInteractions || selectionMode) return null;

        return (
            <View style={orderCardStyles.leftActionContainer}>
                <View style={[orderCardStyles.actionContent, orderCardStyles.editAction]}>
                    <EditIcon color={colors.white} size={32} />
                    <Text style={orderCardStyles.actionLabel}>Edit</Text>
                </View>
            </View>
        );
    };

    const renderRightActions = () => {
        if (selectionMode) return null;

        const isDeleted = order.status === 'deleted';
        const actionStyle = isDeleted ? orderCardStyles.restoreAction : orderCardStyles.deleteAction;

        return (
            <View style={orderCardStyles.rightActionContainer}>
                <View style={[orderCardStyles.actionContent, actionStyle]}>
                    {isDeleted ? (
                        <>
                            <RestoreIcon color={colors.white} size={22} />
                            <Text style={[orderCardStyles.actionLabel, orderCardStyles.actionLabelCenter]}>
                                Restore
                            </Text>
                        </>
                    ) : (
                        <>
                            <TrashIcon color={colors.white} size={32} />
                            <Text style={orderCardStyles.actionLabel}>Delete</Text>
                        </>
                    )}
                </View>
            </View>
        );
    };

    return {
        renderLeftActions,
        renderRightActions,
    };
};

// Remove the default export since we're now exporting the hook
