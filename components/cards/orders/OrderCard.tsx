import React, { useRef, memo } from 'react';
import Swipeable from 'react-native-gesture-handler/ReanimatedSwipeable';
import { GestureDetector } from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';

// Custom hooks
import { useOrderCardGestures } from '../../../hooks/useOrderCardGestures';
import { useOrderCardSwipeable } from '../../../hooks/useOrderCardSwipeable';
import { useOrderCardAnimation } from '../../../hooks/useOrderCardAnimation';

// Components
import OrderCardContent, { formatDate, getInt, getDecimal, getDecimalString } from './OrderCardContent';
import { useOrderCardActions } from './OrderCardActions';

// Types and styles
import { OrderCardProps } from './OrderCardTypes';
import { orderCardStyles } from './OrderCardStyles';

/**
 * Refactored OrderCard Component
 *
 * Clean, performant order card with separated concerns:
 * - Gesture handling via useOrderCardGestures hook
 * - Swipeable actions via useOrderCardSwipeable hook
 * - Animation via useOrderCardAnimation hook
 * - UI content via OrderCardContent component
 * - Action rendering via OrderCardActions component
 */
const OrderCard = memo(
    ({
        order,
        selectionMode,
        setDeleteId,
        selectedOrdersId,
        setSelectedOrdersId,
        setSwipeableClose,
        setModalVisible,
        setModalMode,
        disableInteractions = false,
    }: OrderCardProps) => {
        const swipeableRef = useRef<any>(null);
        const selected = selectedOrdersId.includes(order._id);

        // Custom hooks for separated concerns
        const { gestures } = useOrderCardGestures({
            order,
            selectionMode,
            selectedOrdersId,
            setSelectedOrdersId,
            disableInteractions,
        });

        const { handleSwipeableWillOpen } = useOrderCardSwipeable({
            order,
            swipeableRef,
            setDeleteId,
            setModalVisible,
            setModalMode,
            setSwipeableClose,
            disableInteractions,
        });

        const { selectedAnimation } = useOrderCardAnimation({ selected });

        // Action components
        const { renderLeftActions, renderRightActions } = useOrderCardActions({
            order,
            disableInteractions,
            selectionMode,
        });

        return (
            <Swipeable
                ref={swipeableRef}
                enabled={true}
                overshootLeft={false}
                onSwipeableWillOpen={handleSwipeableWillOpen}
                friction={2}
                renderLeftActions={renderLeftActions}
                renderRightActions={renderRightActions}
            >
                <GestureDetector gesture={gestures}>
                    <Animated.View style={[orderCardStyles.container, selectedAnimation]}>
                        <OrderCardContent order={order} selected={selected} />
                    </Animated.View>
                </GestureDetector>
            </Swipeable>
        );
    },
    (prevProps, nextProps) => {
        // Custom comparison function for React.memo
        return (
            prevProps.order._id === nextProps.order._id &&
            prevProps.order.reference === nextProps.order.reference &&
            prevProps.order.status === nextProps.order.status &&
            prevProps.order.total?.totalPrice === nextProps.order.total?.totalPrice &&
            prevProps.selectionMode === nextProps.selectionMode &&
            prevProps.selectedOrdersId.length === nextProps.selectedOrdersId.length &&
            prevProps.selectedOrdersId.includes(prevProps.order._id) ===
                nextProps.selectedOrdersId.includes(nextProps.order._id) &&
            prevProps.disableInteractions === nextProps.disableInteractions &&
            prevProps.isBlockCustomerView === nextProps.isBlockCustomerView
        );
    }
);

// Re-export utility functions for backward compatibility
export { formatDate, getInt, getDecimal, getDecimalString };

export default OrderCard;
