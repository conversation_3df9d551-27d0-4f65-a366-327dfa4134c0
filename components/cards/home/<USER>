import { CartIcon, DolarIcon } from "@components/icons/HeaderIcons";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Image, Text, View } from "react-native";
import { getDecimal, getDecimalString, getInt } from "../orders/OrderCard";

type Props = {
    title: string;
    date: string;
    value: number | string;
    currency: string;
    units: number | string;
};

const HomeCardBig = ({ title, date, value, currency = "TND", units }: Props) => {
    const Title = "Ajourd'hui";

    return (
        <View
            style={{
                minHeight: 100,
                display: "flex",
                alignItems: "center",
                justifyContent: "space-around",
                gap: 20,
                padding: 20,
                backgroundColor: colors.primary[500],
                borderRadius: 12,
                overflow: "hidden",
            }}
        >
            {/* the background texture */}
            <View
                style={{
                    height: 0,
                    overflow: "visible",
                    width: " 150%",
                    right: "-50%",
                    top: "-300%",
                    position: "absolute",
                }}
            >
                <Image
                    source={require("@assets/testing.png")}
                    style={{
                        opacity: 0.1,
                        position: "absolute",

                        transform: [{ rotate: "-25deg" }],
                    }}
                />
            </View>
            {/* the Top items */}
            <View
                style={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignSelf: "stretch",
                    alignItems: "baseline",
                }}
            >
                {/* day container*/}
                <Text
                    style={{
                        color: colors.white,
                        fontSize: typography.xl.fontSize,
                        fontFamily: typography.fontSemibold.fontFamily,
                    }}
                >
                    {title}
                </Text>
                {/* date container*/}
                <Text
                    style={{
                        textAlign: "right",
                        color: colors.white,
                        fontSize: typography.sm.fontSize,
                        fontFamily: typography.baseText.fontFamily,
                    }}
                >
                    {date}
                </Text>
            </View>
            {/* the bottom items */}
            <View
                style={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    alignSelf: "stretch",
                }}
            >
                {/* the price container */}
                <View
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 5,
                    }}
                >
                    <DolarIcon size={24} color={colors.secondary[300]} />
                    <View
                        style={{
                            display: "flex",
                            flexDirection: "row",
                            alignItems: "baseline",
                            gap: 2,
                        }}
                    >
                        <View
                            style={{
                                display: "flex",
                                flexDirection: "row",
                                alignItems: "baseline",
                            }}
                        >
                            <Text
                                style={{
                                    color: colors.white,
                                    fontFamily: typography.fontBold.fontFamily,
                                    fontSize: typography.xl.fontSize,
                                }}
                            >
                                {getInt(Number(value)).toLocaleString()}
                            </Text>
                            <Text
                                style={{
                                    color: colors.white,
                                    fontFamily: typography.fontBold.fontFamily,
                                    fontSize: typography.xs.fontSize,
                                }}
                            >
                                .{getDecimalString(Number(value), 2)}
                            </Text>
                        </View>
                        <Text
                            style={{
                                color: colors.white,
                                fontFamily: typography.fontNormal.fontFamily,
                                fontSize: typography.xs.fontSize,
                            }}
                        >
                            {currency}
                        </Text>
                    </View>
                </View>
                {/* units container */}
                <View
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 5,
                    }}
                >
                    <CartIcon size={24} color={colors.secondary[300]} />
                    <Text
                        style={{
                            color: colors.white,
                            fontFamily: typography.fontBold.fontFamily,
                            fontSize: typography.xl.fontSize,
                        }}
                    >
                        {units}
                    </Text>
                </View>
            </View>
        </View>
    );
};

export default HomeCardBig;
