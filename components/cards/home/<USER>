import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Image, Text, View } from "react-native";
import { CartIcon, DolarIcon } from "@components/icons/HeaderIcons";
import { getDecimalString, getInt } from "../orders/OrderCard";
type Props = {
    title: string;
    date: string;
    value: string | number;
    units: string | number;
    currency: string;
    variant: "gray" | "teal" | "cyan" | "orange";
};
const HomeCardSmall = ({ title, date, value, units, currency, variant }: Props) => {
    return (
        <View
            style={{
                backgroundColor: colors[variant][500],
                justifyContent: "space-between",
                height: 100,
                flex: 1,
                padding: 10,
                borderRadius: 12,
                overflow: "hidden",
            }}
        >
            <View
                style={{
                    height: 0,
                    overflow: "visible",
                    width: " 150%",
                    right: "-50%",
                    top: "-300%",
                }}
            >
                <Image
                    source={require("@assets/testing.png")}
                    style={{
                        opacity: 0.1,
                        position: "absolute",

                        transform: [{ rotate: "-25deg" }],
                    }}
                />
            </View>
            <View style={{ flexDirection: "row", alignItems: "baseline", justifyContent: "space-between" }}>
                <Text
                    style={{
                        fontSize: typography.sm.fontSize,
                        color: colors.white,
                    }}
                >
                    {title}
                </Text>
                <Text
                    style={{
                        fontSize: typography.sm.fontSize,
                        color: colors.white,
                    }}
                >
                    {date}
                </Text>
            </View>
            <View
                style={{
                    display: "flex",
                }}
            >
                <View
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 5,
                    }}
                >
                    <DolarIcon size={20} color={colors.white} />
                    <View
                        style={{
                            display: "flex",
                            flexDirection: "row",
                            alignItems: "baseline",
                            gap: 2,
                        }}
                    >
                        <View
                            style={{
                                display: "flex",
                                flexDirection: "row",
                                alignItems: "baseline",
                            }}
                        >
                            <Text
                                style={{
                                    fontFamily: typography.fontBold.fontFamily,
                                    fontSize: typography.lg.fontSize,
                                    color: colors.white,
                                }}
                            >
                                {getInt(Number(value)).toLocaleString()}
                            </Text>
                            <Text
                                style={{
                                    fontFamily: typography.fontBold.fontFamily,
                                    fontSize: typography.xxs.fontSize,
                                    color: colors.white,
                                }}
                            >
                                .{getDecimalString(Number(value), 2)}
                            </Text>
                        </View>
                        <Text
                            style={{
                                fontFamily: typography.baseText.fontFamily,
                                fontSize: typography.xxs.fontSize,
                                color: colors.white,
                            }}
                        >
                            {currency}
                        </Text>
                    </View>
                </View>

                <View
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 5,
                    }}
                >
                    <CartIcon size={20} color={colors.white} />
                    <Text
                        style={{
                            fontFamily: typography.fontBold.fontFamily,
                            fontSize: typography.lg.fontSize,
                            color: colors.white,
                        }}
                    >
                        {units}
                    </Text>
                </View>
            </View>
        </View>
    );
};

export default HomeCardSmall;
