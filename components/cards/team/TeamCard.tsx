import React from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Svg, { Path } from "react-native-svg";

import colors from "@styles/colors";

const TeamCard = ({ user }: any) => {
    return (
        <TouchableOpacity style={styles.container} activeOpacity={1}>
            <View style={styles.leftContainer}>
                <Avatar username={user.username} image={user.image?.sm} />
                <Tag label="Allowed" color="green" />
            </View>
            <View style={styles.rightContainer}>
                <View style={styles.row}>
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "500",
                        }}
                    >
                        Username:
                    </Text>
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "600",
                        }}
                    >
                        {user.username}
                    </Text>
                </View>
                <View style={styles.row}>
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "500",
                        }}
                    >
                        Full name:
                    </Text>
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "600",
                        }}
                    >
                        {user.firstname} {user.lastname}
                    </Text>
                </View>
                <View style={styles.row}>
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "500",
                        }}
                    >
                        Phone:
                    </Text>
                    <View
                        style={{
                            flexDirection: "row",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <Svg width={14} height={15} viewBox="0 0 14 15" fill="none">
                            <Path
                                d="M3.86167 6.79417C4.70167 8.445 6.055 9.7925 7.70583 10.6383L8.98917 9.355C9.14667 9.1975 9.38 9.145 9.58417 9.215C10.2375 9.43083 10.9433 9.5475 11.6667 9.5475C11.9875 9.5475 12.25 9.81 12.25 10.1308V12.1667C12.25 12.4875 11.9875 12.75 11.6667 12.75C6.18917 12.75 1.75 8.31083 1.75 2.83333C1.75 2.5125 2.0125 2.25 2.33333 2.25H4.375C4.69583 2.25 4.95833 2.5125 4.95833 2.83333C4.95833 3.5625 5.075 4.2625 5.29083 4.91583C5.355 5.12 5.30833 5.3475 5.145 5.51083L3.86167 6.79417Z"
                                fill="#4A5568"
                            />
                        </Svg>
                        <Text
                            style={{
                                color: colors.gray[600],
                                fontSize: 14,
                                fontWeight: "600",
                            }}
                        >
                            {user.phone}
                        </Text>
                    </View>
                </View>
                <View style={styles.line} />
                <View style={styles.row}>
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "500",
                        }}
                    >
                        Role:
                    </Text>
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "600",
                        }}
                    >
                        {user.role}
                    </Text>
                </View>
            </View>
        </TouchableOpacity>
    );
};

export default TeamCard;

const Avatar = ({ username, image }: any) => {
    const firstLetter = username?.charAt(0).toUpperCase();

    return (
        <View
            style={{
                width: 110,
                height: 110,
                borderRadius: 70,
                overflow: "hidden",
                backgroundColor: "white",
                justifyContent: "center",
                alignItems: "center",
                borderColor: colors.blue[900],
                borderWidth: image ? 0 : 3,
            }}
        >
            {image ? (
                <Image
                    source={{ uri: image || "" }}
                    style={{
                        width: "100%",
                        height: "100%",
                        resizeMode: "cover",
                    }}
                />
            ) : (
                <View
                    style={{
                        width: "100%",
                        height: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor: colors.blue[300],
                    }}
                >
                    <Text
                        style={{
                            fontSize: 24,
                            fontWeight: "bold",
                            color: colors.blue[900],
                        }}
                    >
                        {firstLetter}
                    </Text>
                </View>
            )}
        </View>
    );
};

const Tag = ({ enabled }: any) => {
    if (enabled === false) {
        return (
            <View
                style={{
                    ...styles.tagContainer,
                    backgroundColor: colors.red[100],
                }}
            >
                <Text
                    style={{
                        ...styles.tagText,
                        color: colors.red[800],
                    }}
                >
                    Disabled
                </Text>
                <View>
                    <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <Path
                            d="M12.6663 4.27301L11.7263 3.33301L7.99967 7.05967L4.27301 3.33301L3.33301 4.27301L7.05967 7.99967L3.33301 11.7263L4.27301 12.6663L7.99967 8.93967L11.7263 12.6663L12.6663 11.7263L8.93967 7.99967L12.6663 4.27301Z"
                            fill="#822727"
                        />
                    </Svg>
                </View>
            </View>
        );
    }

    return (
        <View
            style={{
                ...styles.tagContainer,
                backgroundColor: colors.green[100],
            }}
        >
            <Text
                style={{
                    ...styles.tagText,
                    color: colors.green[800],
                }}
            >
                Enabled
            </Text>
            <View>
                <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <Path
                        d="M6.0001 10.7799L3.2201 7.9999L2.27344 8.9399L6.0001 12.6666L14.0001 4.66656L13.0601 3.72656L6.0001 10.7799Z"
                        fill="#25855A"
                    />
                </Svg>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: "100%",
        paddingTop: 15,
        paddingBottom: 15,
        paddingLeft: 10,
        paddingRight: 10,
        borderWidth: 1,
        borderColor: colors.primary[50],
        borderRadius: 12,
        backgroundColor: colors.gray[50],
        alignSelf: "center",
        flexDirection: "row",
        gap: 20,
    },
    leftContainer: {
        width: "30%",
        gap: 10,
    },
    rightContainer: {
        width: "65%",
        gap: 10,
        padding: 5,
    },
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    line: {
        width: "100%",
        height: 1,
        backgroundColor: colors.primary[50],
    },
    tagContainer: {
        paddingTop: 5,
        paddingBottom: 5,
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 6,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        gap: 5,
    },
    tagText: {
        fontSize: 14,
        fontWeight: "500",
    },
});
