import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Tag } from "@components/inputs/chips/Tag/Tag";
import { UpsellTypeProp } from "../../../store/upsellStore";
import { formatDate } from "@components/cards/orders/OrderCard";
import { router } from "expo-router";

const UpsellCard = ({ upsell }: { upsell: UpsellTypeProp }) => {
    const handleUpsellClick = () => {
        router.navigate({
            pathname: "/upsell/upsellDetails",
            params: { upsellID: upsell._id },
        });
    };

    return (
        <TouchableOpacity
            onPress={handleUpsellClick}
            activeOpacity={0.7}
            style={{
                paddingHorizontal: 15,
                paddingVertical: 10,
                borderRadius: 10,
                gap: 25,
                borderWidth: 1,
                borderColor: colors.gray[300],
                backgroundColor: colors.blue[50],
            }}
        >
            <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
                <View>
                    <Text
                        style={{
                            fontFamily: typography.fontBold.fontFamily,
                            color: colors.primary[500],
                            fontSize: 16,
                        }}
                    >
                        {upsell.name}
                    </Text>
                    <Text
                        style={{
                            color: colors.gray[500],
                            fontFamily: typography.fontMedium.fontFamily,
                            fontSize: 12,
                            marginRight: 10,
                        }}
                    >
                        {formatDate(String(upsell.createdAt))}
                    </Text>
                </View>
                <Tag
                    color={upsell.status === "shown" ? colors.primary[500] : colors.red[500]}
                    status={upsell.status === "shown" ? "Shown" : "Hidden"}
                    key={upsell._id}
                />
            </View>
            <View
                style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "flex-end",
                }}
            >
                <View>
                    <Text
                        style={{
                            fontSize: 16,
                            textTransform: "capitalize",
                            fontFamily: typography.fontSemibold.fontFamily,
                            color: colors.gray[600],
                            maxWidth: 300,
                        }}
                    >
                        {upsell.type}
                    </Text>
                </View>
                <Text
                    style={{
                        color: colors.primary[600],
                        fontFamily: typography.fontBold.fontFamily,
                        fontSize: 16,
                    }}
                >
                    Priority: {upsell.priority}
                </Text>
            </View>
        </TouchableOpacity>
    );
};

export default UpsellCard;

const styles = StyleSheet.create({
    cardContainer: {
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 10,
        gap: 25,
        borderWidth: 1,
        borderColor: colors.gray[300],
        backgroundColor: colors.blue[50],
    },
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    upsellName: {
        fontFamily: typography.fontBold.fontFamily,
        color: colors.primary[500],
        fontSize: 16,
    },
    upsellDate: {
        color: colors.gray[500],
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: 12,
        marginRight: 10,
    },
    footer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "flex-end",
    },
    upsellPriority: {
        fontSize: 18,
        textTransform: "capitalize",
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.gray[600],
        maxWidth: 300,
    },
    upsellType: {
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.gray[500],
        fontSize: 14,
        marginRight: 10,
    },
});
