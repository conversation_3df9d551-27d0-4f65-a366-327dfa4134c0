import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { Defs, Ellipse, LinearGradient, Stop, Svg } from "react-native-svg";

const BalanceCardGraphic = () => {
    return (
        <Svg width="148" height="90" viewBox="0 0 148 90">
            <Defs>
                <LinearGradient
                    id="grad1"
                    x1="182.246"
                    y1="0.193489"
                    x2="182.246"
                    y2="172.193"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#5563D9" />
                    <Stop offset="1" stopColor="#151E67" />
                </LinearGradient>
                <LinearGradient
                    id="grad2"
                    x1="182.246"
                    y1="0.193619"
                    x2="182.246"
                    y2="172.194"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#5563D9" />
                    <Stop offset="1" stopColor="#151E67" />
                </LinearGradient>
                <LinearGradient
                    id="grad3"
                    x1="182.247"
                    y1="0.193192"
                    x2="182.247"
                    y2="172.193"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#5563D9" />
                    <Stop offset="1" stopColor="#151E67" />
                </LinearGradient>
                <LinearGradient
                    id="grad4"
                    x1="182.247"
                    y1="0.192856"
                    x2="182.247"
                    y2="172.193"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#5563D9" />
                    <Stop offset="1" stopColor="#151E67" />
                </LinearGradient>
                <LinearGradient
                    id="grad5"
                    x1="147.674"
                    y1="0.192291"
                    x2="147.674"
                    y2="172.192"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#5563D9" />
                    <Stop offset="1" stopColor="#151E67" />
                </LinearGradient>
            </Defs>
            <Ellipse
                cx="182.246"
                cy="86.1935"
                rx="160.746"
                ry="86"
                transform="rotate(-21.935 182.246 86.1935)"
                fill="url(#grad1)"
            />
            <Ellipse
                cx="182.246"
                cy="86.1936"
                rx="160.746"
                ry="86"
                transform="rotate(-29.6104 182.246 86.1936)"
                fill="url(#grad2)"
            />
            <Ellipse
                cx="182.247"
                cy="86.1932"
                rx="160.746"
                ry="86"
                transform="rotate(-45 182.247 86.1932)"
                fill="url(#grad3)"
            />
            <Ellipse
                cx="182.247"
                cy="86.1929"
                rx="160.746"
                ry="86"
                transform="rotate(-60 182.247 86.1929)"
                fill="url(#grad4)"
            />
            <Ellipse
                cx="147.674"
                cy="86.1923"
                rx="160.746"
                ry="86"
                transform="rotate(-80.8689 147.674 86.1923)"
                fill="url(#grad5)"
            />
        </Svg>
    );
};

const BalanceCardContent = ({ value, currency }: { value: number; currency: string }) => {
    return (
        <View style={styles.content}>
            <Text style={styles?.label}>Balance</Text>
            <View>
                <Text style={styles.value}>
                    {value.toFixed(2)}
                    <Text style={styles.currency}>{currency}</Text>
                </Text>
            </View>
        </View>
    );
};

const BalanceCard = ({ value, currency }: { value: number; currency: string }) => {
    return (
        <View style={styles.card}>
            <BalanceCardContent value={value} currency={currency}></BalanceCardContent>
            <BalanceCardGraphic></BalanceCardGraphic>
        </View>
    );
};

const styles = StyleSheet.create({
    card: {
        borderRadius: 14,
        borderWidth: 1,
        borderColor: colors.blue[200],
        backgroundColor: colors.blue[50],
        height: 90,
        flexShrink: 0,
        flexDirection: "row",
        overflow: "hidden",
        width: "100%",
        justifyContent: "space-between",
        paddingLeft: 20,
    },
    content: {
        justifyContent: "center",
        alignItems: "flex-start",
        paddingVertical: 15,
    },
    label: {
        color: colors.blue[500],
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.lg.fontSize,
    },
    value: {
        color: colors.gray[900],
        fontFamily: typography.fontBold.fontFamily,
        fontSize: typography.lg.fontSize,
    },
    currency: {
        fontSize: typography.sm.fontSize,
    },
});

export default BalanceCard;
