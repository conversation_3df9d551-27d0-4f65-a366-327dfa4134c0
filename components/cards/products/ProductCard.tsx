import React from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Svg, { <PERSON>, <PERSON>, Path, Rect } from "react-native-svg";
import colors from "@styles/colors";
import { useStoreStore } from "../../../store/storeStore";
import { router } from "expo-router";
import { useProductStore } from "../../../store/productStore";
import { Product } from "../../../types/Product";

const ProductCard = ({ product }: { product: Product }) => {
    const { store } = useStoreStore();
    const { setSelectedProduct } = useProductStore();
    const truncatedName = product.name.length > 12 ? `${product.name.substring(0, 12)}...` : product.name;

    return (
        <TouchableOpacity
            style={styles.container}
            activeOpacity={1}
            onPress={() => {
                router.navigate(`/products/productDetails/${product.reference}`);
                setSelectedProduct(product);
            }}
        >
            <View style={styles.leftContainer}>
                <ProductImage
                    image={
                        product?.images && product?.images?.length > 0 && product?.images[0].sm
                            ? product?.images[0].sm
                            : ""
                    }
                />
            </View>
            <View style={styles.rightContainer}>
                <View style={styles.row}>
                    <View style={{ maxWidth: "45%" }}>
                        <Text
                            style={{
                                color: colors.gray[600],
                                fontSize: 14,
                                fontWeight: "600",
                                flexWrap: "wrap",
                            }}
                        >
                            {truncatedName}
                        </Text>
                        <Text
                            style={{
                                color: colors.gray[600],
                                fontSize: 14,
                                fontWeight: "500",
                            }}
                        >
                            #{product.reference}
                        </Text>
                    </View>
                    <Tag status={product.status} />
                </View>
                <View style={styles.row}>
                    <View
                        style={{
                            flexDirection: "row",
                            gap: 2,
                            alignItems: "flex-end",
                        }}
                    >
                        <Svg width={20} height={21} viewBox="0 0 20 21" fill="none">
                            <Path
                                d="M14.9997 5.50033H13.333C13.333 3.65866 11.8413 2.16699 9.99967 2.16699C8.15801 2.16699 6.66634 3.65866 6.66634 5.50033H4.99967C4.08301 5.50033 3.33301 6.25033 3.33301 7.16699V17.167C3.33301 18.0837 4.08301 18.8337 4.99967 18.8337H14.9997C15.9163 18.8337 16.6663 18.0837 16.6663 17.167V7.16699C16.6663 6.25033 15.9163 5.50033 14.9997 5.50033ZM9.99967 3.83366C10.9163 3.83366 11.6663 4.58366 11.6663 5.50033H8.33301C8.33301 4.58366 9.08301 3.83366 9.99967 3.83366ZM14.9997 17.167H4.99967V7.16699H6.66634V8.83366C6.66634 9.29199 7.04134 9.66699 7.49967 9.66699C7.95801 9.66699 8.33301 9.29199 8.33301 8.83366V7.16699H11.6663V8.83366C11.6663 9.29199 12.0413 9.66699 12.4997 9.66699C12.958 9.66699 13.333 9.29199 13.333 8.83366V7.16699H14.9997V17.167Z"
                                fill="#4A5568"
                            />
                        </Svg>
                        <Text
                            style={{
                                color: colors.gray[600],
                                fontSize: 14,
                                fontWeight: "600",
                            }}
                        >
                            Price
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: "row",
                            gap: 2,
                            alignItems: "flex-end",
                        }}
                    >
                        <Svg width={20} height={21} viewBox="0 0 20 21" fill="none">
                            <Path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M2.15515 4.32116C2.46771 4.0086 2.89163 3.83301 3.33366 3.83301H10.0003C10.4424 3.83301 10.8663 4.0086 11.1788 4.32116C11.4914 4.63372 11.667 5.05765 11.667 5.49967H13.822C14.2639 5.49977 14.6879 5.67539 15.0004 5.98792C15.0004 5.98789 15.0004 5.98795 15.0004 5.98792L17.8453 8.83283C18.1579 9.14532 18.3336 9.56917 18.3337 10.0112V13.833C18.3337 14.275 18.1581 14.699 17.8455 15.0115C17.5329 15.3241 17.109 15.4997 16.667 15.4997H16.524C16.4009 15.8479 16.2009 16.1679 15.9348 16.4341C15.4659 16.9029 14.83 17.1663 14.167 17.1663C13.504 17.1663 12.8681 16.9029 12.3992 16.4341C12.1331 16.1679 11.9331 15.8479 11.81 15.4997H11.667C11.3719 15.4997 11.0849 15.4214 10.8337 15.2764C10.5824 15.4214 10.2954 15.4997 10.0003 15.4997H8.19068C8.06755 15.8479 7.8676 16.1679 7.60143 16.4341C7.13259 16.9029 6.4967 17.1663 5.83366 17.1663C5.17062 17.1663 4.53473 16.9029 4.06589 16.4341C3.79972 16.1679 3.59977 15.8479 3.47663 15.4997H3.33366C2.89163 15.4997 2.46771 15.3241 2.15515 15.0115C1.84259 14.699 1.66699 14.275 1.66699 13.833V5.49967C1.66699 5.05765 1.84259 4.63372 2.15515 4.32116ZM5.00033 14.6663C5.00033 14.8874 5.08812 15.0993 5.2444 15.2556C5.40068 15.4119 5.61265 15.4997 5.83366 15.4997C6.05467 15.4997 6.26663 15.4119 6.42291 15.2556C6.5792 15.0993 6.66699 14.8874 6.66699 14.6663C6.66699 14.4453 6.5792 14.2334 6.42291 14.0771C6.26663 13.9208 6.05467 13.833 5.83366 13.833C5.61265 13.833 5.40068 13.9208 5.2444 14.0771C5.08812 14.2334 5.00033 14.4453 5.00033 14.6663ZM8.19068 13.833H10.0003V5.49967L3.33366 5.49967V13.833H3.47663C3.59977 13.4848 3.79972 13.1647 4.06589 12.8986C4.53473 12.4297 5.17062 12.1663 5.83366 12.1663C6.4967 12.1663 7.13259 12.4297 7.60143 12.8986C7.8676 13.1647 8.06755 13.4848 8.19068 13.833ZM11.667 13.833H11.81C11.9331 13.4848 12.1331 13.1647 12.3992 12.8986C12.8681 12.4297 13.504 12.1663 14.167 12.1663C14.83 12.1663 15.4659 12.4297 15.9348 12.8986C16.2009 13.1647 16.4009 13.4848 16.524 13.833H16.667V10.0115L13.8219 7.16643L11.667 7.16634V13.833ZM14.167 13.833C13.946 13.833 13.734 13.9208 13.5777 14.0771C13.4215 14.2334 13.3337 14.4453 13.3337 14.6663C13.3337 14.8874 13.4215 15.0993 13.5777 15.2556C13.734 15.4119 13.946 15.4997 14.167 15.4997C14.388 15.4997 14.6 15.4119 14.7562 15.2556C14.9125 15.0993 15.0003 14.8874 15.0003 14.6663C15.0003 14.4453 14.9125 14.2334 14.7562 14.0771C14.6 13.9208 14.388 13.833 14.167 13.833Z"
                                fill="#4A5568"
                            />
                        </Svg>
                        <Text
                            style={{
                                color: colors.gray[600],
                                fontSize: 14,
                                fontWeight: "600",
                            }}
                        >
                            Cost
                        </Text>
                    </View>
                </View>
                <View style={styles.row}>
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "700",
                        }}
                    >
                        {product.price} <Text style={{ fontWeight: "400" }}>{store.currency?.code}</Text>
                    </Text>
                    <Text
                        style={{
                            color: colors.gray[600],
                            fontSize: 14,
                            fontWeight: "700",
                        }}
                    >
                        {product.cost} <Text style={{ fontWeight: "400" }}>{store.currency?.code}</Text>
                    </Text>
                </View>
                <View style={styles.line} />
                <View style={styles.row}>
                    <View
                        style={{
                            flexDirection: "row",
                            gap: 2,
                            alignItems: "flex-end",
                        }}
                    >
                        <Svg width={24} height={25} viewBox="0 0 24 25" fill="none">
                            <Mask
                                id="mask0_8291_32348"
                                mask-type="alpha"
                                maskUnits="userSpaceOnUse"
                                x="0"
                                y="0"
                                width="24"
                                height="25"
                            >
                                <Rect y="0.5" width="24" height="24" fill="#D9D9D9" />
                            </Mask>
                            <G mask="url(#mask0_8291_32348)">
                                <Path
                                    d="M16.65 13.5002L11 7.8502L16.65 2.2002L22.3 7.8502L16.65 13.5002ZM3 11.5002V3.5002H11V11.5002H3ZM13 21.5002V13.5002H21V21.5002H13ZM3 21.5002V13.5002H11V21.5002H3ZM5 9.5002H9V5.5002H5V9.5002ZM16.675 10.7002L19.5 7.8752L16.675 5.0502L13.85 7.8752L16.675 10.7002ZM15 19.5002H19V15.5002H15V19.5002ZM5 19.5002H9V15.5002H5V19.5002Z"
                                    fill="#4A5568"
                                />
                            </G>
                        </Svg>
                        <Text
                            style={{
                                color: colors.gray[600],
                                fontSize: 14,
                                fontWeight: "500",
                            }}
                        >
                            {product.stock ? "In Stock" : "No Stock"}
                        </Text>
                    </View>
                    {product.stock && (
                        <Text
                            style={{
                                color: colors.gray[600],
                                fontSize: 14,
                                fontWeight: "600",
                            }}
                        >
                            {product.stock} <Text style={{ fontWeight: "400" }}>Units</Text>
                        </Text>
                    )}
                </View>
            </View>
        </TouchableOpacity>
    );
};

export default ProductCard;

export const ProductImage = ({ image }: any) => {
    return (
        <View
            style={{
                width: 130,
                height: 170,
                overflow: "hidden",
                backgroundColor: "white",
                justifyContent: "center",
                alignItems: "center",
                borderTopLeftRadius: 10,
                borderBottomLeftRadius: 10,
            }}
        >
            <Image
                source={{ uri: image }}
                style={{
                    width: "100%",
                    height: "100%",
                    resizeMode: "cover",
                }}
            />
        </View>
    );
};

const Tag = ({ status }: any) => {
    if (status === "hidden") {
        return (
            <View
                style={{
                    ...styles.tagContainer,
                    backgroundColor: colors.orange[100],
                }}
            >
                <Text
                    style={{
                        ...styles.tagText,
                        color: colors.orange[800],
                    }}
                >
                    Hidden
                </Text>
            </View>
        );
    }

    if (status === "outOfStock") {
        return (
            <View
                style={{
                    ...styles.tagContainer,
                    backgroundColor: colors.red[100],
                }}
            >
                <Text
                    style={{
                        ...styles.tagText,
                        color: colors.red[800],
                    }}
                >
                    Out of stock
                </Text>
            </View>
        );
    }

    return (
        <View
            style={{
                ...styles.tagContainer,
                backgroundColor: colors.green[100],
            }}
        >
            <Text
                style={{
                    ...styles.tagText,
                    color: colors.green[800],
                }}
            >
                Shown
            </Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: "100%",
        borderWidth: 1,
        borderColor: colors.primary[50],
        borderRadius: 12,
        backgroundColor: colors.gray[50],
        alignSelf: "center",
        flexDirection: "row",
    },
    leftContainer: {
        width: "35%",
        gap: 10,
    },
    rightContainer: {
        width: "63%",
        gap: 10,
        padding: 10,
    },
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    line: {
        width: "100%",
        height: 1,
        backgroundColor: colors.primary[50],
    },
    tagContainer: {
        paddingTop: 5,
        paddingBottom: 5,
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 6,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        gap: 5,
    },
    tagText: {
        fontSize: 14,
        fontWeight: "500",
    },
});
