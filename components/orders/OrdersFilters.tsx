import React, { memo, useCallback } from 'react';
import { View, ScrollView } from 'react-native';
import { Divider } from 'react-native-elements';
import Svg, { Path } from 'react-native-svg';
import FilterChip from '@components/stats/FilterChip';
import IconButton from '@components/inputs/buttons/IconButton';
import SelectDeliveryCompanyInput from '@components/Navigation/ModalView/SelectDeliveryCompanyInput';
import SelectProductInput from '@components/Navigation/ModalView/SelectProductInput';
import colors from '@styles/colors';
import { Product } from '../../types/Product';

// Constants
const STATUS_LIST = [
  'all',
  'attempt',
  'abandoned',
  'pending',
  'restored',
  'uploaded',
  'confirmed',
  'rejected',
  'packed',
  'cancelled',
  'deposit',
  'in transit',
  'delivered',
  'to be returned',
  'returned',
  'received',
  'exchange',
  'deleted',
] as const;

// Types
export type OrderStatus = typeof STATUS_LIST[number];

interface OrdersFiltersProps {
  /** Whether advanced filters are visible */
  isFiltersVisible: boolean;
  /** Currently active filter chip index */
  activeFilterChip: number;
  /** Currently selected delivery company */
  activeDeliveryCompany: string | null;
  /** Currently selected product */
  activeProductItem: Product | null;
  /** Whether the component is disabled (e.g., during loading) */
  disabled?: boolean;
  /** Callback when filter visibility toggles */
  onToggleFiltersVisibility: (visible: boolean) => void;
  /** Callback when status filter changes */
  onStatusFilterChange: (status: OrderStatus, index: number) => void;
  /** Callback when delivery company filter changes */
  onDeliveryCompanyChange: (deliveryCompany: string | null) => void;
  /** Callback when product filter changes */
  onProductChange: (product: Product | null) => void;
  /** Render inline with filter icon and chips in same row */
  renderInline?: boolean;
  /** Render only the advanced filters section */
  renderOnlyAdvanced?: boolean;
  /** Render only the filter icon */
  renderOnlyIcon?: boolean;
  /** Render only the status filter chips */
  renderOnlyChips?: boolean;
}

// Filter icon component - memoized to prevent re-renders
const FilterIcon = memo(({ color }: { color: string }) => (
  <Svg height="24" viewBox="0 -***********" width="24">
    <Path
      d="M400-240v-80h160v80H400ZM240-440v-80h480v80H240ZM120-640v-80h720v80H120Z"
      fill={color}
    />
  </Svg>
));

FilterIcon.displayName = 'FilterIcon';

// Status filter chips component - memoized for performance
const StatusFilterChips = memo<{
  activeFilterChip: number;
  disabled: boolean;
  onStatusFilterChange: (status: OrderStatus, index: number) => void;
}>(({ activeFilterChip, disabled, onStatusFilterChange }) => (
  <ScrollView
    horizontal
    showsHorizontalScrollIndicator={false}
    contentContainerStyle={{
      flexDirection: 'row',
      alignItems: 'center',
      gap: 5,
      paddingVertical: 2,
    }}
  >
    {STATUS_LIST.map((status, index) => (
      <FilterChip
        key={`${status}-${index}`} // More stable key
        disabled={disabled}
        active={activeFilterChip === index}
        title={status}
        type="orders"
        action={() => onStatusFilterChange(status, index)}
      />
    ))}
  </ScrollView>
));

StatusFilterChips.displayName = 'StatusFilterChips';

// Advanced filters component - memoized for performance
const AdvancedFilters = memo<{
  activeDeliveryCompany: string | null;
  activeProductItem: Product | null;
  onDeliveryCompanyChange: (deliveryCompany: string | null) => void;
  onProductChange: (product: Product | null) => void;
}>(({
  activeDeliveryCompany,
  activeProductItem,
  onDeliveryCompanyChange,
  onProductChange
}) => (
  <View style={{ flexDirection: 'row', width: '100%', gap: 5 }}>
    <View style={{ flex: 1 }}>
      <SelectDeliveryCompanyInput
        addAllCompanies
        deliveryCompany={activeDeliveryCompany}
        action={onDeliveryCompanyChange}
      />
    </View>
    <View style={{ flex: 1 }}>
      <SelectProductInput
        product={activeProductItem}
        action={onProductChange}
        addAllProducts
      />
    </View>
  </View>
));

AdvancedFilters.displayName = 'AdvancedFilters';

/**
 * OrdersFilters Component
 *
 * A comprehensive filtering component for orders with status chips,
 * delivery company selector, and product selector.
 * Optimized for performance with proper memoization.
 */
const OrdersFilters = memo<OrdersFiltersProps>(({
  isFiltersVisible,
  activeFilterChip,
  activeDeliveryCompany,
  activeProductItem,
  disabled = false,
  onToggleFiltersVisibility,
  onStatusFilterChange,
  onDeliveryCompanyChange,
  onProductChange,
  renderInline = false,
  renderOnlyAdvanced = false,
  renderOnlyIcon = false,
  renderOnlyChips = false,
}) => {
  // Handle filter toggle button press
  const handleTogglePress = useCallback(() => {
    onToggleFiltersVisibility(!isFiltersVisible);
  }, [isFiltersVisible, onToggleFiltersVisibility]);

  // Render only the advanced filters section
  if (renderOnlyAdvanced) {
    return (
      <AdvancedFilters
        activeDeliveryCompany={activeDeliveryCompany}
        activeProductItem={activeProductItem}
        onDeliveryCompanyChange={onDeliveryCompanyChange}
        onProductChange={onProductChange}
      />
    );
  }

  // Render only the filter icon
  if (renderOnlyIcon) {
    return (
      <IconButton
        disabled={disabled}
        backrgound={isFiltersVisible ? colors.gray[200] : undefined}
        action={handleTogglePress}
      >
        <View style={{ paddingTop: 2 }}>
          <FilterIcon color={colors.gray[800]} />
        </View>
      </IconButton>
    );
  }

  // Render only the status filter chips
  if (renderOnlyChips) {
    return (
      <StatusFilterChips
        activeFilterChip={activeFilterChip}
        disabled={disabled}
        onStatusFilterChange={onStatusFilterChange}
      />
    );
  }

  // Render inline (filter icon and chips in same row)
  if (renderInline) {
    return (
      <>
        {/* Filter Toggle Button */}
        <IconButton
          disabled={disabled}
          backrgound={isFiltersVisible ? colors.gray[200] : undefined}
          action={handleTogglePress}
        >
          <View style={{ paddingTop: 2 }}>
            <FilterIcon color={colors.gray[800]} />
          </View>
        </IconButton>

        {/* Status Filter Chips */}
        <StatusFilterChips
          activeFilterChip={activeFilterChip}
          disabled={disabled}
          onStatusFilterChange={onStatusFilterChange}
        />
      </>
    );
  }

  // Default render (full component with sections)
  return (
    <View style={{ gap: 5, backgroundColor: colors.gray[50] }}>
      {/* Filter Controls Row */}
      <View style={{ flexDirection: 'row', paddingLeft: 10 }}>
        {/* Filter Toggle Button */}
        <View style={{ flexDirection: 'row', gap: 5, paddingHorizontal: 5 }}>
          <IconButton
            disabled={disabled}
            backrgound={isFiltersVisible ? colors.gray[200] : undefined}
            action={handleTogglePress}
          >
            <View style={{ paddingTop: 2 }}>
              <FilterIcon color={colors.gray[800]} />
            </View>
          </IconButton>
        </View>

        {/* Status Filter Chips */}
        
          <StatusFilterChips
            activeFilterChip={activeFilterChip}
            disabled={disabled}
            onStatusFilterChange={onStatusFilterChange}
          />
      </View>

      {/* Advanced Filters Section */}
      <View style={{ gap: 7, paddingHorizontal: 10 }}>
        {/* Advanced Filters - Only render when visible for performance */}
        {isFiltersVisible && (
          <AdvancedFilters
            activeDeliveryCompany={activeDeliveryCompany}
            activeProductItem={activeProductItem}
            onDeliveryCompanyChange={onDeliveryCompanyChange}
            onProductChange={onProductChange}
          />
        )}
        <Divider />
      </View>
    </View>
  );
});

OrdersFilters.displayName = 'OrdersFilters';

export default OrdersFilters;
export { STATUS_LIST };