import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import Svg, { Circle, G, Path } from 'react-native-svg';
import Input from '@components/inputs/textInputs/Input';
import IconButton from '@components/inputs/buttons/IconButton';
import colors from '@styles/colors';

// Types
interface OrdersSearchProps {
  /** Whether search input is visible */
  isVisible: boolean;
  /** Current search value */
  searchValue: string;
  /** Whether the component is disabled (e.g., during loading) */
  disabled?: boolean;
  /** Callback when search visibility toggles */
  onToggleVisibility: (visible: boolean) => void;
  /** Callback when search value changes (debounced) */
  onSearchChange: (searchText: string) => void;
  /** Debounce delay in milliseconds */
  debounceDelay?: number;
  /** Render only the search icon toggle button */
  renderOnlyIcon?: boolean;
  /** Render only the search input field */
  renderOnlyInput?: boolean;
}

// Search icon component - memoized to prevent re-renders
const SearchIcon = memo(({ color, size = 17 }: { color: string; size?: number }) => (
  <Svg width={size} height={size} viewBox="0 0 20 20" fill="none">
    <G stroke={color} strokeWidth="2">
      <Circle cx="8.5" cy="8.5" r="6.5" />
      <Path d="M13 13l5 5" />
    </G>
  </Svg>
));

SearchIcon.displayName = 'SearchIcon';

/**
 * OrdersSearch Component
 *
 * A performant search component with debounced input and toggle functionality.
 * Follows React best practices with proper memoization and cleanup.
 */
const OrdersSearch = memo<OrdersSearchProps>(({
  isVisible,
  searchValue,
  disabled = false,
  onToggleVisibility,
  onSearchChange,
  debounceDelay = 300,
  renderOnlyIcon = false,
  renderOnlyInput = false,
}) => {
  // Local state for immediate UI updates (before debounce)
  const [localSearchValue, setLocalSearchValue] = useState(searchValue);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Sync local state with prop changes
  useEffect(() => {
    setLocalSearchValue(searchValue);
  }, [searchValue]);

  // Debounced search handler
  const handleDebouncedSearch = useCallback((text: string) => {
    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      onSearchChange(text);
    }, debounceDelay);
  }, [onSearchChange, debounceDelay]);

  // Handle input change
  const handleInputChange = useCallback((text: string) => {
    setLocalSearchValue(text);
    handleDebouncedSearch(text);
  }, [handleDebouncedSearch]);

  // Handle search button press (immediate search)
  const handleSearchPress = useCallback(() => {
    // Clear debounce and search immediately
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    onSearchChange(localSearchValue);
  }, [localSearchValue, onSearchChange]);

  // Handle input blur
  const handleInputBlur = useCallback(() => {
    onToggleVisibility(false);
  }, [onToggleVisibility]);

  // Handle submit editing (Enter key)
  const handleSubmitEditing = useCallback(() => {
    handleSearchPress();
  }, [handleSearchPress]);

  // Handle toggle button press
  const handleTogglePress = useCallback(() => {
    onToggleVisibility(!isVisible);
  }, [isVisible, onToggleVisibility]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Render only the search icon toggle button
  if (renderOnlyIcon) {
    return (
      <IconButton
        disabled={disabled}
        backrgound={isVisible ? colors.gray[200] : undefined}
        action={handleTogglePress}
      >
        <SearchIcon color={colors.gray[600]} />
      </IconButton>
    );
  }

  // Render only the search input field
  if (renderOnlyInput) {
    return (
      <Input
        noBottomPadding
        placeholder="Search orders..."
        onChange={handleInputChange}
        onIconPress={handleSearchPress}
        inputProps={{
          value: localSearchValue,
          onBlur: handleInputBlur,
          onSubmitEditing: handleSubmitEditing,
          returnKeyType: 'search',
          autoFocus: true, // Auto-focus when search becomes visible
          clearButtonMode: 'while-editing', // iOS clear button
        }}
        icon={<SearchIcon color={colors.gray[900]} />}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          flexShrink: 1,
          paddingHorizontal: 10,
          borderWidth: 1,
          borderColor: 'rgba(203, 213, 224, 1)',
          borderRadius: 5,
        }}
      />
    );
  }

  // Default render (both icon and input when visible)
  return (
    <View style={{ flexDirection: 'row', gap: 5, paddingHorizontal: 5 }}>
      {/* Search Toggle Button */}
      <IconButton
        disabled={disabled}
        backrgound={isVisible ? colors.gray[200] : undefined}
        action={handleTogglePress}
      >
        <SearchIcon color={colors.gray[600]} />
      </IconButton>

      {/* Search Input - Only render when visible for performance */}
      {isVisible && (
        <View style={{ flex: 1, paddingHorizontal: 5 }}>
          <Input
            noBottomPadding
            placeholder="Search orders..."
            onChange={handleInputChange}
            onIconPress={handleSearchPress}
            inputProps={{
              value: localSearchValue,
              onBlur: handleInputBlur,
              onSubmitEditing: handleSubmitEditing,
              returnKeyType: 'search',
              autoFocus: true, // Auto-focus when search becomes visible
              clearButtonMode: 'while-editing', // iOS clear button
            }}
            icon={<SearchIcon color={colors.gray[900]} />}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              flexShrink: 1,
              paddingHorizontal: 10,
              borderWidth: 1,
              borderColor: 'rgba(203, 213, 224, 1)',
              borderRadius: 5,
            }}
          />
        </View>
      )}
    </View>
  );
});

OrdersSearch.displayName = 'OrdersSearch';

export default OrdersSearch;