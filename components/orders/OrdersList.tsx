import React, { memo, useCallback, useMemo } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { FlatList, RefreshControl } from 'react-native-gesture-handler';
import Svg, { Path } from 'react-native-svg';
import OrderCard from '@components/cards/orders/OrderCard';
import colors from '@styles/colors';
import { typography } from '@styles/typography';
import { Order, Filter } from './types';

// Constants
const ITEM_HEIGHT = 120;
const ITEM_GAP = 10;
const ITEM_LAYOUT_HEIGHT = ITEM_HEIGHT + ITEM_GAP;

// Types
interface OrdersListProps {
  /** Array of orders to display */
  orders: Order[];
  /** Whether the list is currently loading */
  loading: boolean;
  /** Whether there are no more items to load */
  noMore: boolean;
  /** Whether selection mode is active */
  selectionMode: boolean;
  /** Array of selected order IDs */
  selectedOrdersId: string[];
  /** Current filter state for extraData */
  filter: Filter;
  /** Callback when reaching end of list */
  onEndReached: () => void;
  /** Callback when refreshing */
  onRefresh: () => void;
  /** Callback when selection changes */
  onSelectionChange: (ids: string[]) => void;
  /** Callback when delete is triggered */
  onDeleteOrder: (id: string) => void;
  /** Callback when modal visibility changes */
  onModalVisibilityChange: (visible: boolean) => void;
  /** Callback when modal mode changes */
  onModalModeChange: (mode: 'edit' | 'delete' | 'send' | 'restore') => void;
  /** Callback when swipeable close function is set */
  onSwipeableCloseSet: (closeFunction: (() => void) | undefined) => void;
}

// Empty state component - memoized for performance
const EmptyState = memo<{ loading: boolean }>(({ loading }) => {
  if (loading) return null;

  return (
    <View
      style={{
        height: 400,
        alignItems: 'center',
        justifyContent: 'center',
        gap: 16,
        opacity: 0.8,
      }}
    >
      <Svg width="64" height="64" viewBox="0 0 24 24" fill={colors.gray[300]}>
        <Path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z" />
      </Svg>
      <View style={{ alignItems: 'center', gap: 8 }}>
        <Text style={[typography.lg, typography.fontMedium, { color: colors.gray[600] }]}>
          No Orders Found
        </Text>
        <Text style={[typography.sm, { color: colors.gray[500], textAlign: 'center' }]}>
          Try adjusting your filters or create{'\n'}a new order to get started
        </Text>
      </View>
    </View>
  );
});

EmptyState.displayName = 'EmptyState';

// Footer component - memoized for performance
const ListFooter = memo<{
  loading: boolean;
  noMore: boolean;
  ordersCount: number;
}>(({ loading, noMore, ordersCount }) => (
  <View
    style={{
      paddingVertical: 20,
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
    }}
  >
    {loading && !noMore && (
      <ActivityIndicator size="small" color={colors.primary[400]} />
    )}
    {!loading && ordersCount > 0 && ordersCount < 9 && (
      <View style={{ alignItems: 'center', gap: 8 }}>
        <Text style={[typography.xs, { color: colors.gray[400] }]}>
          No more orders to display
        </Text>
      </View>
    )}
    {noMore && ordersCount >= 9 && (
      <View style={{ alignItems: 'center', gap: 8 }}>
        <Text style={[typography.sm, typography.fontMedium, { color: colors.gray[500] }]}>
          You've reached the end
        </Text>
        <Text style={[typography.xs, { color: colors.gray[400] }]}>
          No more orders to display
        </Text>
      </View>
    )}
  </View>
));

ListFooter.displayName = 'ListFooter';

/**
 * OrdersList Component
 *
 * A highly optimized FlatList component for displaying orders.
 * Includes performance optimizations, empty states, and loading indicators.
 */
const OrdersList = memo<OrdersListProps>(({
  orders,
  loading,
  noMore,
  selectionMode,
  selectedOrdersId,
  filter,
  onEndReached,
  onRefresh,
  onSelectionChange,
  onDeleteOrder,
  onModalVisibilityChange,
  onModalModeChange,
  onSwipeableCloseSet,
}) => {
  // Memoized render function for order items
  const renderOrder = useCallback(
    ({ item }: { item: Order }) => (
      <OrderCard
        order={item}
        selectionMode={selectionMode}
        selectedOrdersId={selectedOrdersId}
        setSelectedOrdersId={onSelectionChange}
        setDeleteId={onDeleteOrder}
        setSwipeableClose={onSwipeableCloseSet}
        setModalVisible={onModalVisibilityChange}
        setModalMode={onModalModeChange}
      />
    ),
    [
      selectionMode,
      selectedOrdersId,
      onSelectionChange,
      onDeleteOrder,
      onSwipeableCloseSet,
      onModalVisibilityChange,
      onModalModeChange,
    ]
  );

  // Memoized key extractor
  const keyExtractor = useCallback((item: Order) => `order-${item._id}`, []);

  // Memoized item layout for performance
  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_LAYOUT_HEIGHT * index,
      index,
    }),
    []
  );

  // Memoized end reached handler
  const handleEndReached = useCallback(() => {
    onEndReached();
  }, [onEndReached]);

  // Memoized extraData for FlatList optimization
  const extraData = useMemo(
    () => ({
      selectionMode,
      selectedCount: selectedOrdersId.length,
      filter,
    }),
    [selectionMode, selectedOrdersId.length, filter]
  );

  // Memoized content container style
  const contentContainerStyle = useMemo(
    () => ({
      gap: ITEM_GAP,
      paddingVertical: 10,
      // Extra padding when exactly 2 items to prevent FAB interference
      paddingBottom: orders.length === 2 ? 120 : 80,
    }),
    [orders.length]
  );

  return (
    <FlatList
      style={{ backgroundColor: colors.gray[50] }}
      contentContainerStyle={contentContainerStyle}
      data={orders}
      extraData={extraData}
      renderItem={renderOrder}
      keyExtractor={keyExtractor}
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.3}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={onRefresh} />
      }
      // Performance optimizations
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      updateCellsBatchingPeriod={50}
      initialNumToRender={10}
      windowSize={10}
      getItemLayout={getItemLayout}
      // Components
      ListEmptyComponent={<EmptyState loading={loading} />}
      ListFooterComponent={
        <ListFooter
          loading={loading}
          noMore={noMore}
          ordersCount={orders.length}
        />
      }
    />
  );
});

OrdersList.displayName = 'OrdersList';

export default OrdersList;