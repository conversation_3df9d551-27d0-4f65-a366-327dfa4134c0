import { Status } from "../../types/Order";
import { Product } from "../../types/Product";

export type Filter = {
    search?: string;
    abandoned?: boolean;
    status?: string;
    product?: string;
    deliveryCompany?: string;
    date?: {
        from?: Date | undefined;
        to?: Date | undefined;
    };
    deleted?: boolean;
    archived?: boolean;
};

// export type ProductsListItem = {
//     _id: string;
//     reference: number;
//     name: string;
//     price: number;
//     variants: Variant[];
//     deliveryPrice: number;
//     images: Image[];
// };

export type ProductsListItem = {
    _id: string;
    name: string;
    slug: string;
    sku: string;
    images: Image[];
    price: number;
    comparePrice: number;
    cost: number;
    deliveryPrice: number;
    deliveryCost: number;
    trackStock: boolean;
    defaultCombination: any[];
    relatedProducts: any[];
    store: string;
    categories: any[];
    campaignIds: any[];
    status: string;
    isDeleted: boolean;
    expiryDate: Date | null;
    discounts: any[];
    variants: Variant[];
    reviews: any[];
    coupons: any[];
    createdAt: Date;
    updatedAt: Date;
    reference: number;
    __v: number;
    upsell?: string;
};

export type ProductsList = Array<Partial<Product>>;

export type Variant = {
    name?: string;
    type?: "text" | "color";
    values?: string[];
};

export type Image = {
    sm?: string;
    md?: string;
    lg?: string;
};

export type History = {
    status?: Status;
    timestamp?: Date;
    actionTakerComment?: string;
    actionTaker?: string;
};

export type Order = {
    _id: string;
    attempt?: number;
    reference: number;
    customer: Partial<Customer>;
    note?: string;
    actionTakerComment?: string;
    cart: Cart;
    status: Status;
    rejectionReason?: string;
    total: {
        deliveryPrice?: number;
        deliveryCost?: number;
        basePrice?: number;
        totalPrice?: number;
    };
    deliveryCompany: string; //"none" | "aramex" | "adex" | "intigo" | "first" | "mylerz"
    barcode?: string;
    history: History[];
    store?: string;
    isDeleted?: boolean;
    expiryDate?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    paymentStatus?: string;
    billedAmount?: number;
    deliveryTrackable?: boolean;
    label?: string;
    duplicated?: boolean;
    refunded: boolean;
    isTest?: boolean;
    __v?: number;
    count?: number;
};

export type Cart = CartItem[];

export type CartItem = {
    product: Partial<Product>;
    quantity: number;
    selectedVariants: {
        name: string;
        type: "text" | "color" | "image";
        value: string;
    }[];
    pricePerUnit: number;
};

export type Customer = {
    _id: string; // mongoose _id
    name?: string;
    phone?: string;
    address?: string;
    note?: string;
    email?: string;
    city?: string;
    town?: string;
    stopdesk?: string;
    country?: string;
    postalCode?: number;
    orders?: string[]; // mongoose _id
    store?: string; // mongoose _id
    isDeleted?: boolean;
    expiryDate?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    rejectionReason?: string;
};

export type OrderResponseProp = {
    count?: number;
    data: Order[];
    message?: string;
    success?: boolean;
};
