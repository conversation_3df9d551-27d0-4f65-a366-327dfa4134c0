import { StyleSheet } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";

export const styles = StyleSheet.create({
    orderContainer: {
        backgroundColor: colors.primary["600"],
        borderRadius: 15,
        paddingTop: 10,
        paddingHorizontal: 15,
        paddingBottom: 15,
        alignItems: "center",
        gap: 10,
    },
    title: {
        fontSize: 24,
        color: colors.white,
        fontWeight: "bold",
    },
    total: {
        fontSize: 24,
        color: colors.secondary["400"],
        fontWeight: "bold",
    },
    detailsContainer: {
        gap: 10,
        width: "100%",
    },
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    column: {
        flex: 1,
        alignItems: "flex-start",
    },
    columnRight: {
        flex: 1,
        alignItems: "flex-end",
    },
    statusButton: {
        backgroundColor: colors.secondary["400"],
        borderRadius: 10,
        paddingVertical: 5,
        paddingHorizontal: 10,
        alignItems: "center",
    },
    statusButtonText: {
        fontSize: 16,
        fontWeight: "bold",
        color: colors.primary["600"],
    },
    products: {},
    deliveryImage: {
        height: 30,
    },
    clientContainer: {
        backgroundColor: colors.gray["50"],
        elevation: 3,
        borderRadius: 15,
        paddingVertical: 20,

        alignItems: "stretch",
        shadowColor: colors.gray["900"], // IOS
        shadowOffset: { height: 1, width: 0 }, // IOS
        shadowOpacity: 0.2, // IOS
        shadowRadius: 1.5, //IOS
    },
    callButton: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: colors.green["500"],
        borderRadius: 10,
        paddingVertical: 12,
        paddingHorizontal: 15,
        gap: 15,
        justifyContent: "center",
    },
    callButtonText: {
        color: colors.white,
        fontSize: 16,
        fontWeight: "bold",
    },
    productContainer: {
        flexDirection: "column",
        borderRadius: 10,
        alignItems: "center",
        overflow: "hidden",
        borderColor: colors.primary["200"],
        borderWidth: 1,
        width: 140,
    },
    productImage: {
        width: 140,
        height: 90,
    },
    productInfo: {
        paddingVertical: 5,
        paddingHorizontal: 10,
        justifyContent: "space-between",
        alignItems: "stretch",
        alignSelf: "stretch",
        gap: 5,
    },
    productName: {
        fontSize: 14,
        fontFamily: typography.fontBold.fontFamily,
        color: colors.gray["800"],
        height: 40,
    },
    productDetails: {
        flexDirection: "row",
        justifyContent: "space-between",
        fontSize: 12,
        alignItems: "baseline",
    },
    productDetailElement: {
        fontSize: 16,
        color: colors.gray["700"],
        fontFamily: typography.fontBold.fontFamily,
    },
    grayLabel: {
        fontSize: 14,
        color: colors.gray["700"],
        fontFamily: typography.fontMedium.fontFamily,
    },
    grayLabelForClient: {
        fontSize: 14,
        color: colors.gray["600"],
        fontFamily: typography.fontMedium.fontFamily,
    },
    grayValue: {
        fontSize: 16,
        color: colors.gray["600"],
        fontFamily: typography.fontSemibold.fontFamily,
    },
    label: {
        fontSize: 14,
        color: colors.primary["50"],
        fontFamily: typography.fontMedium.fontFamily,
    },
    value: {
        fontFamily: typography.fontBold.fontFamily,
        fontSize: 16,
        color: colors.primary["50"],
    },
    historyCardContainer: {
        padding: 10,
        borderRadius: 10,
        borderWidth: 1,
        borderColor: colors.gray[300],
        gap: 5,
        width: "100%",
    },
    historyCardlabel: {
        fontSize: 14,
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.gray[500],
    },
    historyCardValue: {
        fontSize: 14,
        fontFamily: typography.fontBold.fontFamily,
        color: colors.gray[500],
    },
    historyCardRow: { flexDirection: "row", justifyContent: "space-between", alignItems: "center" },
    historyStatus: {
        fontSize: 16,
        color: "#6A1B9A",
    },
    historyUser: {
        fontSize: 14,
        color: "#888888",
    },
    historyContainer: {
        paddingVertical: 10,
    },
    historyItem: {
        alignItems: "center",
    },
    historyTitle: {
        alignSelf: "center",
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.gray[800],
        fontSize: 18,
    },
    TouchableOpacityStyle: {
        position: "absolute",
        width: 50,
        height: 50,
        alignItems: "center",
        justifyContent: "center",
        right: 30,
        bottom: 30,
    },
    FloatingButtonStyle: {
        resizeMode: "contain",
        width: 50,
        height: 50,
    },
    floatingButton: {
        position: "absolute",
        zIndex: 200,
        width: 60,
        height: 60,
        borderRadius: 30,
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: colors.black,
        elevation: 3,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    editButton: {
        bottom: 10,
        right: 10,
    },
    swipeDownButton: {
        bottom: 10,
        left: 10,
    },
});
