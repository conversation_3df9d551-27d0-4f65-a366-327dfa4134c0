import React, { useEffect, useLayoutEffect, useState } from "react";
import { Image, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from "react-native";
import { Path, Svg } from "react-native-svg";
import { typography } from "@styles/typography";
import colors from "@styles/colors";
import RNPickerSelect from "react-native-picker-select";
import Input from "@components/inputs/textInputs/Input";
import Divider from "@components/dividers/Divider";
import * as orderTypes from "@components/orders/types";
import { TabsContentCellDelete } from "@components/icons/BudgetManagerIcons";
import { useOrderStore } from "../../store/orders";
import { useStoreStore } from "../../store/storeStore";
import { useLocalSearchParams, useRouter } from "expo-router";

const AddOrderForm = () => {
    const [formValues, setFormValues] = useState<any>({
        customer: {},
        cart: [],
        status: "pending",
        total: {
            deliveryPrice: 0,
            deliveryCost: 0,
            totalPrice: 0,
        },
        deliveryCompany: "",
        note: "",
        analyticsData: {},
        archived: false,
    });
    const [selectedValue, setSelectedValue] = useState<String>(null);
    const { products, getProducts } = useOrderStore();
    const { store } = useStoreStore();

    useLayoutEffect(() => {
        getProducts();
    }, []);

    const router = useRouter();

    useLayoutEffect(() => {
        router.setParams({ newOrder: JSON.stringify({ ...formValues }) });
    }, [formValues]);

    const TotalPriceCard = () => {
        if (!formValues?.cart) {
            return (
                <View style={styles.totalPriceCard}>
                    <Text style={styles.totalPriceTextLeft}>No Products In Order</Text>
                </View>
            );
        }

        let totalPrice = formValues?.cart?.reduce((acc: number, product: orderTypes.CartItem) => acc + product.pricePerUnit * product.quantity, 0);
        totalPrice += formValues?.total?.deliveryPrice;

        if (formValues?.cart?.length === 0) {
            return (
                <View style={styles.totalPriceCard}>
                    <Text style={styles.totalPriceTextLeft}>No Products In Order</Text>
                </View>
            );
        }

        return (
            <View style={styles.totalPriceCard}>
                <Text style={styles.totalPriceTextLeft}>Total Price:</Text>
                <Text style={styles.totalPriceTextRight}>
                    {totalPrice || 0} {store.currency?.code}
                </Text>
            </View>
        );
    };

    const OrderProducts = ({ cart, onDelete }: { cart: orderTypes.Cart; onDelete: (productID: string) => void }) => {
        if (cart.length > 0) {
            return (
                <ScrollView horizontal={true}>
                    <View style={{ flexDirection: "row", flexWrap: "wrap", paddingVertical: 15 }}>
                        {cart.map((product, index) => (
                            <View key={index} style={styles.productContainer}>
                                <Image source={{ uri: product.product?.images[0].sm }} style={styles.productImage} />
                                <View style={styles.productInfo}>
                                    <Text style={styles.productName}>
                                        {product.product ? product.product.name : ""}
                                    </Text>
                                    <Text style={styles.productDetails}>
                                        {product.quantity} x{" "}
                                        {product.product && product.product.price ? product.product.price : 0}{" "}
                                        {store.currency?.code}
                                    </Text>
                                    <Text style={styles.productDetails}>
                                        {product.selectedVariants && product.selectedVariants.length > 0
                                            ? product.selectedVariants.map((variant) => variant.value).join(", ")
                                            : ""}
                                    </Text>
                                </View>
                                <TouchableOpacity
                                    style={styles.deleteButton}
                                    onPress={() => onDelete(product.product._id)}
                                >
                                    <TabsContentCellDelete />
                                </TouchableOpacity>
                            </View>
                        ))}
                    </View>
                </ScrollView>
            );
        } else {
            return <Text style={{ textAlign: "center", paddingVertical: 20 }}>No products in the cart</Text>;
        }
    };

    const handleUpdate = (value: any) => {
        setFormValues(value);
        router.setParams(value);
    };

    return (
        <ScrollView contentContainerStyle={styles.container}>
            <View style={styles.orderContainer}>
                <Text style={styles.sectionTitle}>Delivery Details</Text>

                <View style={styles.element}>
                    <Text style={styles?.label}>Status</Text>

                    <RNPickerSelect
                        placeholder={{ label: "Select Status", value: null }}
                        onValueChange={(value) => {
                            setFormValues({ ...formValues, status: value });
                        }}
                        items={[
                            { value: "pending", label: "Pending" },
                            { value: "confirmed", label: "Confirmed" },
                            { value: "exchange", label: "Exchange" },
                            { value: "packed", label: "Packed" },
                        ]}
                        useNativeAndroidPickerStyle={false}
                        Icon={() => (
                            <Svg height="24" viewBox="0 -***********" width="24">
                                <Path d="M480-345 240-585l56-56 ***********-184 56 56-240 240Z" />
                            </Svg>
                        )}
                        style={{
                            iconContainer: styles.iconContainer,
                            inputAndroid: styles.inputAndroid,
                            inputIOS: styles.inputAndroid,
                        }}
                    />
                </View>
                <View style={styles.element}>
                    <Text style={styles?.label}>Delivery Company</Text>

                    <RNPickerSelect
                        placeholder={{ label: "Select Delivery Company", value: null }}
                        onValueChange={(value) => {
                            setFormValues({ ...formValues, deliveryCompany: value });
                        }}
                        items={store.integrations
                            .filter(
                                (integration) =>
                                    !["microsoft-clarity", "google-analytics", "facebook"].includes(integration.ref)
                            )
                            .map((integration) => ({
                                value: integration.ref,
                                label: integration.ref.charAt(0).toUpperCase() + integration.ref.slice(1),
                            }))}
                        useNativeAndroidPickerStyle={false}
                        Icon={() => (
                            <Svg height="24" viewBox="0 -***********" width="24">
                                <Path d="M480-345 240-585l56-56 ***********-184 56 56-240 240Z" />
                            </Svg>
                        )}
                        style={{
                            iconContainer: styles.iconContainer,
                            inputAndroid: styles.inputAndroid,
                            inputIOS: styles.inputAndroid,
                        }}
                    />
                </View>
                <View style={styles.element}>
                    <Text style={styles?.label}>Private Note</Text>

                    <Input
                        inputProps={{
                            defaultValue: "",
                        }}
                        onChange={(value) => {
                            setFormValues({ ...formValues, note: value });
                        }}
                        style={inputStyles}
                    />
                </View>
            </View>
            <View style={styles.orderContainer}>
                <Text style={styles.sectionTitle}>Customer Details</Text>
                <View>
                    <Text style={styles?.label}>Name</Text>
                    <View style={styles.element}>
                        <Input
                            placeholder="Name"
                            inputProps={{
                                defaultValue: "",
                            }}
                            style={inputStyles}
                            onChange={(value) => {
                                setFormValues({ ...formValues, customer: { ...formValues?.customer, name: value } });
                                // router.setParams
                            }}
                        />
                    </View>
                    <Text style={styles?.label}>Email</Text>
                    <View style={styles.element}>
                        <Input
                            inputProps={{
                                defaultValue: "",
                            }}
                            placeholder={"Email"}
                            style={inputStyles}
                            isEditable={true}
                            onChange={(value) => {
                                setFormValues({ ...formValues, customer: { ...formValues?.customer, email: value } });
                            }}
                        />
                    </View>
                    <Text style={styles?.label}>Address</Text>
                    <View style={styles.element}>
                        <Input
                            inputProps={{
                                defaultValue: "",
                            }}
                            placeholder={"Address"}
                            style={inputStyles}
                            onChange={(value) => {
                                setFormValues({ ...formValues, customer: { ...formValues?.customer, address: value } });
                            }}
                        />
                    </View>
                    <Text style={styles?.label}>Phone</Text>
                    <View style={styles.element}>
                        <Input
                            inputProps={{
                                defaultValue: "",
                            }}
                            placeholder={"Phone"}
                            style={inputStyles}
                            onChange={(value) => {
                                setFormValues({ ...formValues, customer: { ...formValues?.customer, phone: value } });
                            }}
                        />
                    </View>
                    <Text style={styles?.label}>Customer Note</Text>
                    <View style={styles.element}>
                        <Input
                            inputProps={{
                                defaultValue: "",
                            }}
                            onChange={(value) => {
                                setFormValues({ ...formValues, customer: { ...formValues?.customer, note: value } });
                            }}
                            style={inputStyles}
                        />
                    </View>
                    <Text style={styles?.label}>City</Text>
                    <View style={styles.element}>
                        <RNPickerSelect
                            placeholder={{ label: "Select City", value: null }}
                            onValueChange={(value) => {
                                setFormValues({ ...formValues, customer: { ...formValues?.customer, city: value } });
                            }}
                            items={[
                                "Ariana",
                                "Beja",
                                "Ben Arous",
                                "Bizerte",
                                "Gabes",
                                "Gafsa",
                                "Jendouba",
                                "Kasserine",
                                "Kef",
                                "Mahdia",
                                "Manouba",
                                "Monastir",
                                "Nabeul",
                                "Sfax",
                                "Sidi Bouzid",
                                "Sousse",
                                "Siliana",
                                "Tataouine",
                                "Tozeur",
                                "Tunis",
                                "Zaghouan",
                                "Medenine",
                                "Kebili",
                                "Kairouan",
                            ].map((city) => ({
                                value: city,
                                label: city,
                            }))}
                            useNativeAndroidPickerStyle={false}
                            Icon={() => (
                                <Svg height="24" viewBox="0 -***********" width="24">
                                    <Path d="M480-345 240-585l56-56 ***********-184 56 56-240 240Z" />
                                </Svg>
                            )}
                            style={{
                                iconContainer: styles.iconContainer,
                                inputAndroid: styles.inputAndroid,
                                inputIOS: styles.inputAndroid,
                            }}
                        />
                    </View>
                </View>
            </View>
            <View style={styles.orderContainer}>
                <Text style={styles.sectionTitle}>Order Details</Text>
                <View>
                    {Platform.OS == "ios" ? (
                        <RNPickerSelect
                            value={null}
                            placeholder={{ label: "Select Product", value: null }}
                            onDonePress={() => {
                                if (products.length === 0 || !selectedValue) return;
                                const selectedProduct = products.find((product) => product._id === selectedValue);
                                const foundProduct = formValues?.cart?.find(
                                    (product: orderTypes.CartItem) => product.product._id === selectedValue
                                );
                                if (foundProduct) {
                                    handleUpdate({
                                        ...formValues,
                                        cart: formValues?.cart?.map((product) =>
                                            product.product._id === selectedValue
                                                ? {
                                                      ...product,
                                                      quantity: product.quantity + 1,
                                                  }
                                                : product
                                        ),
                                        total: {
                                            ...formValues?.total,
                                            totalPrice: formValues?.total?.totalPrice + selectedProduct.price,
                                        },
                                    });
                                    return;
                                } else {
                                    handleUpdate({
                                        ...formValues,
                                        cart: [
                                            ...(formValues?.cart || []),
                                            {
                                                product: selectedProduct,
                                                quantity: 1,
                                                selectedVariants: [],
                                                pricePerUnit: selectedProduct.price,
                                            },
                                        ],
                                        total: {
                                            ...formValues?.total,
                                            totalPrice: formValues?.total?.totalPrice + selectedProduct.price,
                                        },
                                    });
                                }
                            }}
                            useNativeAndroidPickerStyle={false}
                            style={{
                                iconContainer: styles.iconContainer,
                                inputAndroid: styles.inputAndroid,
                                inputIOS: styles.inputAndroid,
                            }}
                            onValueChange={(value) => {
                                if (value !== null) {
                                    setSelectedValue(value);
                                }
                            }}
                            items={products.map((product) => ({
                                value: product._id,
                                label: product.name,
                            }))}
                        />
                    ) : (
                        <RNPickerSelect
                            value={null}
                            placeholder={{ label: "Select Product", value: null }}
                            onValueChange={(value) => {
                                if (products.length === 0) return;
                                const selectedProduct = products.find((product) => product._id === value);
                                const foundProduct = formValues?.cart?.find(
                                    (product: orderTypes.CartItem) => product.product._id === value
                                );
                                if (foundProduct) {
                                    handleUpdate({
                                        ...formValues,
                                        cart: formValues?.cart?.map((product: orderTypes.CartItem) =>
                                            product.product._id === value
                                                ? {
                                                      ...product,
                                                      quantity: product.quantity + 1,
                                                  }
                                                : product
                                        ),
                                        total: {
                                            ...formValues?.total,
                                            totalPrice: formValues?.total?.totalPrice + selectedProduct.price,
                                        },
                                    });
                                    return;
                                }
                                if (selectedProduct) {
                                    handleUpdate({
                                        ...formValues,
                                        cart: [
                                            ...formValues?.cart,
                                            {
                                                product: selectedProduct,
                                                quantity: 1,
                                                selectedVariants: [],
                                                pricePerUnit: selectedProduct.price,
                                            },
                                        ],
                                        total: {
                                            ...formValues?.total,
                                            totalPrice: formValues?.total?.totalPrice + selectedProduct.price,
                                        },
                                    });
                                }
                            }}
                            items={products.map((product) => ({
                                value: product._id,
                                label: product.name,
                            }))}
                            useNativeAndroidPickerStyle={false}
                            style={{
                                iconContainer: styles.iconContainer,
                                inputAndroid: styles.inputAndroid,
                            }}
                        />
                    )}
                    <OrderProducts
                        cart={formValues?.cart || []}
                        onDelete={(productID: string) => {
                            let foundProduct = formValues?.cart?.find((product: orderTypes.CartItem) => product.product._id === productID);
                            if (foundProduct && foundProduct.quantity > 1) {
                                handleUpdate({
                                    ...formValues,
                                    cart: formValues?.cart?.map((product: orderTypes.CartItem) =>
                                        product.product._id === productID
                                            ? {
                                                  ...product,
                                                  quantity: product.quantity - 1,
                                              }
                                            : product
                                    ),
                                    total: {
                                        ...formValues?.total,
                                        totalPrice: formValues?.total?.totalPrice - foundProduct.pricePerUnit,
                                    },
                                });
                                return;
                            }
                            if (foundProduct) {
                                handleUpdate({
                                    ...formValues,
                                    cart: formValues?.cart?.filter((product: orderTypes.CartItem) => product.product._id !== productID),
                                    total: {
                                        ...formValues?.total,
                                        totalPrice:
                                            formValues?.total?.totalPrice -
                                            foundProduct.pricePerUnit * foundProduct.quantity,
                                    },
                                });
                            }
                        }}
                    />
                </View>

                <Divider color={colors.gray[200]} />
                <View style={styles.element}>
                    <Text style={styles?.label}>Delivery Cost</Text>

                    <Input
                        inputProps={{
                            defaultValue: "",
                        }}
                        style={inputStyles}
                        isEditable={true}
                        onChange={(value) => {
                            handleUpdate({
                                ...formValues,
                                total: { ...formValues?.total, deliveryCost: Number.parseFloat(value) },
                            });
                        }}
                    />
                    <Text style={styles?.label}>Delivery Price</Text>

                    <Input
                        inputProps={{
                            defaultValue: "",
                        }}
                        placeholder={"Delivery Price"}
                        style={inputStyles}
                        isEditable={true}
                        onChange={(value) => {
                            handleUpdate({
                                ...formValues,
                                total: { ...formValues?.total, deliveryPrice: Number.parseFloat(value) },
                            });
                        }}
                    />
                </View>
                <Divider color={colors.gray[200]} />
                <TotalPriceCard />
            </View>
        </ScrollView>
    );
};

export default AddOrderForm;

const inputStyles: ViewStyle = {
    width: "100%",
    height: 50,
    padding: 10,
    borderWidth: 1,
    borderColor: colors.gray[400],
    borderRadius: 5,
    backgroundColor: colors.white,
};

const styles = StyleSheet.create({
    container: {
        paddingVertical: 20,
        paddingHorizontal: 10,
        gap: 20,
        backgroundColor: colors.gray[100],
    },
    orderContainer: {
        backgroundColor: colors.white,
        borderRadius: 13,
        padding: 15,
        gap: 10,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 5,
        elevation: 3,
        paddingVertical: 20,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: colors.primary[500],
        textAlign: "center",
    },
    label: {
        fontSize: 14,
        fontWeight: "bold",
        color: colors.gray[800],
    },
    element: {
        width: "100%",
        backgroundColor: colors.white,
        gap: 5,
        borderRadius: 5,
        paddingTop: 5,
    },
    iconContainer: {
        height: "100%",
        justifyContent: "center",
        paddingRight: 10,
    },
    inputAndroid: {
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.black,
        borderWidth: 1,
        borderColor: colors.gray[400],
        borderRadius: 5,
        padding: 10,
        backgroundColor: colors.white,
    },
    actionsContainer: {
        flexDirection: "column",
        justifyContent: "space-between",
    },
    actionButton: {
        flex: 1,
    },
    productContainer: {
        backgroundColor: colors.white,
        borderRadius: 10,
        padding: 10,
        alignItems: "center",
        borderWidth: 1,
        borderColor: colors.gray[300],
    },
    productImage: {
        width: 70,
        height: 70,
        borderRadius: 5,
    },
    productInfo: {
        alignItems: "center",
    },
    productName: {
        fontSize: 14,
        fontWeight: "bold",
        color: colors.black,
    },
    productDetails: {
        fontSize: 12,
        color: colors.gray[600],
    },
    deleteButton: {
        position: "absolute",
        top: 5,
        right: 5,
        padding: 5,
    },
    totalPriceCard: {
        backgroundColor: colors.white,
        padding: 10,
        borderRadius: 5,
        flex: 1,
        flexDirection: "row",
        justifyContent: "space-between",
    },
    totalPriceTextLeft: {
        fontSize: 18,
        fontWeight: "bold",
        color: colors.black,
        alignItems: "flex-start",
    },
    totalPriceTextRight: {
        fontSize: 18,
        fontWeight: "bold",
        color: colors.black,
        alignItems: "flex-end",
    },
});
