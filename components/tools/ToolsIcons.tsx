import { colors } from "react-native-elements";
import { G, <PERSON>, <PERSON>, Rect, Svg } from "react-native-svg";

export const IpBlockerIcon = () => {
    return (
        <Svg height="61" viewBox="0 -960 960 960" width="60" fill={colors.white}>
            <Path d="M440-80v-331q-18-11-29-28.5T400-480q0-33 23.5-56.5T480-560q33 0 56.5 23.5T560-480q0 23-11 41t-29 28v331h-80ZM204-190q-57-55-90.5-129.5T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 86-33.5 161T756-190l-56-56q46-44 73-104.5T800-480q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 69 27 129t74 104l-57 57Zm113-113q-35-33-56-78.5T240-480q0-100 70-170t170-70q100 0 170 70t70 170q0 53-21 99t-56 78l-57-57q25-23 39.5-54t14.5-66q0-66-47-113t-113-47q-66 0-113 47t-47 113q0 36 14.5 66.5T374-360l-57 57Z" />
        </Svg>
    );
};

export const BudgetManagerIcon = () => {
    return (
        <Svg width="60" height="61" viewBox="0 0 60 61" fill="none">
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17.3681 8C17.4548 8.48691 17.5 8.98817 17.5 9.5C17.5 10.7477 17.2312 11.9326 16.7483 13H47.5V48H12.5V17.2483C11.4326 17.7312 10.2477 18 9 18C8.48817 18 7.98691 17.9548 7.5 17.8681V48C7.5 50.75 9.75 53 12.5 53H47.5C50.25 53 52.5 50.75 52.5 48V13C52.5 10.25 50.25 8 47.5 8H17.3681Z"
                fill="white"
            />
            <Path
                d="M15.625 19.8H28.125V23.55H15.625V19.8ZM32.5 39.875H45V43.625H32.5V39.875ZM32.5 33.625H45V37.375H32.5V33.625ZM20 45.5H23.75V40.5H28.75V36.75H23.75V31.75H20V36.75H15V40.5H20V45.5ZM35.225 27.875L38.75 24.35L42.275 27.875L44.925 25.225L41.4 21.675L44.925 18.15L42.275 15.5L38.75 19.025L35.225 15.5L32.575 18.15L36.1 21.675L32.575 25.225L35.225 27.875Z"
                fill="white"
            />
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M2.5 9.66667C2.5 5.98667 5.48667 3 9.16667 3C12.8467 3 15.8333 5.98667 15.8333 9.66667C15.8333 13.3467 12.8467 16.3333 9.16667 16.3333C5.48667 16.3333 2.5 13.3467 2.5 9.66667ZM10.9469 12.8746C10.9443 12.8761 10.9417 12.8777 10.9391 12.8792C10.6648 13.0414 10.3417 13.1535 9.99333 13.22V14.3333H8.43333V13.2C7.43333 12.9933 6.59333 12.3533 6.52667 11.22H7.67333C7.73333 11.8333 8.15333 12.3133 9.22 12.3133C10.36 12.3133 10.62 11.74 10.62 11.3867C10.62 10.9067 10.36 10.4467 9.06 10.14C7.61333 9.79333 6.62 9.19333 6.62 8C6.62 6.99333 7.42667 6.34 8.43333 6.12667V5H9.98667V6.14C11.0667 6.40667 11.6133 7.22667 11.6467 8.12H10.5067C10.48 7.46667 10.1333 7.02667 9.21333 7.02667C8.34 7.02667 7.81333 7.42 7.81333 7.98C7.81333 8.46667 8.19333 8.79333 9.37333 9.09333C10.5467 9.4 11.8067 9.90667 11.8133 11.3733C11.813 11.4241 11.8109 11.4737 11.8071 11.5222C11.4995 11.9511 11.2134 12.3961 10.9736 12.8264C10.9647 12.8425 10.9558 12.8585 10.9469 12.8746Z"
                fill="white"
            />
        </Svg>
    );
};

export const CostCalculatorIcon = () => {
    return (
        <Svg width="61" height="61" viewBox="0 0 61 61" fill="none">
            <Path
                d="M8 8C8 6.01088 8.79018 4.10322 10.1967 2.6967C11.6032 1.29018 13.5109 0.5 15.5 0.5L45.5 0.5C47.4891 0.5 49.3968 1.29018 50.8033 2.6967C52.2098 4.10322 53 6.01088 53 8V53C53 54.9891 52.2098 56.8968 50.8033 58.3033C49.3968 59.7098 47.4891 60.5 45.5 60.5H15.5C13.5109 60.5 11.6032 59.7098 10.1967 58.3033C8.79018 56.8968 8 54.9891 8 53V8ZM15.5 9.875V17.375C15.5 17.8723 15.6975 18.3492 16.0492 18.7008C16.4008 19.0525 16.8777 19.25 17.375 19.25H43.625C44.1223 19.25 44.5992 19.0525 44.9508 18.7008C45.3025 18.3492 45.5 17.8723 45.5 17.375V9.875C45.5 9.37772 45.3025 8.90081 44.9508 8.54918C44.5992 8.19754 44.1223 8 43.625 8H17.375C16.8777 8 16.4008 8.19754 16.0492 8.54918C15.6975 8.90081 15.5 9.37772 15.5 9.875ZM15.5 24.875V28.625C15.5 29.1223 15.6975 29.5992 16.0492 29.9508C16.4008 30.3025 16.8777 30.5 17.375 30.5H21.125C21.6223 30.5 22.0992 30.3025 22.4508 29.9508C22.8025 29.5992 23 29.1223 23 28.625V24.875C23 24.3777 22.8025 23.9008 22.4508 23.5492C22.0992 23.1975 21.6223 23 21.125 23H17.375C16.8777 23 16.4008 23.1975 16.0492 23.5492C15.6975 23.9008 15.5 24.3777 15.5 24.875ZM17.375 34.25C16.8777 34.25 16.4008 34.4475 16.0492 34.7992C15.6975 35.1508 15.5 35.6277 15.5 36.125V39.875C15.5 40.3723 15.6975 40.8492 16.0492 41.2008C16.4008 41.5525 16.8777 41.75 17.375 41.75H21.125C21.6223 41.75 22.0992 41.5525 22.4508 41.2008C22.8025 40.8492 23 40.3723 23 39.875V36.125C23 35.6277 22.8025 35.1508 22.4508 34.7992C22.0992 34.4475 21.6223 34.25 21.125 34.25H17.375ZM15.5 47.375V51.125C15.5 51.6223 15.6975 52.0992 16.0492 52.4508C16.4008 52.8025 16.8777 53 17.375 53H21.125C21.6223 53 22.0992 52.8025 22.4508 52.4508C22.8025 52.0992 23 51.6223 23 51.125V47.375C23 46.8777 22.8025 46.4008 22.4508 46.0492C22.0992 45.6975 21.6223 45.5 21.125 45.5H17.375C16.8777 45.5 16.4008 45.6975 16.0492 46.0492C15.6975 46.4008 15.5 46.8777 15.5 47.375ZM28.625 23C28.1277 23 27.6508 23.1975 27.2992 23.5492C26.9475 23.9008 26.75 24.3777 26.75 24.875V28.625C26.75 29.1223 26.9475 29.5992 27.2992 29.9508C27.6508 30.3025 28.1277 30.5 28.625 30.5H32.375C32.8723 30.5 33.3492 30.3025 33.7008 29.9508C34.0525 29.5992 34.25 29.1223 34.25 28.625V24.875C34.25 24.3777 34.0525 23.9008 33.7008 23.5492C33.3492 23.1975 32.8723 23 32.375 23H28.625ZM26.75 36.125V39.875C26.75 40.3723 26.9475 40.8492 27.2992 41.2008C27.6508 41.5525 28.1277 41.75 28.625 41.75H32.375C32.8723 41.75 33.3492 41.5525 33.7008 41.2008C34.0525 40.8492 34.25 40.3723 34.25 39.875V36.125C34.25 35.6277 34.0525 35.1508 33.7008 34.7992C33.3492 34.4475 32.8723 34.25 32.375 34.25H28.625C28.1277 34.25 27.6508 34.4475 27.2992 34.7992C26.9475 35.1508 26.75 35.6277 26.75 36.125ZM28.625 45.5C28.1277 45.5 27.6508 45.6975 27.2992 46.0492C26.9475 46.4008 26.75 46.8777 26.75 47.375V51.125C26.75 51.6223 26.9475 52.0992 27.2992 52.4508C27.6508 52.8025 28.1277 53 28.625 53H32.375C32.8723 53 33.3492 52.8025 33.7008 52.4508C34.0525 52.0992 34.25 51.6223 34.25 51.125V47.375C34.25 46.8777 34.0525 46.4008 33.7008 46.0492C33.3492 45.6975 32.8723 45.5 32.375 45.5H28.625ZM38 24.875V28.625C38 29.1223 38.1975 29.5992 38.5492 29.9508C38.9008 30.3025 39.3777 30.5 39.875 30.5H43.625C44.1223 30.5 44.5992 30.3025 44.9508 29.9508C45.3025 29.5992 45.5 29.1223 45.5 28.625V24.875C45.5 24.3777 45.3025 23.9008 44.9508 23.5492C44.5992 23.1975 44.1223 23 43.625 23H39.875C39.3777 23 38.9008 23.1975 38.5492 23.5492C38.1975 23.9008 38 24.3777 38 24.875ZM39.875 34.25C39.3777 34.25 38.9008 34.4475 38.5492 34.7992C38.1975 35.1508 38 35.6277 38 36.125V51.125C38 51.6223 38.1975 52.0992 38.5492 52.4508C38.9008 52.8025 39.3777 53 39.875 53H43.625C44.1223 53 44.5992 52.8025 44.9508 52.4508C45.3025 52.0992 45.5 51.6223 45.5 51.125V36.125C45.5 35.6277 45.3025 35.1508 44.9508 34.7992C44.5992 34.4475 44.1223 34.25 43.625 34.25H39.875Z"
                fill="white"
            />
        </Svg>
    );
};
export const LocalShipping = () => {
    return (
        <Svg width="54" height="49" viewBox="0 0 54 49" fill="none">
            <Mask id="mask0_11419_12246" maskUnits="userSpaceOnUse" x="0" y="0" width="54" height="49">
                <Rect y="0.5" width="54" height="48" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_11419_12246)">
                <Path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M23.9731 35.6893C24.1043 35.1866 24.1699 34.6557 24.1699 34.0967H36.1699C36.1699 35.7633 36.7533 37.18 37.9199 38.3467C39.0866 39.5133 40.5033 40.0967 42.1699 40.0967C43.8366 40.0967 45.2533 39.5133 46.4199 38.3467C47.5866 37.18 48.1699 35.7633 48.1699 34.0967H50.1699C50.7366 34.0967 51.2116 33.905 51.5949 33.5217C51.9783 33.1383 52.1699 32.6633 52.1699 32.0967V24.7467C52.1699 24.5133 52.1366 24.2967 52.0699 24.0967C52.0033 23.8967 51.9033 23.7133 51.7699 23.5467L47.3699 17.6967C47.0033 17.1967 46.5366 16.805 45.9699 16.5217C45.4033 16.2383 44.8033 16.0967 44.1699 16.0967H40.1699V12.0967C40.1699 10.9967 39.7783 10.055 38.9949 9.27168C38.2116 8.48835 37.2699 8.09668 36.1699 8.09668H12.1699C11.0699 8.09668 10.1283 8.48835 9.34492 9.27168C8.56159 10.055 8.16992 10.9967 8.16992 12.0967V25.1242C9.37272 24.7194 10.6607 24.5 12 24.5C12.0567 24.5 12.1134 24.5004 12.1699 24.5012V12.0967H36.1699V30.0967H22.5699C22.2427 29.7502 21.8877 29.4427 21.5049 29.174C22.9148 31.0006 23.81 33.2448 23.9731 35.6893ZM43.5949 35.5217C43.2116 35.905 42.7366 36.0967 42.1699 36.0967C41.6033 36.0967 41.1283 35.905 40.7449 35.5217C40.3616 35.1383 40.1699 34.6633 40.1699 34.0967C40.1699 33.53 40.3616 33.055 40.7449 32.6717C41.1283 32.2883 41.6033 32.0967 42.1699 32.0967C42.7366 32.0967 43.2116 32.2883 43.5949 32.6717C43.9783 33.055 44.1699 33.53 44.1699 34.0967C44.1699 34.6633 43.9783 35.1383 43.5949 35.5217ZM48.6699 26.0967H40.1699V20.0967H44.1699L48.6699 26.0967Z"
                    fill="#FAFAFA"
                />
                <Mask id="mask1_11419_12246" maskUnits="userSpaceOnUse" x="2" y="26" width="20" height="21">
                    <Rect x="2" y="26.5" width="20" height="20" fill="#D9D9D9" />
                </Mask>
                <G mask="url(#mask1_11419_12246)">
                    <Path
                        d="M16.7916 43.375C15.9166 43.375 15.177 43.0729 14.5728 42.4688C13.9687 41.8646 13.6666 41.125 13.6666 40.25C13.6666 39.375 13.9687 38.6354 14.5728 38.0312C15.177 37.4271 15.9166 37.125 16.7916 37.125C17.6666 37.125 18.4062 37.4271 19.0103 38.0312C19.6145 38.6354 19.9166 39.375 19.9166 40.25C19.9166 41.125 19.6145 41.8646 19.0103 42.4688C18.4062 43.0729 17.6666 43.375 16.7916 43.375ZM16.7916 41.7083C17.1944 41.7083 17.5381 41.566 17.8228 41.2812C18.1076 40.9965 18.2499 40.6528 18.2499 40.25C18.2499 39.8472 18.1076 39.5035 17.8228 39.2188C17.5381 38.934 17.1944 38.7917 16.7916 38.7917C16.3888 38.7917 16.0451 38.934 15.7603 39.2188C15.4756 39.5035 15.3333 39.8472 15.3333 40.25C15.3333 40.6528 15.4756 40.9965 15.7603 41.2812C16.0451 41.566 16.3888 41.7083 16.7916 41.7083ZM11.1666 41.0833H6.16659C5.93047 41.0833 5.73256 41.0035 5.57284 40.8438C5.41311 40.684 5.33325 40.4861 5.33325 40.25C5.33325 40.0139 5.41311 39.816 5.57284 39.6562C5.73256 39.4965 5.93047 39.4167 6.16659 39.4167H11.1666C11.4027 39.4167 11.6006 39.4965 11.7603 39.6562C11.9201 39.816 11.9999 40.0139 11.9999 40.25C11.9999 40.4861 11.9201 40.684 11.7603 40.8438C11.6006 41.0035 11.4027 41.0833 11.1666 41.0833ZM7.20825 35.875C6.33325 35.875 5.59367 35.5729 4.9895 34.9688C4.38534 34.3646 4.08325 33.625 4.08325 32.75C4.08325 31.875 4.38534 31.1354 4.9895 30.5312C5.59367 29.9271 6.33325 29.625 7.20825 29.625C8.08325 29.625 8.82283 29.9271 9.427 30.5312C10.0312 31.1354 10.3333 31.875 10.3333 32.75C10.3333 33.625 10.0312 34.3646 9.427 34.9688C8.82283 35.5729 8.08325 35.875 7.20825 35.875ZM7.20825 34.2083C7.61103 34.2083 7.95478 34.066 8.2395 33.7812C8.52422 33.4965 8.66658 33.1528 8.66658 32.75C8.66658 32.3472 8.52422 32.0035 8.2395 31.7188C7.95478 31.434 7.61103 31.2917 7.20825 31.2917C6.80547 31.2917 6.46172 31.434 6.177 31.7188C5.89228 32.0035 5.74992 32.3472 5.74992 32.75C5.74992 33.1528 5.89228 33.4965 6.177 33.7812C6.46172 34.066 6.80547 34.2083 7.20825 34.2083ZM17.8333 33.5833H12.8333C12.5971 33.5833 12.3992 33.5035 12.2395 33.3438C12.0798 33.184 11.9999 32.9861 11.9999 32.75C11.9999 32.5139 12.0798 32.316 12.2395 32.1562C12.3992 31.9965 12.5971 31.9167 12.8333 31.9167H17.8333C18.0694 31.9167 18.2673 31.9965 18.427 32.1562C18.5867 32.316 18.6666 32.5139 18.6666 32.75C18.6666 32.9861 18.5867 33.184 18.427 33.3438C18.2673 33.5035 18.0694 33.5833 17.8333 33.5833Z"
                        fill="#FAFAFA"
                    />
                </G>
            </G>
        </Svg>
    );
};

export const DelivertyPresets = () => {
    return (
        <Svg height="61" viewBox="0 -960 960 960" width="60" fill={colors.white}>
            <Path d="M240-160q-50 0-85-35t-35-85H80q-17 0-28.5-11.5T40-320v-400q0-33 23.5-56.5T120-800h480q33 0 56.5 23.5T680-720v80h80q19 0 36 8.5t28 23.5l88 117q4 5 6 11t2 13v147q0 17-11.5 28.5T880-280h-40q0 50-35 85t-85 35q-50 0-85-35t-35-85H360q0 50-35 85t-85 35Zm0-80q17 0 28.5-11.5T280-280q0-17-11.5-28.5T240-320q-17 0-28.5 11.5T200-280q0 17 11.5 28.5T240-240ZM120-360h32q17-18 39-29t49-11q27 0 49 11t39 29h272v-360H120v360Zm600 120q17 0 28.5-11.5T760-280q0-17-11.5-28.5T720-320q-17 0-28.5 11.5T680-280q0 17 11.5 28.5T720-240Zm-40-200h170l-90-120h-80v120ZM360-540Z" />
        </Svg>
    );
};
