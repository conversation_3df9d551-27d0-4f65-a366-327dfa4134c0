import React from "react";

import { BudgetManagerIcon, CostCalculatorIcon, LocalShipping, IpBlockerIcon, DelivertyPresets } from "./ToolsIcons";
import { Href } from "expo-router";
// todo: fix the any type
// this is a function that returns a function definition
// this might be a bit complecated, basically i set each param at a
// different level,there might be a better solution,this works for now
const navigate =
    (Page: any, title: string) =>
    // you set Page component in the toolsPages or account pages for example
    // ! its type should be set to a React.ReactElement or smthn like that
    (navigation: any) =>
    // you set navigation at the actual triggering elemnt (button, clickable card...)
    // ! the navigation type, was too complecated it should be changed across the app
    () => {
        navigation.navigate("BackPage", {
            content: () => {
                return <Page navigation={navigation} />;
            },
            title,
        });
    };

type Props = {
    variant: "orange" | "primary" | "blue" | "red";
    icon: React.ReactElement;
    content: {
        label: string;
        detail: string;
    };
    route: string;
    permission?: string;
};

const pages: Props[] = [
    {
        variant: "primary",
        icon: <CostCalculatorIcon />,
        content: {
            label: "Cost Calculator",
            detail: "Calculate Your Cost, Profile and Success rates.",
        },
        route: "/costCalculator",
        permission: "calculator",
    },
    {
        variant: "orange",
        icon: <BudgetManagerIcon />,
        content: {
            label: "Budget Manager",
            detail: "Manage your Budget, Expenses And Revenue.",
        },
        route: "/budgetManager",
        permission: "budget",
    },
    {
        variant: "blue",
        icon: <DelivertyPresets />,
        content: {
            label: "Delivery Presets",
            detail: "Manage your Delivery costs between different Cities.",
        },
        route: "/presets",
        permission: "store",
    },
    {
        variant: "red",
        icon: <IpBlockerIcon />,
        content: {
            label: "IP Blocker",
            detail: "Block IP addresses from accessing your website.",
        },
        route: "/ipBlocker",
        permission: "store",
    },
];

export default pages;
