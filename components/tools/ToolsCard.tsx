import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Href, router, useNavigation, useRouter } from "expo-router";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
type CardProps = {
    variant: keyof typeof colors;
    label: string;
    detail: string;
    order: number;
    children?: React.ReactNode;
    navigation: any;
    route: Href<string>;
};
const ToolsCard = ({ variant, label, detail, order, children, navigation, route }: CardProps) => {
    const router = useRouter();

    return (
        <TouchableOpacity
            onPress={() => {
                router.navigate(route);
            }}
        >
            <View
                style={[
                    styles.card,
                    { backgroundColor: colors[variant][500] },
                    order % 2 == 0 ? { flexDirection: "row" } : { flexDirection: "row-reverse" },
                ]}
            >
                {children}
                <View style={styles.content}>
                    <Text style={styles?.label}>{label}</Text>
                    <Text style={styles.detail}>{detail}</Text>
                </View>
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    card: {
        height: 111,
        padding: 10,
        paddingStart: 15,
        justifyContent: "space-between",
        alignItems: "center",
        gap: 10,
        overflow: "hidden",
        borderRadius: 12,
    },
    content: {
        flex: 1,
    },
    label: {
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: typography.xl.fontSize,
        color: "white",
    },
    detail: {
        fontFamily: typography.fontNormal.fontFamily,
        fontSize: typography.md.fontSize,
        color: "white",
    },
});
export default ToolsCard;
