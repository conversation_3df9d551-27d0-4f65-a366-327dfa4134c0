import React from "react";
import { View, StyleSheet, Dimensions, Text, Platform } from "react-native";
import { RotatingSuspense } from "./RotatingSuspense";
import { ActivityIndicator } from "react-native";
import { opacity } from "react-native-reanimated/lib/typescript/Colors";

interface LoadingProps {
    height?: string | number;
    width?: string | number;
}

const Loading: React.FC<LoadingProps> = ({ height, width }) => {
    return (
        <View style={styles.overlay} pointerEvents="none">
            <View style={styles.container}>
                <View style={styles.centered}>
                    <View style={styles.suspenseContainer}>
                        <RotatingSuspense />
                    </View>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    overlay: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: "100%",
        height: "100%",
        zIndex: 9999,
        elevation: 9999,
    },
    container: {
        flex: 1,
        // backgroundColor: 'white',
        justifyContent: "center",
        alignItems: "center",
    },
    centered: {
        justifyContent: "center",
        alignItems: "center",
        gap: 20,
    },
    suspenseContainer: {
        opacity: 1,
        justifyContent: "center",
        alignItems: "center",
        height: 100,
        width: 100,
    },
});

export default Loading;
