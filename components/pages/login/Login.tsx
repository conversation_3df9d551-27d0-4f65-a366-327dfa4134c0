import React, { useEffect } from "react";
import {
    View,
    StyleSheet,
    Platform,
    Keyboard,
} from "react-native";

import LogoInside from "../../icons/LogoInside";
import Input from "../../inputs/textInputs/Input";
import PasswordInput from "../../inputs/textInputs/PasswordInput";
import TextButton from "../../inputs/buttons/TextButton/TextButton";
import colors from "@styles/colors";

import { useLoginForm } from "../../../hooks/useLoginForm";
import { useAuthStore } from "../../../store/authStore";
import { useStoreStore } from "../../../store/storeStore";

/**
 * Login Component
 *
 * Handles user authentication with proper form validation,
 * error handling, and state management using best practices.
 */
const Login: React.FC = () => {
    const { auth } = useAuthStore();
    const { store } = useStoreStore();

    // Custom hook for form management
    const {
        formState,
        setEmail,
        setPassword,
        handleSubmit,
        isFormValid,
        hasErrors,
        resetForm,
    } = useLoginForm();

    // Reset app state when returning to login
    useEffect(() => {
        if (auth === "unauth") {
            useAuthStore.getState().resetAppState();
            resetForm();
            console.log("App state reset on login screen");
        }
    }, [auth, resetForm]);

    return (
        <View style={styles.container}>
            <View style={styles.formContainer}>
                <LogoInside />

                <Input
                    autoComplete="username"
                    textContentType="username"
                    isValid={!formState.emailError}
                    label="Email"
                    onChange={setEmail}
                    placeholder="Email"
                    inputProps={{
                        value: formState.email,
                        textContentType: "username",
                        keyboardType: "email-address",
                        autoComplete: "username",
                        editable: !formState.isSubmitting,
                    }}
                />

                <PasswordInput
                    label="Password"
                    onChange={setPassword}
                    inputProps={{
                        value: formState.password,
                        editable: !formState.isSubmitting,
                    }}
                    validationSchema={(value) => {
                        if (!value.trim()) return "Password is required";
                        return null;
                    }}
                />

                <TextButton
                    label={formState.isSubmitting ? "Signing In..." : "Login"}
                    onPress={handleSubmit}
                    disabled={!isFormValid || formState.isSubmitting}
                    color={!isFormValid || formState.isSubmitting ? colors.gray[400] : colors.primary[500]}
                />
            </View>
        </View>
    );
};

export default Login;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: colors.white,
        paddingHorizontal: 15,
        ...(Platform.OS === "ios" && {
            paddingTop: 50,
        }),
    },
    formContainer: {
        width: "100%",
        maxWidth: 400,
        gap: 20,
        alignItems: "center",
    },
});
