import {
    CategoryIcon,
    LogoutIcon,
    ManageIcon,
    NotificationIcon,
    ProfileIcon,
    TeamIcon,
    ThemeIcon,
    UpsellIcon,
    ProductListIcons,
    UsersActivity,
} from "@components/icons/AccountListIcons";
import colors from "@styles/colors";
import { Href } from "expo-router";
import { useAuthStore } from "../../../store/authStore";

const size = 16;
const color = colors.primary[800];

type ItemProp = {
    icon: React.JSX.Element;
    label: string;
    route?: Href;
    action?: () => void;
    permission?: string;
};

type AccountPagesProps = {
    sectionLabel?: string;
    items: ItemProp[];
};

const pages: AccountPagesProps[] = [
    {
        sectionLabel: "Products",
        items: [
            {
                icon: <ProductListIcons size={size} color={color} />,
                label: "List",
                route: "/products",
                permission: "product",
            },
            {
                icon: <CategoryIcon size={size} color={color} />,
                label: "Categories",
                route: "/categories",
                permission: "category",
            },
            {
                icon: <UpsellIcon color={color} size={size} />,
                label: "Upsells",
                route: "/upsell",
                permission: "product",
            },
        ],
    },
    {
        sectionLabel: "Team",
        items: [
            {
                icon: <TeamIcon size={size} color={color} />,
                label: "team",
                route: "/team",
                permission: "staff",
            },
            {
                icon: <UsersActivity size={size} color={color} />,
                label: "activities",
                route: "/activities",
                permission: "staff",
            },
        ],
    },
    {
        sectionLabel: "Store",
        items: [
            {
                icon: <ThemeIcon size={size} color={color} />,
                label: "Details",
                route: "/store",
                permission: "store",
            },
            {
                icon: <ManageIcon size={size} color={color} />,
                label: "Settings",
                route: "/settings",
                permission: "store",
            },
        ],
    },
    {
        sectionLabel: "Account",
        items: [
            {
                icon: <ProfileIcon size={size} color={color} />,
                label: "Edit Profile",
                route: "/profile",
            },
            {
                icon: <NotificationIcon size={size} color={color} />,
                label: "Notification Settings",
                route: "/notificationSettings",
            },
            {
                icon: <LogoutIcon size={size} color={color} />,
                label: "Logout",
                action: () => {
                    useAuthStore.getState().logout();
                    // useStoreStore.getState().setUser(undefined);
                    // useStoreStore.getState().setStore({});
                    // useOrderStore.getState().setOrders([]);
                    // useOrderStore.getState().setOrder(undefined);
                },
            },
        ],
    },
];

export default pages;
