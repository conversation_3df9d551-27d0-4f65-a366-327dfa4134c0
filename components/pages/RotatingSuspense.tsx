import { View, StyleSheet } from "react-native";
import React, { useEffect } from "react";
import Animated, { useSharedValue, useAnimatedStyle, withRepeat, withTiming, Easing } from "react-native-reanimated";
import { Svg, Path } from "react-native-svg";
import colors from "@styles/colors";

interface ArcProps {
    x: string | number;
    y: string | number;
    radius: string | number;
    startAngle: string | number;
    endAngle: string | number;
    stroke: string;
    strokeWidth: string | number;
    fill: string;
}

const Arc: React.FC<ArcProps> = ({ x, y, radius, startAngle, endAngle, stroke, strokeWidth, fill }) => {
    // Convert string values to numbers
    const centerX = Number(x);
    const centerY = Number(y);
    const r = Number(radius);
    const start = Number(startAngle);
    const end = Number(endAngle);

    // Convert angles from degrees to radians
    const startRad = (start * Math.PI) / 180;
    const endRad = (end * Math.PI) / 180;

    // Calculate start and end points
    const startX = centerX + r * Math.cos(startRad);
    const startY = centerY + r * Math.sin(startRad);
    const endX = centerX + r * Math.cos(endRad);
    const endY = centerY + r * Math.sin(endRad);

    // Determine if the arc should be drawn clockwise or counterclockwise
    const largeArcFlag = end - start <= 180 ? "0" : "1";

    // Create the SVG path
    const d = ["M", startX, startY, "A", r, r, 0, largeArcFlag, 1, endX, endY].join(" ");

    return <Path d={d} stroke={stroke} strokeWidth={strokeWidth} fill={fill} />;
};

interface RotatingSuspenseProps {
    firstCircleColor?: string;
    secondCircleColor?: string;
    thirdCircleColor?: string;
    firstCircleAnimationDuration?: number;
    secondCircleAnimationDuration?: number;
    thirdCircleAnimationDuration?: number;
}

export const RotatingSuspense: React.FC<RotatingSuspenseProps> = ({
    firstCircleColor = colors.primary["500"],
    secondCircleColor = colors.black,
    thirdCircleColor = colors.secondary["500"],
    firstCircleAnimationDuration = 1500,
    secondCircleAnimationDuration = 1200,
    thirdCircleAnimationDuration = 1000,
    ...props
}) => {
    const rotation1 = useSharedValue(0);
    const rotation2 = useSharedValue(0);
    const rotation3 = useSharedValue(0);

    useEffect(() => {
        rotation1.value = withRepeat(
            withTiming(360, {
                duration: firstCircleAnimationDuration,
                easing: Easing.linear,
            }),
            -1,
            false
        );

        rotation2.value = withRepeat(
            withTiming(-360, {
                duration: secondCircleAnimationDuration,
                easing: Easing.linear,
            }),
            -1,
            false
        );

        rotation3.value = withRepeat(
            withTiming(360, {
                duration: thirdCircleAnimationDuration,
                easing: Easing.linear,
            }),
            -1,
            false
        );
    }, [firstCircleAnimationDuration, secondCircleAnimationDuration, thirdCircleAnimationDuration]);

    const animatedStyle1 = useAnimatedStyle(() => ({
        transform: [{ rotate: `${rotation1.value}deg` }],
    }));

    const animatedStyle2 = useAnimatedStyle(() => ({
        transform: [{ rotate: `${rotation2.value}deg` }],
    }));

    const animatedStyle3 = useAnimatedStyle(() => ({
        transform: [{ rotate: `${rotation3.value}deg` }],
    }));

    return (
        <View style={styles.container} {...props}>
            <Animated.View style={[styles.circleWrapper, animatedStyle1]}>
                <Svg width="100" height="100">
                    <Arc
                        x="50"
                        y="50"
                        radius="45"
                        startAngle="0" // from 0 to 90 deg
                        endAngle="90"
                        stroke={firstCircleColor}
                        strokeWidth="12"
                        fill="none"
                    />
                </Svg>
            </Animated.View>

            <Animated.View style={[styles.circleWrapper, animatedStyle2]}>
                <Svg width="70" height="70">
                    <Arc
                        x="35"
                        y="35"
                        radius="30"
                        startAngle="45" // from 45 to 135 deg - 90+45 so they start in different positions
                        endAngle="135"
                        stroke={secondCircleColor}
                        strokeWidth="2"
                        fill="none"
                    />
                </Svg>
            </Animated.View>

            <Animated.View style={[styles.circleWrapper, animatedStyle3]}>
                <Svg width="40" height="40">
                    <Arc
                        x="20"
                        y="20"
                        radius="15"
                        startAngle="90"
                        endAngle="210"
                        stroke={thirdCircleColor}
                        strokeWidth="6"
                        fill="none"
                    />
                </Svg>
            </Animated.View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: 100,
        height: 100,
        justifyContent: "center",
        alignItems: "center",
    },
    circleWrapper: {
        position: "absolute",
        justifyContent: "center",
        alignItems: "center",
    },
});
