import { AccountIcon, ConvertyNavIcon, HiOutlineQrCode, OrdersIcons, ToolsIcon } from "@components/icons/NavIcons";
import colors from "@styles/colors";
import React from "react";
import { View } from "react-native";

import { BottomTabBarProps } from "@react-navigation/bottom-tabs";

// ref https://reactnavigation.org/docs/bottom-tab-navigator
const BottomNav = ({ state, navigation }: BottomTabBarProps) => {
    const navRoutes = [
        {
            name: "QrScan",
            icon: HiOutlineQrCode,
        },
        {
            name: "Orders",
            icon: OrdersIcons,
        },
        // {
        //     name: 'Orders',
        //     icon: HiOutlineShoppingCart,
        // },
        {
            name: "Home",
            icon: ConvertyNavIcon,
        },
        {
            name: "Tools",
            icon: ToolsIcon,
        },
        // {
        //     name: 'Profile',
        //     icon: HiOutlineUser,
        // },
        {
            name: "Account",
            icon: AccountIcon,
        },
    ] as {
        name: string;
        icon: any;
    }[];

    return (
        <View
            style={{
                height: 70,
                width: "100%",
                borderTopRightRadius: 12,
                borderTopLeftRadius: 12,
                backgroundColor: colors.white,
            }}
        >
            <View
                style={{
                    flexDirection: "row",
                    justifyContent: "space-around",
                    alignItems: "center",
                    height: "100%",
                }}
            >
                {state.routes.map((route: any, index: number) => {
                    // const { options } = descriptors[route.key];
                    const Icon = navRoutes[index].icon;
                    // const label =
                    //     options.tabBarLabel !== undefined
                    //         ? options.tabBarLabel
                    //         : options.title !== undefined
                    //         ? options.title
                    //         : route.name;

                    const isFocused = state.index === index;

                    const onPress = () => {
                        const event = navigation.emit({
                            type: "tabPress",
                            target: route.key,
                            canPreventDefault: true,
                        });

                        if (!isFocused && !event.defaultPrevented) {
                            navigation.navigate(route.name);
                        }
                    };

                    const onLongPress = () => {
                        navigation.emit({
                            type: "tabLongPress",
                            target: route.key,
                        });
                    };

                    return <Icon key={index} active={isFocused} onPress={onPress} onLongPress={onLongPress} />;
                })}
            </View>
        </View>
    );
};

export default BottomNav;
