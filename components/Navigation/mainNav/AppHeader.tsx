import { StatsIcon } from "@components/icons/HeaderIcons";
import IconButton from "@components/inputs/buttons/IconButton";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React, { useState, useEffect } from "react";
import { Text, View } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useRouter } from "expo-router";
import { BottomTabHeaderProps } from "@react-navigation/bottom-tabs";
import { useStoreStore } from "../../../store/storeStore";
import { useAuthStore } from "../../../store/authStore";
import StoreSelector from "./StoreSelector";

type Props = {
    notif?: boolean;
};

const AppHeader = ({ route }: Props & BottomTabHeaderProps) => {
    const router = useRouter();
    const { auth } = useAuthStore();
    const { user, store, setShowStoreSelector, storeLoading } = useStoreStore();
    const [storeImageUrl, setStoreImageUrl] = useState<string | null>(null);

    useEffect(() => {
        const updateStoreImage = async () => {
            // Always prioritize the current store logo from state
            if (store?.logo?.sm) {
                setStoreImageUrl(store.logo.sm);
            } else {
                // Only use AsyncStorage as fallback when store logo is not available
                try {
                    const cachedImageUrl = await AsyncStorage.getItem("store-logo");
                    setStoreImageUrl(cachedImageUrl);
                } catch (error) {
                    console.error("Failed to load cached store image:", error);
                    setStoreImageUrl(null);
                }
            }
        };

        updateStoreImage();
    }, [store]);

    return (
        <View
            style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                gap: 60,
                alignItems: "center",
                alignSelf: "stretch",
                backgroundColor: colors.gray[50],
                paddingHorizontal: 15,
                paddingVertical: 10,
            }}
        >
            {/* {!storeLoading ? ( */}
            <View style={{ flex: 1, alignItems: "flex-start", gap: -5 }}>
                <View
                    style={{
                        display: "flex",
                        rowGap: -10,
                        flexDirection: "row",
                        flexWrap: "wrap",
                        columnGap: 4,
                    }}
                >
                    {route.name === "(home)" && (
                        <Text
                            style={{
                                color: colors.gray[800],
                                fontFamily: typography.altTextSemiBold.fontFamily,
                                fontSize: 20,
                                alignSelf: "baseline",
                            }}
                        >
                            Welcome,
                        </Text>
                    )}
                    <Text
                        style={{
                            color: colors.gray[800],
                            fontFamily: typography.altTextSemiBold.fontFamily,
                            fontSize: 20,
                            textTransform: "capitalize",
                        }}
                    >
                        {route?.name === "(home)"
                            ? user?.firstname === ""
                                ? user?.username
                                : user?.firstname
                            : route?.name === "account"
                            ? user?.firstname === "" && user?.lastname === ""
                                ? user?.username
                                : user?.firstname + " " + user?.lastname
                            : route?.name}
                    </Text>
                </View>

                <Text
                    style={{
                        color: colors.gray[500],
                        fontFamily: typography.fontNormal.fontFamily,
                        fontSize: typography.xs.fontSize,
                    }}
                >
                    {route.name === "(home)" && "Your Progress"}
                    {route.name === "account" && "Your Account"}
                    {route.name === "tools" && "Quick Access"}
                    {route.name === "orders" && "Your Orders"}
                    {route.name === "account" && `  •  `}
                    {route.name === "account" && `${store.name}`}
                </Text>
            </View>
            {/* ) : null } */}
            <View
                style={{
                    display: "flex",
                    flexDirection: "row",
                    gap: 8,
                    alignItems: "center",
                    flexShrink: 0,
                    justifyContent: "center",
                }}
            >
                {(user?.permissions === "all" || user?.permissions?.statistics?.read) && (
                    <IconButton
                        action={() => {
                            router.navigate("/stats/products");
                        }}
                    >
                        <StatsIcon size={20} color={colors.gray[800]} />
                    </IconButton>
                )}

                {user && user?.stores?.length > 1 && auth === "auth" && (
                    <StoreSelector
                        imageUrl={storeImageUrl}
                        storeName={store?.name || ""}
                        onPress={() => setShowStoreSelector(true)}
                        size={38} // You can adjust this size as needed
                    />
                )}
            </View>
        </View>
    );
};

export default AppHeader;
