import React, { useRef, useEffect } from "react";
import { Text, View, TouchableOpacity, Image, StyleSheet, Animated, Easing } from "react-native";
import { Svg, Path, G } from "react-native-svg";
import colors from "@styles/colors";
import { typography } from "@styles/typography";

type StoreSelectorProps = {
    imageUrl: string | null;
    storeName: string;
    onPress: () => void;
    size?: number;
    isChanging?: boolean;
};

const StoreSelector = ({ imageUrl, storeName, onPress, size = 40 }: StoreSelectorProps) => {
    // Calculate proportional values based on size
    const containerSize = size;
    const imageSize = size * 0.9;
    const borderWidth = size * 0.04;
    const arrowSize = size * 0.25;

    return (
        <TouchableOpacity style={[styles.container, { width: containerSize, height: containerSize }]} onPress={onPress}>
            {/* Circular Border with Arrows */}
            <Svg
                width={containerSize}
                height={containerSize}
                viewBox={`0 0 ${containerSize} ${containerSize}`}
                style={styles.borderSvg}
            >
                {/* Background Circle */}
                <Path
                    d={`
            M ${containerSize / 2},${containerSize / 2}
            m -${containerSize / 2 - borderWidth}, 0
            a ${containerSize / 2 - borderWidth},${containerSize / 2 - borderWidth} 0 1,0 ${
                        containerSize - borderWidth * 2
                    },0
            a ${containerSize / 2 - borderWidth},${containerSize / 2 - borderWidth} 0 1,0 -${
                        containerSize - borderWidth * 2
                    },0
          `}
                    fill="none"
                    stroke={colors.gray[200]}
                    strokeWidth={borderWidth}
                />

                {/* Top Arrow */}
                <G transform={`translate(${containerSize / 2}, ${containerSize / 2}) rotate(180)`}>
                    <Path
                        d={`
              M 0,${-containerSize / 2 + borderWidth * 2}
              A ${containerSize / 2 - borderWidth * 2},${containerSize / 2 - borderWidth * 2} 0 0,1 ${
                            containerSize / 2 - borderWidth * 2
                        },0
            `}
                        fill="none"
                        stroke={colors.black} //primary[400]
                        strokeWidth={borderWidth}
                        strokeLinecap="round"
                    />
                    <Path
                        d={`
              M ${containerSize / 2 - borderWidth * 2},0
              L ${containerSize / 2 - borderWidth * 2 + arrowSize / 3},${-arrowSize / 4}
            `}
                        fill="none"
                        stroke={colors.black} //primary[400]
                        strokeWidth={borderWidth}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    />
                </G>

                {/* Bottom Arrow */}
                <G transform={`translate(${containerSize / 2}, ${containerSize / 2}) rotate(0)`}>
                    <Path
                        d={`
              M 0,${-containerSize / 2 + borderWidth * 2}
              A ${containerSize / 2 - borderWidth * 2},${containerSize / 2 - borderWidth * 2} 0 0,1 ${
                            containerSize / 2 - borderWidth * 2
                        },0
            `}
                        fill="none"
                        stroke={colors.black} //primary[400]
                        strokeWidth={borderWidth}
                        strokeLinecap="round"
                    />
                    <Path
                        d={`
              M ${containerSize / 2 - borderWidth * 2},0
              L ${containerSize / 2 - borderWidth * 2 + arrowSize / 3},${-arrowSize / 4}
            `}
                        fill="none"
                        stroke={colors.black} //primary[400]
                        strokeWidth={borderWidth}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    />
                </G>
            </Svg>

            {/* Store logo or placeholder */}
            {imageUrl ? (
                <Image
                    source={{ uri: imageUrl }}
                    style={[
                        styles.image,
                        {
                            width: imageSize / 1.2,
                            height: imageSize / 1.2,
                            borderRadius: imageSize / 1.5,
                        },
                    ]}
                />
            ) : (
                <View
                    style={[
                        styles.placeholderImage,
                        {
                            width: imageSize / 1.2,
                            height: imageSize / 1.2,
                            borderRadius: imageSize / 1.5,
                        },
                    ]}
                >
                    <Text style={[typography.fontMedium, { color: colors.gray[600] }]}>
                        {(storeName || "").charAt(0).toUpperCase()}
                    </Text>
                </View>
            )}
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        justifyContent: "center",
        alignItems: "center",
        position: "relative",
        marginLeft: 8,
    },
    image: {
        position: "absolute",
        zIndex: 1,
        borderWidth: 0.1,
        borderColor: colors.gray[50],
    },
    placeholderImage: {
        position: "absolute",
        zIndex: 1,
        backgroundColor: colors.gray[200],
        justifyContent: "center",
        alignItems: "center",
        borderWidth: 1,
        borderColor: colors.gray[50],
    },
    borderSvg: {
        position: "absolute",
        zIndex: 2,
    },
});

export default StoreSelector;
