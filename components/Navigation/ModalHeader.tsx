import { BackIcon, CloseIcon } from "@components/icons/HeaderIcons";
import IconButton from "@components/inputs/buttons/IconButton";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { ActivityIndicator, StyleSheet, Text, View } from "react-native";
import { NativeStackHeaderProps } from "@react-navigation/native-stack";
import { useRouter } from "expo-router";
import Button from "@components/inputs/buttons/Button";
const ModalHeader = ({
    navigation,
    options,
    route,
    back,
    backAction,
    modalButtonAction,
    modalButtonLabel,
    backIcon,
    loading,
}: NativeStackHeaderProps & {
    modalButtonAction?: () => void;
    modalButtonLabel?: string;
    backIcon?: boolean;
    loading?: boolean;
    backAction?: () => void;
}) => {
    let title = options.headerTitle ? options.headerTitle?.toString() : route.name;
    // title = title.replace("/index", "");
    const router = useRouter();
    return (
        <View
            style={{
                backgroundColor: colors.gray[50],
            }}
        >
            <View style={styles.header}>
                <IconButton
                    action={() => {
                        backAction ? backAction() : router.back();
                    }}
                >
                    {!backIcon ? <CloseIcon color={colors.gray[900]} /> : <BackIcon />}
                </IconButton>

                <View>
                    <Button
                        disabled={loading}
                        variant="primary"
                        type="text"
                        label={modalButtonLabel}
                        action={() => {
                            modalButtonAction && modalButtonAction();
                        }}
                    />
                </View>
            </View>
            <View
                style={{
                    width: "100%",
                    height: "100%",
                    position: "absolute",
                    alignItems: "center",
                    justifyContent: "center",
                    flexDirection: "row",
                    paddingVertical: 10,
                }}
            >
                <Text style={styles.title}>{title}</Text>
            </View>
        </View>
    );
};

export default ModalHeader;

const styles = StyleSheet.create({
    header: {
        flexDirection: "row",
        paddingHorizontal: 20,
        paddingVertical: 10,
        zIndex: 100,
        alignItems: "center",
        justifyContent: "space-between",
        gap: 70,
    },
    title: {
        alignSelf: "center",
        textTransform: "capitalize",
        color: colors.gray[900],
        fontSize: typography.md.fontSize,
        fontFamily: typography.fontSemibold.fontFamily,
        paddingLeft: 10,
    },
});
