import { View, Text, ScrollView, FlatList, TouchableOpacity, Touchable, TouchableWithoutFeedback } from "react-native";
import * as React from "react";
import { useEffect, useState } from "react";
import ModalBase from "./ModalBase";
import { useStoreStore } from "../../../store/storeStore";
import { SmallImage } from "@components/productDetails/components";
import { ImageSourcePropType } from "react-native";
import colors from "@styles/colors";
import { Image } from "expo-image";
import Divider from "@components/dividers/Divider";
import { typography } from "@styles/typography";

const SelectDeliveryCompany = ({
    visible,
    setVisible,
    action,
    addAllCompanies,
}: {
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    action: (value: string) => void;
    addAllCompanies?: boolean;
}) => {
    const { store } = useStoreStore();
    const [deliveryCompanies, setDeliveryCompanies] = useState<
        {
            name: string;
            logo: any;
        }[]
    >([]);

    useEffect(() => {
        const _companies: {
            name: string;
            logo: any;
        }[] = addAllCompanies ? [{ name: "store", logo: "" }] : [];

        store.integrations?.forEach((integration) => {
            if (
                integration.ref !== "facebook" &&
                integration.ref !== "microsoft-clarity" &&
                integration.ref !== "tiktok" &&
                integration.ref !== "google-analytics" &&
                integration.ref !== "dz-wilaya-presets" &&
                integration.integrated === true
            )
                _companies.push({ name: integration.ref, logo: integration.fields?.logo });
        });

        setDeliveryCompanies([..._companies]);
    }, []);

    return (
        <ModalBase visible={visible} setVisible={setVisible}>
            <Text style={[typography.fontMedium, typography.md, { alignSelf: "center" }]}>Select Delivery Company</Text>
            <FlatList
                style={{ maxHeight: 330, width: 300 }}
                data={deliveryCompanies}
                ItemSeparatorComponent={Divider}
                renderItem={({ index, item }) => {
                    return (
                        <TouchableOpacity
                            key={index}
                            style={{
                                paddingHorizontal: 5,
                                flexDirection: "row",
                                alignItems: "center",
                                justifyContent: "space-between",
                                paddingVertical: 10,
                            }}
                            onPress={() => {
                                action(item.name);
                                setVisible(false);
                            }}
                        >
                            <Text
                                style={[
                                    typography.fontNormal,
                                    typography.sm,
                                    { color: colors.gray[700], textTransform: "capitalize" },
                                ]}
                            >
                                {item?.name === "store" ? "all companies" : item?.name}
                            </Text>
                            <DeliveryCompanyLogo
                                invisible={item.logo === ""}
                                source={{ uri: `https://cdn.converty.shop/assets/integrations/${item?.name}.webp` }}
                            />
                        </TouchableOpacity>
                    );
                }}
            />
        </ModalBase>
    );
};

export const DeliveryCompanyLogo = ({ source, invisible }: { source: ImageSourcePropType; invisible?: boolean }) => {
    return (
        <View
            style={{
                opacity: invisible ? 0 : 1,
                height: 30,
                width: 90,
                elevation: 2,
                shadowColor: colors.black,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.2,
                shadowRadius: 2,
                borderWidth: 0.5,
                borderColor: colors.gray[200],
                borderRadius: 5,
                backgroundColor: "white",
                overflow: "hidden",
            }}
        >
            <Image
                style={{
                    height: 30,
                    width: 90,
                    borderRadius: 5,
                }}
                source={source}
            />
        </View>
    );
};

export default SelectDeliveryCompany;
