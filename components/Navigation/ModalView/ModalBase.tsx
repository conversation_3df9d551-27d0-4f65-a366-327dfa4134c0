import { KeyboardAvoidingView, Platform } from "react-native";
import { Dimensions, Modal, View, ViewStyle } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

export default ({
    visible,
    setVisible,
    children,
    onDismiss,
}: {
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    children: React.ReactNode;
    onDismiss?: () => void;
}) => {
    return (
        <GestureHandlerRootView>
            <Modal visible={visible} transparent={true} statusBarTranslucent animationType="fade">
                <KeyboardAvoidingView
                    behavior={Platform.OS === "ios" ? "height" : "padding"}
                    style={[
                        {
                            backgroundColor: "rgba(0, 0, 0, 0.5001)",
                            height: Dimensions.get("screen").height,
                            width: Dimensions.get("screen").width,
                        },
                    ]}
                >
                    <Padding
                        action={() => {
                            setVisible(false);
                            onDismiss?.();
                        }}
                        style={{ flexGrow: 1 }}
                    />
                    <View style={{ flexDirection: "row", justifyContent: "center" }}>
                        <Padding
                            action={() => {
                                setVisible(false);
                                onDismiss?.();
                            }}
                            style={{ flexGrow: 1 }}
                        />

                        <View
                            style={{
                                backgroundColor: "white",
                                alignItems: "stretch",
                                borderRadius: 15,
                                gap: 10,
                                padding: 15,
                            }}
                        >
                            {children}
                        </View>
                        <Padding
                            action={() => {
                                setVisible(false);
                                onDismiss?.();
                            }}
                            style={{ flexGrow: 1 }}
                        />
                    </View>
                    <Padding
                        action={() => {
                            setVisible(false);
                            onDismiss?.();
                        }}
                        style={{ flexGrow: 1 }}
                    />
                </KeyboardAvoidingView>
            </Modal>
        </GestureHandlerRootView>
    );
};

export const Padding = ({ action, style }: { action?: () => void; style?: ViewStyle }) => {
    return <View onTouchStart={action ? action : () => {}} style={[style]} />;
};
