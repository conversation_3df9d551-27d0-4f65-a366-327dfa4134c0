import { Text, TouchableOpacity } from "react-native";
import * as React from "react";
import { useState } from "react";
import { ExpandIconSVG } from "@components/icons/OrderPageIcons";
import colors from "@styles/colors";
import SelectDeliveryCompany from "./SelectDeliveryCompany";
import { typography } from "@styles/typography";
import { Tag } from "@components/inputs/chips/Tag/Tag";
import { ViewStyle } from "react-native";

const SelectDeliveryCompanyInput = ({
    deliveryCompany,
    action,
    disabled,
    addAllCompanies,
    style,
}: {
    deliveryCompany: string;
    action: (deliveryCompany: string) => void;
    disabled?: boolean;
    addAllCompanies?: boolean;
    style?: ViewStyle;
}) => {
    const [visible, setVisible] = useState(false);
    return (
        <>
            <TouchableOpacity
                onPress={() => {
                    !disabled && setVisible(true);
                }}
                style={[
                    {
                        alignSelf: "stretch",
                        borderWidth: 1,
                        borderColor: colors.gray[300],
                        borderRadius: 5,
                        minHeight: 50,
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        padding: 10,
                    },
                    disabled && { backgroundColor: colors.gray[200] },
                    style,
                ]}
            >
                {deliveryCompany ? (
                    deliveryCompany !== "store" ? (
                        <Tag delivery={deliveryCompany} />
                    ) : (
                        <Text
                            style={[
                                typography.fontMedium,
                                typography.sm,
                                { color: colors.gray[400], textTransform: "capitalize" },
                            ]}
                        >
                            All Companies
                        </Text>
                    )
                ) : (
                    <Text
                        numberOfLines={1}
                        style={[
                            typography.fontMedium,
                            typography.sm,
                            { color: colors.gray[400], textTransform: "capitalize" },
                        ]}
                    >
                        Select Delivery
                    </Text>
                )}
                <ExpandIconSVG color={colors.gray[600]} />
            </TouchableOpacity>
            <SelectDeliveryCompany
                addAllCompanies={addAllCompanies}
                setVisible={setVisible}
                visible={visible}
                action={action}
            />
        </>
    );
};

export default SelectDeliveryCompanyInput;
