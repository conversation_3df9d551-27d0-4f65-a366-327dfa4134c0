import colors from "@styles/colors";
import React from "react";
import { ScrollView, StyleSheet } from "react-native";

import { Modal, Pressable } from "react-native";

const ModalView = ({
    activeModal,
    setActiveModal,
    children,
}: {
    activeModal: boolean;
    setActiveModal: React.Dispatch<React.SetStateAction<boolean>>;
    children?: React.ReactNode;
}) => {
    return (
        <Modal transparent={true} visible={activeModal}>
            <Pressable
                onPressOut={() => {
                    setActiveModal(false);
                }}
                style={styles.modelBackgournd}
            >
                {/* 
								//! the empty onPress function insures that on card press the card the modal stays  
							*/}
                <Pressable onPress={() => {}} style={styles.modelCard}>
                    <ScrollView>{children}</ScrollView>
                </Pressable>
            </Pressable>
        </Modal>
    );
};

export default ModalView;

const styles = StyleSheet.create({
    modelBackgournd: {
        position: "absolute",
        height: "100%",
        width: "100%",
        justifyContent: "space-evenly",
        alignItems: "center",
        backgroundColor: "rgba(0,0,0,0.1)",
        zIndex: 50,
    },
    modelCard: {
        width: "80%",
        minHeight: "15%",
        maxHeight: "75%",
        zIndex: 70,
        elevation: 1,
        padding: 20,
        alignItems: "stretch",
        backgroundColor: colors.white,
        borderWidth: 1,
        borderColor: colors.gray[50],
        borderRadius: 10,
    },
});
