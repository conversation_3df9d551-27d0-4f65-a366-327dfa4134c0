import { FlatList, TouchableOpacity, ActivityIndicator } from "react-native";
import { Product } from "../../../types/Product";
import { useEffect, useState } from "react";
import { useProductStore } from "../../../store/productStore";
import { Text } from "react-native";
import { typography } from "@styles/typography";
import Divider from "@components/dividers/Divider";
import { SmallImage } from "@components/productDetails/components";
import colors from "@styles/colors";
import ModalBase from "./ModalBase";
import { View } from "react-native";

export default ({
    visible,
    setVisible,
    action,
    addDefaultStoreId,
    addAllProducts,
}: {
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    action: (product: Product) => void;
    addDefaultStoreId?: boolean;
    addAllProducts?: boolean;
}) => {
    const { products, getProducts, status } = useProductStore();
    const [myProducts, setMyProducts] = useState<Product[]>([]);
    const isLoading = status === "loading";

    useEffect(() => {
        getProducts();
    }, []);

    useEffect(() => {
        setMyProducts(
            addAllProducts ? [{ ...products[0], _id: "store", name: "All Products" }, ...products] : [...products]
        );
    }, [products]);

    return (
        <ModalBase visible={visible} setVisible={setVisible}>
            <Text style={[typography.fontMedium, typography.md, { alignSelf: "center" }]}>Select a Product</Text>

            {isLoading ? (
                <View style={{ height: 330, width: 300, justifyContent: "center", alignItems: "center" }}>
                    <ActivityIndicator size="large" color={colors.primary[400]} />
                </View>
            ) : (
                <FlatList
                    data={myProducts}
                    style={{ height: 330, width: 300 }}
                    ItemSeparatorComponent={Divider}
                    renderItem={({ item: product }) => {
                        return (
                            <TouchableOpacity
                                key={product._id}
                                style={{
                                    flexDirection: "row",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    paddingVertical: 15,
                                    paddingRight: 10,
                                }}
                                onPress={() => {
                                    action(product);
                                    setVisible(false);
                                }}
                            >
                                <Text
                                    numberOfLines={2}
                                    style={[
                                        typography.fontNormal,
                                        typography.sm,
                                        { color: colors.gray[700], maxWidth: "60%" },
                                    ]}
                                >
                                    {product.name}
                                </Text>
                                {product._id === "store" ? (
                                    <View />
                                ) : (
                                    <SmallImage source={{ uri: product?.images[0]?.sm }} />
                                )}
                            </TouchableOpacity>
                        );
                    }}
                />
            )}
        </ModalBase>
    );
};
