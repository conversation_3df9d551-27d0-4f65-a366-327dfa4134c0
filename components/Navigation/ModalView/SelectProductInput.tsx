import { Text, TouchableOpacity, View } from "react-native";
import React, { useState } from "react";
import { ExpandIconSVG } from "@components/icons/OrderPageIcons";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Tag } from "@components/inputs/chips/Tag/Tag";
import AddProductModal from "./AddProductModal";
import { Product } from "../../../types/Product";
import { SmallImage } from "@components/productDetails/components";

const SelectProductInput = ({
    product,
    action,
    disabled,
    addAllProducts,
}: {
    product: Product;
    action: (product: Product) => void;
    disabled?: boolean;
    addAllProducts?: boolean;
}) => {
    const [visible, setVisible] = useState(false);
    return (
        <>
            <TouchableOpacity
                onPress={() => {
                    !disabled && setVisible(true);
                }}
                style={[
                    {
                        alignSelf: "stretch",
                        borderWidth: 1,
                        borderColor: colors.gray[300],
                        borderRadius: 5,
                        minHeight: 50,
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        padding: 7,
                    },
                    disabled && { backgroundColor: colors.gray[200] },
                ]}
            >
                {product ? (
                    product.name !== "store" ? (
                        // <Tag delivery={product} />
                        <View
                            style={{
                                flexDirection: "row",
                                alignItems: "center",
                                paddingRight: 10,
                                gap: 10,
                                flexGrow: 1,
                            }}
                        >
                            {product._id === "store" ? (
                                <View />
                            ) : (
                                <SmallImage source={{ uri: product?.images[0]?.sm }} />
                            )}

                            <View style={{ width: "64%" }}>
                                <Text
                                    numberOfLines={1}
                                    style={[typography.fontMedium, typography.sm, { color: colors.gray[400] }]}
                                >
                                    {product.name}
                                </Text>
                            </View>
                        </View>
                    ) : (
                        <Text
                            style={[
                                typography.fontMedium,
                                typography.sm,
                                { color: colors.gray[400], textTransform: "capitalize" },
                            ]}
                        >
                            All Products goes here
                        </Text>
                    )
                ) : (
                    <Text
                        style={[
                            typography.fontMedium,
                            typography.sm,
                            { color: colors.gray[400], textTransform: "capitalize" },
                        ]}
                    >
                        Select Product
                    </Text>
                )}
                <ExpandIconSVG color={colors.gray[600]} />
            </TouchableOpacity>
            <AddProductModal
                addAllProducts={addAllProducts}
                setVisible={setVisible}
                visible={visible}
                action={action}
            />
        </>
    );
};

export default SelectProductInput;
