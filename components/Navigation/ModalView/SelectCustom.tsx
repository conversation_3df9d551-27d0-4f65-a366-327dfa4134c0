import { FlatList, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from "react-native";
import React, { useState } from "react";
import colors from "@styles/colors";
import ModalBase from "./ModalBase";
import { typography } from "@styles/typography";
import { ExpandIconSVG } from "@components/icons/OrderPageIcons";

type Item = { label: string; value: any };

const SelectCustom = ({
    value,
    options,
    action,
    disabled,
    style,
    title,
    placeholder,
}: {
    value: string;
    title?: string;
    placeholder?: string;
    options: Item[];
    action: (value: string) => void;
    style?: ViewStyle;
    disabled?: boolean;
}) => {
    const [visible, setVisible] = useState(false);

    return (
        <>
            <TouchableOpacity
                onPress={() => {
                    !disabled && setVisible(true);
                }}
                style={[
                    {
                        alignSelf: "stretch",
                        borderWidth: 1,
                        borderColor: colors.gray[300],
                        borderRadius: 5,
                        minHeight: 50,
                        flexDirection: "row-reverse",
                        justifyContent: "space-between",
                        alignItems: "center",
                        padding: 10,
                    },
                    disabled && { backgroundColor: colors.gray[200] },
                    style,
                ]}
            >
                <ExpandIconSVG color={colors.gray[600]} />
                <View style={{ width: "85%", justifyContent: "center" }}>
                    <Text
                        numberOfLines={1}
                        style={[
                            typography.fontMedium,
                            typography.sm,
                            { color: colors.gray[value ? 800 : 400], textTransform: "capitalize" },
                        ]}
                    >
                        {value
                            ? options.find((option) => {
                                  return option.value === value;
                              })?.label
                            : placeholder
                            ? placeholder
                            : "Select Option"}
                    </Text>
                </View>
            </TouchableOpacity>
            <ModalBase setVisible={setVisible} visible={visible}>
                {title && <Text style={[typography.fontMedium, typography.md, { alignSelf: "center" }]}>{title}</Text>}
                <FlatList
                    style={{ maxHeight: 300, minWidth: 300 }}
                    data={options}
                    renderItem={({ item, index }) => {
                        return (
                            <TouchableOpacity
                                key={index}
                                style={{
                                    paddingHorizontal: 5,
                                    flexDirection: "row",
                                    alignItems: "center",
                                    paddingVertical: 10,
                                }}
                                onPress={() => {
                                    action(item.value);
                                    setVisible(false);
                                }}
                            >
                                <Text
                                    style={[
                                        typography.fontNormal,
                                        typography.sm,
                                        { color: colors.gray[700], textTransform: "capitalize" },
                                    ]}
                                >
                                    {item.label}
                                </Text>
                            </TouchableOpacity>
                        );
                    }}
                />
                {/* <View>
                    {options.map((option) => {
                        return (
                            <TouchableOpacity
                                onPress={() => {
                                    action();
                                }}
                            >
                                <Text>{option.label}</Text>
                            </TouchableOpacity>
                        );
                    })}
                </View> */}
            </ModalBase>
        </>
    );
};

export default SelectCustom;

const styles = StyleSheet.create({});
