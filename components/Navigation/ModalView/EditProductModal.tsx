import { CartItem, Order } from "@components/orders/types";
import { useProductStore } from "../../../store/productStore";
import { useEffect, useState } from "react";
import ModalBase from "./ModalBase";
import { Text, TouchableOpacity, View } from "react-native";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import Divider from "@components/dividers/Divider";
import { CloseIcon } from "@components/icons/HeaderIcons";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Combination, Product } from "../../../types/Product";
import { Dropdown } from "react-native-element-dropdown";
import { isColorHex, isImageLink } from "@components/productDetails/utils";
import { styles } from "@components/editOrders/Styles";
import { SmallImage } from "@components/productDetails/components";
import Input from "@components/inputs/textInputs/Input";
import { useStoreStore } from "../../../store/storeStore";

export default ({
    visible,
    setVisible,
    cartItem,
    setUpdatedOrderValues,
    updatedOrderValues,
    index,
}: {
    setUpdatedOrderValues: React.Dispatch<React.SetStateAction<Partial<Order>>>;
    updatedOrderValues: Partial<Order>;
    index: number;
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    cartItem: CartItem;
    action: (product: Product) => void;
}) => {
    const { getProducts } = useProductStore();
    useEffect(() => {
        getProducts();
    }, []);
    const [isFocus, setIsFocus] = useState(false);
    const [value, setValue] = useState<{ type: "image" | "text" | "color"; name: string; value: string }[]>([]);
    const { store } = useStoreStore();

    const [unitPrice, setUnitPrice] = useState<number>();
    const [quantity, setQuantity] = useState<number>();

    const [total, setTotal] = useState<number>();

    const [endEditing, setEndEditing] = useState(true);

    const handleConfirm = () => {
        const newCart = [...updatedOrderValues.cart]; // Create a new array copy
        newCart[index] = {
            ...newCart[index], // Copy existing item
            quantity: quantity,
            selectedVariants: value,
            pricePerUnit: unitPrice,
        };

        setUpdatedOrderValues({ ...updatedOrderValues, cart: newCart }); // Update state immutably
        setVisible(false);
    };
    // the combinations record is a convoluted possibly infintly nested record with the last child being a combination values, this is me trying to reach that child
    const getSelectedCombinationsValue = (combinations: any) => {
        let _: Combination = value.reduce((current, entry) => current?.[entry.value], combinations);
        return _;
    };

    useEffect(() => {
        if (visible) {
            setUnitPrice(cartItem.pricePerUnit);
            setQuantity(cartItem.quantity);

            setValue(
                cartItem.selectedVariants?.length !== 0
                    ? cartItem.selectedVariants
                    : cartItem.product.variants.map((variant, index) => {
                          return { type: variant.type, name: variant?.name, value: variant?.values[0] };
                      })
            );
        }
    }, [visible]);

    useEffect(() => {
        let combination = getSelectedCombinationsValue(cartItem.product?.combinations);
        if (combination && combination.quantity && combination.price) {
            setUnitPrice(combination.price / combination.quantity);
            setQuantity(combination.quantity);
        }
    }, [value]);

    useEffect(() => {
        setTotal(unitPrice * quantity);
    }, [quantity, unitPrice]);

    useEffect(() => {
        if (Number.isNaN(unitPrice)) setUnitPrice(0);
    }, [unitPrice]);
    useEffect(() => {
        if (Number.isNaN(total)) setTotal(0);
    }, [total]);
    useEffect(() => {
        if (Number.isNaN(quantity)) setQuantity(0);
    }, [quantity]);

    useEffect(() => {
        console.log("product variants => ", JSON.stringify(cartItem.product?.variants));
    }, [cartItem.product?.variants]);

    return (
        <ModalBase setVisible={setVisible} visible={visible}>
            <Text style={[typography.fontMedium, typography.md, { alignSelf: "center" }]}>
                {cartItem.product?.name}
            </Text>

            <View style={{ gap: 5, width: 300 }}>
                <Text
                    style={(typography.altTextSemiBold, { color: colors.gray[800], fontSize: typography.md.fontSize })}
                >
                    Select Options:
                </Text>
                <View
                    style={{
                        columnGap: 10,
                        rowGap: 5,
                        flexDirection: "row",
                        flexWrap: "wrap",
                        alignContent: "space-between",
                        width: "100%",
                    }}
                >
                    {cartItem.product?.variants &&
                        cartItem.product?.variants.map((variant, index) => {
                            // console.log({ comb: cartItem.product?.combinations, value: value ? value : "undefined" });

                            // const selectedCombination = cartItem.product.combinations[value[index]?.value];

                            return (
                                <View key={index} style={{ gap: 2.5 }}>
                                    <Text style={[styles?.label, typography.xs]}>{variant?.name}</Text>
                                    <Text
                                        style={[
                                            { fontSize: 20, height: 1, paddingHorizontal: 30 },
                                            isImageLink(value[index]?.value) && { maxWidth: 120 },
                                        ]}
                                    >
                                        {value[index]?.value}
                                    </Text>

                                    <Dropdown
                                        style={styles.dropdown}
                                        placeholderStyle={styles.placeholderStyle}
                                        selectedTextStyle={[
                                            styles.selectedTextStyle,
                                            isImageLink(value[index]?.value) && {
                                                maxWidth: 100,
                                                lineHeight: 50,
                                            },
                                        ]}
                                        inputSearchStyle={styles.inputSearchStyle}
                                        iconStyle={styles.iconStyle}
                                        data={cartItem.product?.variants[index]?.values.map((variant) => {
                                            return { label: variant, value: variant };
                                        })}
                                        maxHeight={300}
                                        labelField="value"
                                        valueField="value"
                                        placeholder={""}
                                        value={value[index]?.value}
                                        onChange={(item) => {
                                            setValue((prev) => {
                                                const _ = [...prev];

                                                _[index] = { ...prev[index], value: item?.value };
                                                return [..._];
                                            });
                                        }}
                                        renderLeftIcon={() => {
                                            if (isImageLink(value[index]?.value))
                                                return <SmallImage source={{ uri: value[index]?.value }} />;
                                            if (isColorHex(value[index]?.value))
                                                return (
                                                    <View
                                                        style={{
                                                            height: 20,
                                                            width: 20,
                                                            backgroundColor: value[index]?.value,
                                                            borderRadius: 10,
                                                            elevation: 2,
                                                            borderColor: `${colors.gray[300]}`,
                                                        }}
                                                    />
                                                );
                                        }}
                                        renderItem={(_) => {
                                            if (isImageLink(_.value))
                                                return (
                                                    <View style={{ padding: 15 }}>
                                                        <SmallImage source={{ uri: _.value }} />
                                                    </View>
                                                );
                                            if (/^#?([a-fA-F0-9]{3}|[a-fA-F0-9]{6})$/.test(_.value))
                                                return (
                                                    <View style={{ padding: 15 }}>
                                                        <View
                                                            style={{
                                                                height: 20,
                                                                width: 20,
                                                                backgroundColor: `${_.value}`,
                                                                borderRadius: 10,
                                                                elevation: 2,
                                                                borderColor: `${colors.gray[400]}`,
                                                            }}
                                                        />
                                                    </View>
                                                );
                                            return (
                                                <View style={{ padding: 15 }}>
                                                    <Text style={[typography.fontNormal]}>{_.value}</Text>
                                                </View>
                                            );
                                        }}
                                    />
                                </View>
                            );
                        })}
                </View>
            </View>
            <Divider />

            <View style={{ flexDirection: "row", gap: 10 }}>
                <Input
                    label="unit Price"
                    containerStyle={{ flex: 1 }}
                    noBottomPadding
                    onChange={(text) => {
                        setEndEditing(() => {
                            return false;
                        });
                        setUnitPrice((prev) => {
                            if (Number.isNaN(prev)) return 0;
                            return parseFloat(text);
                        });
                    }}
                    inputProps={{
                        onEndEditing: () => {
                            setEndEditing(() => {
                                return true;
                            });
                        },
                        value: endEditing ? unitPrice?.toString() : undefined,
                        inputMode: "decimal",
                        selectTextOnFocus: true,
                    }}
                />
                <Input
                    label="Quantity"
                    containerStyle={{ flex: 1 }}
                    noBottomPadding
                    onChange={(text) => {
                        setQuantity((prev) => {
                            if (Number.isNaN(prev)) return 0;

                            return parseInt(text);
                        });
                    }}
                    inputProps={{
                        value: quantity?.toFixed(0),
                        inputMode: "decimal",

                        selectTextOnFocus: true,
                    }}
                />
            </View>
            <View
                style={{ flexDirection: "row", justifyContent: "space-between", paddingHorizontal: 10, paddingTop: 5 }}
            >
                <Text style={[typography.altTextSemiBold, typography.md, { color: colors.gray["700"] }]}>Total</Text>
                <Text style={[typography.altTextSemiBold, typography.md, { color: colors.gray["700"] }]}>
                    {total}
                    {" " + store?.currency?.code}
                </Text>
            </View>
            <View style={{ flexDirection: "row", gap: 10, paddingTop: 10 }}>
                <TextButton label="confirm" style={{ flex: 1 }} onPress={handleConfirm} />
            </View>
            <TouchableOpacity
                onPress={() => {
                    setVisible(false);
                    setValue([]);
                }}
                style={{ position: "absolute", right: 15, top: 15 }}
            >
                <CloseIcon color={colors.red[500]} />
            </TouchableOpacity>
        </ModalBase>
    );
};
