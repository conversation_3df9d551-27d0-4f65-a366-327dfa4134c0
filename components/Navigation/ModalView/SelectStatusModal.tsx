import { Text, FlatList } from "react-native";
import React from "react";
import ModalBase from "./ModalBase";
import { typography } from "@styles/typography";
import FilterChip from "@components/stats/FilterChip";
import { Status } from "../../../types/Order";

const SelectStatusModal = ({
    visible,
    setVisible,
    action,

    pageType,
}: {
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    action: (status: Status) => void;
    pageType: "edit" | "create";
}) => {
    const statuslist =
        pageType === "edit"
            ? ["attempt", "confirmed", "rejected", "exchange", "packed", "delivered", "returned"]
            : ["pending", "confirmed", "exchange"];
    return (
        <ModalBase visible={visible} setVisible={setVisible}>
            <Text style={[typography.fontMedium, typography.md, { alignSelf: "center" }]}>Select Status</Text>
            <FlatList
                data={statuslist}
                style={{ maxHeight: 300, width: 250 }}
                contentContainerStyle={{ gap: 10, paddingHorizontal: 20 }}
                renderItem={({ item, index }) => {
                    return (
                        <FilterChip
                            key={index}
                            type="orders"
                            action={() => {
                                action(item as Status);
                                setVisible(false);
                            }}
                            active={false}
                            title={item}
                        />
                    );
                }}
            />
        </ModalBase>
    );
};

export default SelectStatusModal;
