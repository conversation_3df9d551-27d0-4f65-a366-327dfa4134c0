import React, { useState } from "react";
import SelectStatusModal from "./SelectStatusModal";
import { Status } from "../../../types/Order";
import { ExpandIconSVG } from "@components/icons/OrderPageIcons";
import colors from "@styles/colors";
import { View, ViewStyle, TouchableOpacity } from "react-native";
import { Tag } from "@components/inputs/chips/Tag/Tag";

const SelectStatusInput = ({
    status,
    action,
    pageType,
    disabled,
    style,
}: {
    status: Status;
    action: (status: Status) => void;
    pageType: "edit" | "create";
    disabled?: boolean;
    style?: ViewStyle;
}) => {
    const [visible, setVisible] = useState(false);

    return (
        <View>
            <TouchableOpacity
                disabled={disabled}
                onPress={() => setVisible(true)}
                style={[
                    {
                        alignSelf: "stretch",
                        borderWidth: 1,
                        borderColor: colors.gray[300],
                        borderRadius: 5,
                        minHeight: 50,
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        padding: 10,
                    },
                    disabled && { backgroundColor: colors.gray[200] },
                    style,
                ]}
            >
                <Tag status={status} />
                <ExpandIconSVG color={colors.gray[600]} />
            </TouchableOpacity>
            <SelectStatusModal action={action} pageType={pageType} setVisible={setVisible} visible={visible} />
        </View>
    );
};

export default SelectStatusInput;
