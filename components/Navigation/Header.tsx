import { BackIcon } from "@components/icons/HeaderIcons";
import IconButton from "@components/inputs/buttons/IconButton";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { StyleSheet, Text, View, ViewStyle } from "react-native";
import { NativeStackHeaderProps } from "@react-navigation/native-stack";
import { Href, useRouter } from "expo-router";
import Button from "@components/inputs/buttons/Button";
import { Variant } from "@components/orders/types";
const Header = ({
    navigation,
    options,
    route,
    back,
    modalButtonAction,
    modalButtonLabel,
    modalButtonTextColor,
    style,
    backPath,
    NoCap,
}: NativeStackHeaderProps & {
    modalButtonAction?: () => any;
    modalButtonLabel?: string;
    backIcon?: boolean;
    style?: ViewStyle;
    backPath?: Href;
    NoCap?: boolean;
    modalButtonTextColor?:
        | "primary"
        | "secondary"
        | "blue"
        | "green"
        | "red"
        | "orange"
        | "teal"
        | "cyan"
        | "pink"
        | "gray";
}) => {
    let title = options.headerTitle ? options.headerTitle?.toString() : route.name;

    title = title.replace("/index", "");
    const router = useRouter();
    return (
        <View style={{ width: "100%", backgroundColor: colors.gray[50] }}>
            <View style={[styles.header, style]}>
                <IconButton
                    action={() => {
                        backPath ? router.navigate(backPath) : router.back();
                    }}
                >
                    <BackIcon />
                </IconButton>

                <View style={{ justifyContent: "center" }}>
                    <Button
                        variant={modalButtonTextColor ? modalButtonTextColor : "primary"}
                        type="text"
                        label={modalButtonLabel}
                        action={() => {
                            modalButtonAction && modalButtonAction();
                        }}
                    />
                </View>
            </View>
            <View
                style={{
                    width: "100%",
                    height: "100%",
                    position: "absolute",
                    alignItems: "center",
                    justifyContent: "center",
                    flexDirection: "row",
                    paddingVertical: 10,
                }}
            >
                <Text style={[styles.title, !NoCap && { textTransform: "capitalize" }]}>{title}</Text>
            </View>
        </View>
    );
};

export default Header;

const styles = StyleSheet.create({
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        paddingHorizontal: 20,
        paddingVertical: 10,
        zIndex: 100,
        width: "100%",
        alignItems: "center",
    },
    title: {
        flexGrow: 1,
        alignSelf: "center",
        textAlign: "center",
        color: colors.gray[900],
        fontSize: typography.md.fontSize,
        fontFamily: typography.fontSemibold.fontFamily,
    },
});
