import React, { useEffect } from "react";
import { Modal, View, StyleSheet, Dimensions, Platform, StatusBar } from "react-native";
import colors from "@styles/colors";
import Loading from "@components/pages/Loading";

interface LoaderModalProps {
    visible: boolean;
}

const LoaderModal = ({ visible }: LoaderModalProps) => {
    // Force re-render when visibility changes to ensure proper layout
    useEffect(() => {
        // This is just to trigger a re-render when visibility changes
        if (visible) {
            console.log("LoaderModal shown");
        } else {
            console.log("LoaderModal hidden");
        }
    }, [visible]);

    if (!visible) return null;

    return (
        <Modal
            transparent={true}
            visible={visible}
            statusBarTranslucent
            animationType="none" // Changed to none to avoid animation issues
            hardwareAccelerated={Platform.OS === "android"} // Disable for iOS to prevent conflicts
            presentationStyle={Platform.OS === "ios" ? "overFullScreen" : undefined}
        >
            <View style={styles.fullScreenOverlay} pointerEvents="auto">
                <View style={styles.centeredContent}>
                    <Loading />
                </View>
            </View>
        </Modal>
    );
};

// Get screen dimensions including the status bar height
const { width, height } = Dimensions.get("screen"); // Use 'screen' instead of 'window'
const statusBarHeight = StatusBar.currentHeight || 0;

const styles = StyleSheet.create({
    fullScreenOverlay: {
        position: "absolute",
        width: width,
        height: Platform.OS === "android" ? height + statusBarHeight : height,
        left: 0,
        top: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        zIndex: 1000,
    },
    centeredContent: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
});

export default LoaderModal;
