import { Circle, Defs, G, <PERSON>arGradient, Mask, Path, Rect, Stop, Svg } from "react-native-svg";

type IconProps = {
    size: number;
    color: string;
};
export const DolarIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width={`${size}px`} height={`${size}px`} viewBox={`0 0 20 20`} fill="none">
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M9.99996 3.33366C8.23185 3.33366 6.53616 4.03604 5.28591 5.28628C4.03567 6.53652 3.33329 8.23222 3.33329 10.0003C3.33329 10.8758 3.50573 11.7427 3.84076 12.5515C4.17579 13.3604 4.66686 14.0953 5.28591 14.7144C5.90497 15.3334 6.6399 15.8245 7.44874 16.1595C8.25757 16.4946 9.12448 16.667 9.99996 16.667C10.8754 16.667 11.7423 16.4946 12.5512 16.1595C13.36 15.8245 14.0949 15.3334 14.714 14.7144C15.3331 14.0953 15.8241 13.3604 16.1592 12.5515C16.4942 11.7427 16.6666 10.8758 16.6666 10.0003C16.6666 8.23222 15.9642 6.53652 14.714 5.28628C13.4638 4.03604 11.7681 3.33366 9.99996 3.33366ZM4.1074 4.10777C5.67021 2.54497 7.78982 1.66699 9.99996 1.66699C12.2101 1.66699 14.3297 2.54497 15.8925 4.10777C17.4553 5.67057 18.3333 7.79019 18.3333 10.0003C18.3333 11.0947 18.1177 12.1783 17.699 13.1894C17.2802 14.2004 16.6663 15.1191 15.8925 15.8929C15.1187 16.6667 14.2 17.2805 13.189 17.6993C12.1779 18.1181 11.0943 18.3337 9.99996 18.3337C8.90561 18.3337 7.82198 18.1181 6.81093 17.6993C5.79988 17.2805 4.88122 16.6667 4.1074 15.8929C3.33358 15.1191 2.71975 14.2004 2.30096 13.1894C1.88217 12.1783 1.66663 11.0947 1.66663 10.0003C1.66663 7.79019 2.5446 5.67057 4.1074 4.10777ZM9.99996 5.00033C10.4602 5.00033 10.8333 5.37342 10.8333 5.83366V5.91063C11.6169 6.05792 12.3262 6.41381 12.7952 6.95411C13.0968 7.30169 13.0596 7.82802 12.712 8.12968C12.3644 8.43135 11.8381 8.39413 11.5364 8.04654C11.4009 7.89039 11.1592 7.73172 10.8333 7.62594V9.24362C11.3513 9.34086 11.8304 9.52858 12.23 9.79495C12.8315 10.1959 13.3333 10.8413 13.3333 11.667C13.3333 12.4926 12.8315 13.1381 12.23 13.539C11.8304 13.8054 11.3513 13.9931 10.8333 14.0904V14.167C10.8333 14.6272 10.4602 15.0003 9.99996 15.0003C9.53972 15.0003 9.16663 14.6272 9.16663 14.167V14.09C8.38306 13.9427 7.67369 13.5868 7.20477 13.0465C6.9031 12.699 6.94032 12.1726 7.28791 11.871C7.6355 11.5693 8.16182 11.6065 8.46349 11.9541C8.59901 12.1103 8.84072 12.2689 9.16663 12.3747V10.757C8.64864 10.6598 8.16953 10.4721 7.76993 10.2057C7.16842 9.80474 6.66663 9.15931 6.66663 8.33366C6.66663 7.50801 7.16842 6.86258 7.76993 6.46161C8.16953 6.19524 8.64864 6.00752 9.16663 5.91029V5.83366C9.16663 5.37342 9.53972 5.00033 9.99996 5.00033ZM9.16663 7.62609C8.98273 7.68557 8.82336 7.76242 8.69436 7.84841C8.39109 8.05057 8.33329 8.23847 8.33329 8.33366C8.33329 8.42885 8.39109 8.61675 8.69436 8.81891C8.82336 8.90489 8.98273 8.98175 9.16663 9.04123V7.62609ZM10.8333 10.9594V12.3746C11.0172 12.3151 11.1766 12.2382 11.3056 12.1522C11.6088 11.9501 11.6666 11.7622 11.6666 11.667C11.6666 11.5718 11.6088 11.3839 11.3056 11.1817C11.1766 11.0958 11.0172 11.0189 10.8333 10.9594Z"
                fill={color}
            />
        </Svg>
    );
};

export const ProductListIcons = ({ size, color }: IconProps) => {
    return (
        <Svg width={`${size}px`} height={`${size}px`} viewBox="0 -960 960 960" fill="none">
            <Path
                d="M638-468 468-638q-6-6-8.5-13t-2.5-15q0-8 2.5-15t8.5-13l170-170q6-6 13-8.5t15-2.5q8 0 15 2.5t13 8.5l170 170q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L694-468q-6 6-13 8.5t-15 2.5q-8 0-15-2.5t-13-8.5Zm-518-92v-240q0-17 11.5-28.5T160-840h240q17 0 28.5 11.5T440-800v240q0 17-11.5 28.5T400-520H160q-17 0-28.5-11.5T120-560Zm400 400v-240q0-17 11.5-28.5T560-440h240q17 0 28.5 11.5T840-400v240q0 17-11.5 28.5T800-120H560q-17 0-28.5-11.5T520-160Zm-400 0v-240q0-17 11.5-28.5T160-440h240q17 0 28.5 11.5T440-400v240q0 17-11.5 28.5T400-120H160q-17 0-28.5-11.5T120-160Zm80-440h160v-160H200v160Zm467 48 113-113-113-113-113 113 113 113Zm-67 352h160v-160H600v160Zm-400 0h160v-160H200v160Zm160-400Zm194-65ZM360-360Zm240 0Z"
                fill={color}
            />
        </Svg>
    );
};

export const UsersActivity = ({ size, color }: IconProps) => {
    return (
        <Svg width={`${size}px`} height={`${size}px`} viewBox="0 -960 960 960" fill="none">
            <Path
                d="M80-600v-160q0-33 23.5-56.5T160-840h640q33 0 56.5 23.5T880-760v160h-80v-160H160v160H80Zm80 360q-33 0-56.5-23.5T80-320v-200h80v200h640v-200h80v200q0 33-23.5 56.5T800-240H160ZM40-120v-80h880v80H40Zm440-420ZM80-520v-80h240q11 0 21 6t15 16l47 93 123-215q5-9 14-14.5t20-5.5q11 0 21 5.5t15 16.5l49 98h235v80H620q-11 0-21-5.5T584-542l-26-53-123 215q-5 10-15 15t-21 5q-11 0-20.5-6T364-382l-69-138H80Z"
                fill={color}
            />
        </Svg>
    );
};

export const CartIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width={`${size}px`} height={`${size}px`} viewBox="0 0 20 20" fill="none">
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M1.66675 2.50033C1.66675 2.04009 2.03984 1.66699 2.50008 1.66699H4.16675C4.56398 1.66699 4.906 1.94738 4.9839 2.3369L5.18325 3.33366H17.5001C17.7889 3.33366 18.0571 3.4832 18.209 3.72888C18.3608 3.97456 18.3746 4.28135 18.2454 4.53967L14.9121 11.2063C14.7709 11.4887 14.4824 11.667 14.1667 11.667H6.17859L4.51193 13.3337H14.1667C14.8298 13.3337 15.4657 13.5971 15.9345 14.0659C16.4034 14.5347 16.6667 15.1706 16.6667 15.8337C16.6667 16.4967 16.4034 17.1326 15.9345 17.6014C15.4657 18.0703 14.8298 18.3337 14.1667 18.3337C13.5037 18.3337 12.8678 18.0703 12.399 17.6014C11.9301 17.1326 11.6667 16.4967 11.6667 15.8337C11.6667 15.547 11.716 15.2655 11.8097 15.0003H8.19044C8.2842 15.2655 8.33342 15.547 8.33342 15.8337C8.33342 16.4967 8.07002 17.1326 7.60118 17.6014C7.13234 18.0703 6.49646 18.3337 5.83342 18.3337C5.17037 18.3337 4.53449 18.0703 4.06565 17.6014C3.59681 17.1326 3.33341 16.4967 3.33341 15.8337C3.33341 15.4469 3.42303 15.0694 3.59075 14.7289C2.7743 14.1871 2.53362 12.9549 3.33333 12.1552L4.9288 10.5598L3.68631 4.34729C3.68397 4.3367 3.68183 4.32603 3.6799 4.31529L3.48358 3.33366H2.50008C2.03984 3.33366 1.66675 2.96056 1.66675 2.50033ZM5.51659 5.00033L6.51659 10.0003H13.6517L16.1517 5.00033H5.51659ZM5.83342 15.0003C5.6124 15.0003 5.40044 15.0881 5.24416 15.2444C5.08788 15.4007 5.00008 15.6126 5.00008 15.8337C5.00008 16.0547 5.08788 16.2666 5.24416 16.4229C5.40044 16.5792 5.6124 16.667 5.83342 16.667C6.05443 16.667 6.26639 16.5792 6.42267 16.4229C6.57895 16.2666 6.66675 16.0547 6.66675 15.8337C6.66675 15.6126 6.57895 15.4007 6.42267 15.2444C6.26639 15.0881 6.05443 15.0003 5.83342 15.0003ZM14.1667 15.0003C13.9457 15.0003 13.7338 15.0881 13.5775 15.2444C13.4212 15.4007 13.3334 15.6126 13.3334 15.8337C13.3334 16.0547 13.4212 16.2666 13.5775 16.4229C13.7338 16.5792 13.9457 16.667 14.1667 16.667C14.3878 16.667 14.5997 16.5792 14.756 16.4229C14.9123 16.2666 15.0001 16.0547 15.0001 15.8337C15.0001 15.6126 14.9123 15.4007 14.756 15.2444C14.5997 15.0881 14.3878 15.0003 14.1667 15.0003Z"
                fill={color}
            />
        </Svg>
    );
};

export const StatsIcon = ({ size, color }: IconProps) => {
    return (
        //todo: add the notifaction
        <Svg width={20} height={20} viewBox="0 0 20 20" fill="none">
            <Path
                d="M4.0625 19.375H2.8125C2.56386 19.375 2.3254 19.2762 2.14959 19.1004C1.97377 18.9246 1.875 18.6861 1.875 18.4375V12.8125C1.875 12.5639 1.97377 12.3254 2.14959 12.1496C2.3254 11.9738 2.56386 11.875 2.8125 11.875H4.0625C4.31114 11.875 4.5496 11.9738 4.72541 12.1496C4.90123 12.3254 5 12.5639 5 12.8125V18.4375C5 18.6861 4.90123 18.9246 4.72541 19.1004C4.5496 19.2762 4.31114 19.375 4.0625 19.375ZM12.8125 19.375H11.5625C11.3139 19.375 11.0754 19.2762 10.8996 19.1004C10.7238 18.9246 10.625 18.6861 10.625 18.4375V9.0625C10.625 8.81386 10.7238 8.5754 10.8996 8.39959C11.0754 8.22377 11.3139 8.125 11.5625 8.125H12.8125C13.0611 8.125 13.2996 8.22377 13.4754 8.39959C13.6512 8.5754 13.75 8.81386 13.75 9.0625V18.4375C13.75 18.6861 13.6512 18.9246 13.4754 19.1004C13.2996 19.2762 13.0611 19.375 12.8125 19.375ZM17.1875 19.375H15.9375C15.6889 19.375 15.4504 19.2762 15.2746 19.1004C15.0988 18.9246 15 18.6861 15 18.4375V4.6875C15 4.43886 15.0988 4.2004 15.2746 4.02459C15.4504 3.84877 15.6889 3.75 15.9375 3.75H17.1875C17.4361 3.75 17.6746 3.84877 17.8504 4.02459C18.0262 4.2004 18.125 4.43886 18.125 4.6875V18.4375C18.125 18.6861 18.0262 18.9246 17.8504 19.1004C17.6746 19.2762 17.4361 19.375 17.1875 19.375ZM8.4375 19.375H7.1875C6.93886 19.375 6.7004 19.2762 6.52459 19.1004C6.34877 18.9246 6.25 18.6861 6.25 18.4375V1.5625C6.25 1.31386 6.34877 1.0754 6.52459 0.899587C6.7004 0.723772 6.93886 0.625 7.1875 0.625H8.4375C8.68614 0.625 8.9246 0.723772 9.10041 0.899587C9.27623 1.0754 9.375 1.31386 9.375 1.5625V18.4375C9.375 18.6861 9.27623 18.9246 9.10041 19.1004C8.9246 19.2762 8.68614 19.375 8.4375 19.375Z"
                fill={color}
            />
        </Svg>
    );
};

export const NotifIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width={`${size}px`} height={`${size}px`} viewBox="0 0 20 20" fill="none">
            <Path
                d="M3.33325 15.8332V14.1665H4.99992V8.33317C4.99992 7.18039 5.34714 6.15609 6.04159 5.26025C6.73603 4.36442 7.63881 3.77762 8.74992 3.49984V2.9165C8.74992 2.56928 8.87145 2.27414 9.1145 2.03109C9.35756 1.78803 9.6527 1.6665 9.99992 1.6665C10.3471 1.6665 10.6423 1.78803 10.8853 2.03109C11.1284 2.27414 11.2499 2.56928 11.2499 2.9165V3.49984C12.361 3.77762 13.2638 4.36442 13.9583 5.26025C14.6527 6.15609 14.9999 7.18039 14.9999 8.33317V14.1665H16.6666V15.8332H3.33325ZM9.99992 18.3332C9.54158 18.3332 9.14922 18.17 8.82284 17.8436C8.49645 17.5172 8.33325 17.1248 8.33325 16.6665H11.6666C11.6666 17.1248 11.5034 17.5172 11.177 17.8436C10.8506 18.17 10.4583 18.3332 9.99992 18.3332ZM6.66659 14.1665H13.3333V8.33317C13.3333 7.4165 13.0069 6.63178 12.3541 5.979C11.7013 5.32623 10.9166 4.99984 9.99992 4.99984C9.08325 4.99984 8.29853 5.32623 7.64575 5.979C6.99297 6.63178 6.66659 7.4165 6.66659 8.33317V14.1665Z"
                fill={color}
            />
        </Svg>
    );
};

export const LogoIcon = ({ size, color }: IconProps) => {
    let circleRadius = size / 2;
    return (
        <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`} fill="none">
            <Circle cx={circleRadius} cy={circleRadius} r={circleRadius - 1} fill="#682EB0" />
            <Path
                d="M12.9395 5.79231C12.5948 6.11997 12.1715 6.52411 11.7452 6.93446C11.6744 7.00268 11.5622 7.00682 11.4883 6.94118C11.1007 6.59802 10.6139 6.36442 10.0759 6.28948C9.98179 6.27656 9.91254 6.19542 9.91254 6.10033V4.40467C9.91254 4.31732 9.98386 4.24652 10.0712 4.24807C10.0728 4.24807 10.0748 4.24807 10.0748 4.24807C11.2283 4.30699 12.1452 4.77729 12.938 5.51478C13.0181 5.5892 13.0181 5.71634 12.9385 5.79179L12.9395 5.79231Z"
                fill="url(#paint0_linear_8071_30126)"
            />
            <Path
                d="M12.9421 12.2525L11.7219 11.0571C11.6526 10.9894 11.5431 10.9832 11.4687 11.0447C10.9911 11.4385 10.3756 11.6711 9.70633 11.6592C9.08771 11.6483 8.51921 11.4261 8.06803 11.0617C7.44631 10.5604 7.04888 9.79038 7.05663 8.93712C7.05715 8.83841 7.06387 8.74073 7.07472 8.64512C7.15689 7.93915 7.51246 7.31639 8.03186 6.88486C8.0608 6.86057 8.09026 6.83731 8.12023 6.81457C8.46805 6.54944 8.96315 6.36753 9.41381 6.29621C9.50736 6.28122 9.57764 6.20215 9.57764 6.10757V4.45429C9.57764 4.34214 9.48152 4.25325 9.3704 4.2641C8.7001 4.32922 7.98999 4.53336 7.4308 4.84706C7.35121 4.89151 7.27266 4.93854 7.19565 4.98815C6.99048 5.11891 6.79667 5.26516 6.61476 5.42538C6.07469 5.90084 5.64367 6.49673 5.36304 7.17117C5.11858 7.75827 4.98886 8.4048 5.00075 9.08286C5.04623 11.6369 7.13312 13.7156 9.68773 13.7507C10.9379 13.7678 12.0795 13.3021 12.938 12.5285C13.0191 12.4556 13.0206 12.3279 12.9426 12.252L12.9421 12.2525Z"
                fill="white"
            />
            <Defs>
                <LinearGradient
                    id="paint0_linear_8071_30126"
                    x1="5.00134"
                    y1="9.00164"
                    x2="13.0022"
                    y2="9.00164"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#FFD700" />
                    <Stop offset="1" stopColor="#D5B519" />
                </LinearGradient>
            </Defs>
        </Svg>
    );
};
export const ManageIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <Mask id="mask0_8010_25412" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                <Rect width="16" height="16" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_8010_25412)">
                <Path
                    d="M3.89992 7.99967L3.69992 6.99967C3.56659 6.94412 3.44159 6.88579 3.32492 6.82467C3.20825 6.76356 3.08881 6.68856 2.96659 6.59967L1.99992 6.89967L1.33325 5.76634L2.09992 5.09967C2.0777 4.95523 2.06659 4.81079 2.06659 4.66634C2.06659 4.5219 2.0777 4.37745 2.09992 4.23301L1.33325 3.56634L1.99992 2.43301L2.96659 2.73301C3.08881 2.64412 3.20825 2.56912 3.32492 2.50801C3.44159 2.4469 3.56659 2.38856 3.69992 2.33301L3.89992 1.33301H5.23325L5.43325 2.33301C5.56659 2.38856 5.69159 2.4469 5.80825 2.50801C5.92492 2.56912 6.04436 2.64412 6.16659 2.73301L7.13325 2.43301L7.79992 3.56634L7.03325 4.23301C7.05547 4.37745 7.06659 4.5219 7.06659 4.66634C7.06659 4.81079 7.05547 4.95523 7.03325 5.09967L7.79992 5.76634L7.13325 6.89967L6.16659 6.59967C6.04436 6.68856 5.92492 6.76356 5.80825 6.82467C5.69159 6.88579 5.56659 6.94412 5.43325 6.99967L5.23325 7.99967H3.89992ZM4.56659 5.99967C4.93325 5.99967 5.24714 5.86912 5.50825 5.60801C5.76936 5.3469 5.89992 5.03301 5.89992 4.66634C5.89992 4.29967 5.76936 3.98579 5.50825 3.72467C5.24714 3.46356 4.93325 3.33301 4.56659 3.33301C4.19992 3.33301 3.88603 3.46356 3.62492 3.72467C3.36381 3.98579 3.23325 4.29967 3.23325 4.66634C3.23325 5.03301 3.36381 5.3469 3.62492 5.60801C3.88603 5.86912 4.19992 5.99967 4.56659 5.99967ZM9.86659 15.333L9.56659 13.933C9.3777 13.8663 9.2027 13.7858 9.04159 13.6913C8.88047 13.5969 8.72214 13.4886 8.56659 13.3663L7.23325 13.7997L6.29992 12.1997L7.36659 11.2663C7.34436 11.0663 7.33325 10.8663 7.33325 10.6663C7.33325 10.4663 7.34436 10.2663 7.36659 10.0663L6.29992 9.13301L7.23325 7.53301L8.56659 7.96634C8.72214 7.84412 8.88047 7.73579 9.04159 7.64134C9.2027 7.5469 9.3777 7.46634 9.56659 7.39967L9.86659 5.99967H11.7333L12.0333 7.39967C12.2221 7.46634 12.3971 7.5469 12.5583 7.64134C12.7194 7.73579 12.8777 7.84412 13.0333 7.96634L14.3666 7.53301L15.2999 9.13301L14.2333 10.0663C14.2555 10.2663 14.2666 10.4663 14.2666 10.6663C14.2666 10.8663 14.2555 11.0663 14.2333 11.2663L15.2999 12.1997L14.3666 13.7997L13.0333 13.3663C12.8777 13.4886 12.7194 13.5969 12.5583 13.6913C12.3971 13.7858 12.2221 13.8663 12.0333 13.933L11.7333 15.333H9.86659ZM10.7999 12.6663C11.3555 12.6663 11.8277 12.4719 12.2166 12.083C12.6055 11.6941 12.7999 11.2219 12.7999 10.6663C12.7999 10.1108 12.6055 9.63856 12.2166 9.24967C11.8277 8.86079 11.3555 8.66634 10.7999 8.66634C10.2444 8.66634 9.77214 8.86079 9.38325 9.24967C8.99436 9.63856 8.79992 10.1108 8.79992 10.6663C8.79992 11.2219 8.99436 11.6941 9.38325 12.083C9.77214 12.4719 10.2444 12.6663 10.7999 12.6663Z"
                    fill="#2A1246"
                />
            </G>
        </Svg>
    );
};

export const CustomersIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <Mask id="mask0_8010_25417" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                <Rect width="16" height="16" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_8010_25417)">
                <Path
                    d="M0.666748 13.3337V11.467C0.666748 11.0892 0.76397 10.742 0.958415 10.4253C1.15286 10.1087 1.41119 9.86699 1.73341 9.70033C2.4223 9.35588 3.1223 9.09755 3.83341 8.92532C4.54453 8.7531 5.26675 8.66699 6.00008 8.66699C6.73341 8.66699 7.45564 8.7531 8.16675 8.92532C8.87786 9.09755 9.57786 9.35588 10.2667 9.70033C10.589 9.86699 10.8473 10.1087 11.0417 10.4253C11.2362 10.742 11.3334 11.0892 11.3334 11.467V13.3337H0.666748ZM12.6667 13.3337V11.3337C12.6667 10.8448 12.5306 10.3753 12.2584 9.92532C11.9862 9.47533 11.6001 9.08921 11.1001 8.76699C11.6667 8.83366 12.2001 8.94755 12.7001 9.10866C13.2001 9.26977 13.6667 9.46699 14.1001 9.70033C14.5001 9.92255 14.8056 10.1698 15.0167 10.442C15.2279 10.7142 15.3334 11.0114 15.3334 11.3337V13.3337H12.6667ZM6.00008 8.00033C5.26675 8.00033 4.63897 7.73921 4.11675 7.21699C3.59453 6.69477 3.33341 6.06699 3.33341 5.33366C3.33341 4.60033 3.59453 3.97255 4.11675 3.45033C4.63897 2.9281 5.26675 2.66699 6.00008 2.66699C6.73341 2.66699 7.36119 2.9281 7.88341 3.45033C8.40564 3.97255 8.66675 4.60033 8.66675 5.33366C8.66675 6.06699 8.40564 6.69477 7.88341 7.21699C7.36119 7.73921 6.73341 8.00033 6.00008 8.00033ZM12.6667 5.33366C12.6667 6.06699 12.4056 6.69477 11.8834 7.21699C11.3612 7.73921 10.7334 8.00033 10.0001 8.00033C9.87786 8.00033 9.7223 7.98644 9.53341 7.95866C9.34453 7.93088 9.18897 7.90033 9.06675 7.86699C9.36675 7.51144 9.5973 7.11699 9.75841 6.68366C9.91953 6.25033 10.0001 5.80033 10.0001 5.33366C10.0001 4.86699 9.91953 4.41699 9.75841 3.98366C9.5973 3.55033 9.36675 3.15588 9.06675 2.80033C9.2223 2.74477 9.37786 2.70866 9.53341 2.69199C9.68897 2.67533 9.84453 2.66699 10.0001 2.66699C10.7334 2.66699 11.3612 2.9281 11.8834 3.45033C12.4056 3.97255 12.6667 4.60033 12.6667 5.33366ZM2.00008 12.0003H10.0001V11.467C10.0001 11.3448 9.96953 11.2337 9.90841 11.1337C9.8473 11.0337 9.76675 10.9559 9.66675 10.9003C9.06675 10.6003 8.46119 10.3753 7.85008 10.2253C7.23897 10.0753 6.6223 10.0003 6.00008 10.0003C5.37786 10.0003 4.76119 10.0753 4.15008 10.2253C3.53897 10.3753 2.93341 10.6003 2.33341 10.9003C2.23341 10.9559 2.15286 11.0337 2.09175 11.1337C2.03064 11.2337 2.00008 11.3448 2.00008 11.467V12.0003ZM6.00008 6.66699C6.36675 6.66699 6.68064 6.53644 6.94175 6.27533C7.20286 6.01421 7.33341 5.70033 7.33341 5.33366C7.33341 4.96699 7.20286 4.6531 6.94175 4.39199C6.68064 4.13088 6.36675 4.00033 6.00008 4.00033C5.63341 4.00033 5.31953 4.13088 5.05841 4.39199C4.7973 4.6531 4.66675 4.96699 4.66675 5.33366C4.66675 5.70033 4.7973 6.01421 5.05841 6.27533C5.31953 6.53644 5.63341 6.66699 6.00008 6.66699Z"
                    fill="#2A1246"
                />
            </G>
        </Svg>
    );
};

export const RestoreIcon = ({ color, size }: { color: string; size: number }) => {
    return (
        <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
            <Path
                d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"
                fill={color}
            />
        </Svg>
    );
};

export const DeleteIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <Mask id="mask0_8010_25422" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                <Rect width="16" height="16" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_8010_25422)">
                <Path
                    d="M4.66675 14C4.30008 14 3.98619 13.8694 3.72508 13.6083C3.46397 13.3472 3.33341 13.0333 3.33341 12.6667V4H2.66675V2.66667H6.00008V2H10.0001V2.66667H13.3334V4H12.6667V12.6667C12.6667 13.0333 12.5362 13.3472 12.2751 13.6083C12.014 13.8694 11.7001 14 11.3334 14H4.66675ZM11.3334 4H4.66675V12.6667H11.3334V4ZM6.00008 11.3333H7.33341V5.33333H6.00008V11.3333ZM8.66675 11.3333H10.0001V5.33333H8.66675V11.3333Z"
                    fill="#2A1246"
                />
            </G>
        </Svg>
    );
};

export const TrashIcon = ({ size, color }: IconProps) => {
    return (
        <Svg height={`${size}px`} viewBox="0 -960 960 960" width={`${size}px`} fill={color}>
            <Path d="M280-120q-33 0-56.5-23.5T200-200v-520q-17 0-28.5-11.5T160-760q0-17 11.5-28.5T200-800h160q0-17 11.5-28.5T400-840h160q17 0 28.5 11.5T600-800h160q17 0 28.5 11.5T800-760q0 17-11.5 28.5T760-720v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM400-280q17 0 28.5-11.5T440-320v-280q0-17-11.5-28.5T400-640q-17 0-28.5 11.5T360-600v280q0 17 11.5 28.5T400-280Zm160 0q17 0 28.5-11.5T600-320v-280q0-17-11.5-28.5T560-640q-17 0-28.5 11.5T520-600v280q0 17 11.5 28.5T560-280ZM280-720v520-520Z" />
        </Svg>
    );
};

export const CheckIcon = ({ size, color }: IconProps) => {
    return (
        <Svg height={`${size}px`} viewBox="0 -960 960 960" width={`${size}px`} fill={color}>
            <Path d="m382-354 339-339q12-12 28-12t28 12q12 12 12 28.5T777-636L410-268q-12 12-28 12t-28-12L182-440q-12-12-11.5-28.5T183-497q12-12 28.5-12t28.5 12l142 143Z" />
        </Svg>
    );
};

export const NotificationActiveIcon = ({ size = 16, color }) => {
    return (
        <Svg height={size} viewBox="0 0 20 20" width={size} fill={color}>
            <Path d="M 9 1 L 9 2.1054688 L 8.7070312 2.1699219 L 8.2890625 2.3027344 L 7.8867188 2.46875 L 7.5 2.6699219 L 7.1308594 2.9042969 L 6.7851562 3.1699219 L 6.4628906 3.4628906 L 6.1699219 3.7871094 L 5.9023438 4.1328125 L 5.6699219 4.5 L 5.46875 4.8867188 L 5.3007812 5.2890625 L 5.1679688 5.7050781 L 5.0742188 6.1308594 L 5.0175781 6.5644531 L 5 7 L 4.9804688 7.7597656 L 4.9238281 8.5195312 L 4.8261719 9.2753906 L 4.6914062 10.023438 L 4.5195312 10.765625 L 4.3085938 11.498047 L 4.0625 12.216797 L 3.78125 12.923828 L 3.4628906 13.615234 L 3.109375 14.289062 L 2.7226562 14.943359 L 2.3027344 15.580078 L 1.8515625 16.193359 L 1.7832031 16.314453 L 1.75 16.449219 L 1.7539062 16.585938 L 1.7988281 16.720703 L 1.8769531 16.835938 L 1.9824219 16.923828 L 2.109375 16.980469 L 2.2480469 17 L 7.171875 17 L 7.296875 17.302734 L 7.4589844 17.595703 L 7.6542969 17.869141 L 7.8789062 18.121094 L 8.1308594 18.345703 L 8.4042969 18.541016 L 8.6972656 18.701172 L 9.0097656 18.832031 L 9.3320312 18.925781 L 9.6640625 18.980469 L 10 19 L 10.335938 18.980469 L 10.667969 18.925781 L 10.992188 18.832031 L 11.302734 18.701172 L 11.595703 18.541016 L 11.871094 18.345703 L 12.121094 18.121094 L 12.345703 17.869141 L 12.541016 17.595703 L 12.703125 17.302734 L 12.828125 17 L 17.751953 17 L 17.890625 16.980469 L 18.017578 16.923828 L 18.125 16.835938 L 18.203125 16.720703 L 18.246094 16.585938 L 18.25 16.449219 L 18.216797 16.314453 L 18.146484 16.193359 L 17.697266 15.580078 L 17.277344 14.943359 L 16.890625 14.289062 L 16.539062 13.615234 L 16.21875 12.923828 L 15.9375 12.216797 L 15.691406 11.498047 L 15.480469 10.765625 L 15.306641 10.023438 L 15.173828 9.2753906 L 15.076172 8.5195312 L 15.019531 7.7597656 L 15 7 L 14.980469 6.5644531 L 14.923828 6.1308594 L 14.830078 5.7050781 L 14.699219 5.2890625 L 14.53125 4.8867188 L 14.330078 4.5 L 14.095703 4.1328125 L 13.830078 3.7871094 L 13.535156 3.4628906 L 13.214844 3.1699219 L 12.867188 2.9042969 L 12.498047 2.6699219 L 12.111328 2.46875 L 11.708984 2.3027344 L 11.294922 2.1699219 L 11 2.1054688 L 11 1 L 9 1 z M 10 3 L 10.392578 3.0195312 L 10.78125 3.078125 L 11.162109 3.1738281 L 11.53125 3.3046875 L 11.884766 3.4726562 L 12.222656 3.6738281 L 12.539062 3.9082031 L 12.830078 4.171875 L 13.091797 4.4628906 L 13.326172 4.7773438 L 13.529297 5.1132812 L 13.695312 5.46875 L 13.828125 5.8398438 L 13.923828 6.21875 L 13.980469 6.6074219 L 14 7 L 14.017578 7.734375 L 14.068359 8.46875 L 14.152344 9.1992188 L 14.269531 9.9238281 L 14.419922 10.644531 L 14.605469 11.355469 L 14.822266 12.058594 L 15.070312 12.75 L 15.349609 13.429688 L 15.660156 14.095703 L 16.001953 14.748047 L 16.371094 15.382812 L 16.771484 16 L 3.2285156 16 L 3.6269531 15.382812 L 3.9980469 14.748047 L 4.3398438 14.095703 L 4.6503906 13.429688 L 4.9296875 12.75 L 5.1796875 12.058594 L 5.3945312 11.355469 L 5.578125 10.644531 L 5.7304688 9.9238281 L 5.8496094 9.1992188 L 5.9316406 8.46875 L 5.984375 7.734375 L 6 7 L 6.0195312 6.6074219 L 6.078125 6.21875 L 6.171875 5.8398438 L 6.3046875 5.46875 L 6.4707031 5.1132812 L 6.6738281 4.7773438 L 6.9082031 4.4628906 L 7.171875 4.171875 L 7.4628906 3.9082031 L 7.7773438 3.6738281 L 8.1132812 3.4726562 L 8.46875 3.3046875 L 8.8398438 3.1738281 L 9.21875 3.078125 L 9.6074219 3.0195312 L 10 3 z M 8.2714844 17 L 11.728516 17 L 11.708984 17.039062 L 11.552734 17.261719 L 11.365234 17.462891 L 11.152344 17.634766 L 10.919922 17.775391 L 10.669922 17.884766 L 10.408203 17.958984 L 10.136719 17.996094 L 9.8632812 17.996094 L 9.59375 17.958984 L 9.3320312 17.884766 L 9.0800781 17.775391 L 8.8476562 17.634766 L 8.6367188 17.462891 L 8.4492188 17.261719 L 8.2910156 17.039062 L 8.2714844 17 z" />
        </Svg>
    );
};

export const NotificationDisabledIcon = ({ size = 16, color }) => {
    return (
        <Svg height={size} viewBox="0 0 20 20" width={size} fill={color}>
            <Path d="M 18.646484 0.64648438 L 0.64648438 18.646484 L 1.3535156 19.353516 L 19.353516 1.3535156 L 18.646484 0.64648438 z M 9 1 L 9 2.1054688 L 8.7070312 2.1699219 L 8.2890625 2.3027344 L 7.8867188 2.46875 L 7.5 2.6699219 L 7.1308594 2.9042969 L 6.7851562 3.1699219 L 6.4628906 3.4628906 L 6.1699219 3.7871094 L 5.9023438 4.1328125 L 5.6699219 4.5 L 5.46875 4.8867188 L 5.3007812 5.2890625 L 5.1679688 5.7050781 L 5.0742188 6.1308594 L 5.0175781 6.5644531 L 5 7 L 4.9804688 7.7597656 L 4.9238281 8.5195312 L 4.8261719 9.2753906 L 4.6914062 10.023438 L 4.5195312 10.765625 L 4.3085938 11.498047 L 4.0625 12.216797 L 3.78125 12.923828 L 3.4628906 13.615234 L 3.109375 14.289062 L 2.7226562 14.943359 L 2.3105469 15.568359 L 4.7910156 13.087891 L 4.9296875 12.75 L 5.1796875 12.058594 L 5.3945312 11.355469 L 5.578125 10.644531 L 5.7304688 9.9238281 L 5.8496094 9.1992188 L 5.9316406 8.46875 L 5.984375 7.734375 L 6 7 L 6.0195312 6.6074219 L 6.078125 6.21875 L 6.171875 5.8398438 L 6.3046875 5.46875 L 6.4707031 5.1132812 L 6.6738281 4.7773438 L 6.9082031 4.4628906 L 7.171875 4.171875 L 7.4628906 3.9082031 L 7.7773438 3.6738281 L 8.1132812 3.4726562 L 8.46875 3.3046875 L 8.8398438 3.1738281 L 9.21875 3.078125 L 9.6074219 3.0195312 L 10 3 L 10.392578 3.0195312 L 10.78125 3.078125 L 11.162109 3.1738281 L 11.53125 3.3046875 L 11.884766 3.4726562 L 12.222656 3.6738281 L 12.539062 3.9082031 L 12.830078 4.171875 L 13.091797 4.4628906 L 13.230469 4.6484375 L 13.943359 3.9355469 L 13.830078 3.7871094 L 13.535156 3.4628906 L 13.214844 3.1699219 L 12.867188 2.9042969 L 12.498047 2.6699219 L 12.111328 2.46875 L 11.708984 2.3027344 L 11.294922 2.1699219 L 11 2.1054688 L 11 1 L 9 1 z M 15.003906 7.1171875 L 14.041016 8.0800781 L 14.068359 8.46875 L 14.152344 9.1992188 L 14.269531 9.9238281 L 14.419922 10.644531 L 14.605469 11.355469 L 14.822266 12.058594 L 15.070312 12.75 L 15.349609 13.429688 L 15.660156 14.095703 L 16.001953 14.748047 L 16.371094 15.382812 L 16.771484 16 L 6.1210938 16 L 5.1210938 17 L 7.171875 17 L 7.296875 17.302734 L 7.4589844 17.595703 L 7.6542969 17.869141 L 7.8789062 18.121094 L 8.1308594 18.345703 L 8.4042969 18.541016 L 8.6972656 18.701172 L 9.0097656 18.832031 L 9.3320312 18.925781 L 9.6640625 18.980469 L 10 19 L 10.335938 18.980469 L 10.667969 18.925781 L 10.992188 18.832031 L 11.302734 18.701172 L 11.595703 18.541016 L 11.871094 18.345703 L 12.121094 18.121094 L 12.345703 17.869141 L 12.541016 17.595703 L 12.703125 17.302734 L 12.828125 17 L 17.751953 17 L 17.890625 16.980469 L 18.017578 16.923828 L 18.125 16.835938 L 18.203125 16.720703 L 18.246094 16.585938 L 18.25 16.449219 L 18.216797 16.314453 L 18.146484 16.193359 L 17.697266 15.580078 L 17.277344 14.943359 L 16.890625 14.289062 L 16.539062 13.615234 L 16.21875 12.923828 L 15.9375 12.216797 L 15.691406 11.498047 L 15.480469 10.765625 L 15.306641 10.023438 L 15.173828 9.2753906 L 15.076172 8.5195312 L 15.019531 7.7597656 L 15.003906 7.1171875 z M 8.2714844 17 L 11.728516 17 L 11.708984 17.039062 L 11.552734 17.261719 L 11.365234 17.462891 L 11.152344 17.634766 L 10.919922 17.775391 L 10.669922 17.884766 L 10.408203 17.958984 L 10.136719 17.996094 L 9.8632812 17.996094 L 9.59375 17.958984 L 9.3320312 17.884766 L 9.0800781 17.775391 L 8.8476562 17.634766 L 8.6367188 17.462891 L 8.4492188 17.261719 L 8.2910156 17.039062 L 8.2714844 17 z" />
        </Svg>
    );
};

export const CategoryIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <Mask id="mask0_8106_30192" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                <Rect width="16" height="16" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_8106_30192)">
                <Path
                    d="M9.49992 14.2668C9.24436 14.5224 8.9277 14.6502 8.54992 14.6502C8.17214 14.6502 7.85547 14.5224 7.59992 14.2668L1.73325 8.40016C1.61103 8.27794 1.51381 8.1335 1.44159 7.96683C1.36936 7.80016 1.33325 7.62239 1.33325 7.4335V2.66683C1.33325 2.30016 1.46381 1.98627 1.72492 1.72516C1.98603 1.46405 2.29992 1.3335 2.66659 1.3335H7.43325C7.62214 1.3335 7.79992 1.36961 7.96659 1.44183C8.13325 1.51405 8.2777 1.61127 8.39992 1.7335L14.2666 7.61683C14.5221 7.87239 14.6499 8.18627 14.6499 8.5585C14.6499 8.93072 14.5221 9.24461 14.2666 9.50016L9.49992 14.2668ZM8.54992 13.3335L13.3166 8.56683L7.43325 2.66683H2.66659V7.4335L8.54992 13.3335ZM4.33325 5.3335C4.61103 5.3335 4.84714 5.23627 5.04159 5.04183C5.23603 4.84739 5.33325 4.61127 5.33325 4.3335C5.33325 4.05572 5.23603 3.81961 5.04159 3.62516C4.84714 3.43072 4.61103 3.3335 4.33325 3.3335C4.05547 3.3335 3.81936 3.43072 3.62492 3.62516C3.43047 3.81961 3.33325 4.05572 3.33325 4.3335C3.33325 4.61127 3.43047 4.84739 3.62492 5.04183C3.81936 5.23627 4.05547 5.3335 4.33325 5.3335Z"
                    fill="#2A1246"
                />
            </G>
        </Svg>
    );
};
export const SendIcon = ({ size, color }: IconProps) => {
    return (
        <Svg height={`${size}px`} viewBox="0 -960 960 960" width={`${size}px`} fill={color}>
            <Path d="M792-443 176-183q-20 8-38-3.5T120-220v-520q0-22 18-33.5t38-3.5l616 260q25 11 25 37t-25 37ZM200-280l474-200-474-200v140l240 60-240 60v140Zm0 0v-400 400Z" />
        </Svg>
    );
};
export const ThemeIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <Mask id="mask0_8106_30201" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                <Rect width="16" height="16" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_8106_30201)">
                <Path
                    d="M14.0166 7.36667V12.6667C14.0166 13.0333 13.886 13.3472 13.6249 13.6083C13.3638 13.8694 13.0499 14 12.6832 14H3.3499C2.98323 14 2.66934 13.8694 2.40823 13.6083C2.14712 13.3472 2.01656 13.0333 2.01656 12.6667V7.36667C1.76101 7.13333 1.56378 6.83333 1.4249 6.46667C1.28601 6.1 1.28323 5.7 1.41656 5.26667L2.11656 3C2.20545 2.71111 2.36378 2.47222 2.59156 2.28333C2.81934 2.09444 3.08323 2 3.38323 2H12.6499C12.9499 2 13.211 2.09167 13.4332 2.275C13.6555 2.45833 13.8166 2.7 13.9166 3L14.6166 5.26667C14.7499 5.7 14.7471 6.09444 14.6082 6.45C14.4693 6.80556 14.2721 7.11111 14.0166 7.36667ZM9.48323 6.66667C9.78323 6.66667 10.011 6.56389 10.1666 6.35833C10.3221 6.15278 10.3832 5.92222 10.3499 5.66667L9.98323 3.33333H8.68323V5.8C8.68323 6.03333 8.76101 6.23611 8.91656 6.40833C9.07212 6.58056 9.26101 6.66667 9.48323 6.66667ZM6.48323 6.66667C6.73879 6.66667 6.94712 6.58056 7.10823 6.40833C7.26934 6.23611 7.3499 6.03333 7.3499 5.8V3.33333H6.0499L5.68323 5.66667C5.63879 5.93333 5.69712 6.16667 5.85823 6.36667C6.01934 6.56667 6.22767 6.66667 6.48323 6.66667ZM3.51656 6.66667C3.71656 6.66667 3.89156 6.59444 4.04156 6.45C4.19156 6.30556 4.28323 6.12222 4.31656 5.9L4.68323 3.33333H3.38323L2.71656 5.56667C2.6499 5.78889 2.68601 6.02778 2.8249 6.28333C2.96378 6.53889 3.19434 6.66667 3.51656 6.66667ZM12.5166 6.66667C12.8388 6.66667 13.0721 6.53889 13.2166 6.28333C13.361 6.02778 13.3943 5.78889 13.3166 5.56667L12.6166 3.33333H11.3499L11.7166 5.9C11.7499 6.12222 11.8416 6.30556 11.9916 6.45C12.1416 6.59444 12.3166 6.66667 12.5166 6.66667ZM3.3499 12.6667H12.6832V7.96667C12.6277 7.98889 12.5916 8 12.5749 8H12.5166C12.2166 8 11.9527 7.95 11.7249 7.85C11.4971 7.75 11.2721 7.58889 11.0499 7.36667C10.8499 7.56667 10.6221 7.72222 10.3666 7.83333C10.111 7.94444 9.83879 8 9.5499 8C9.2499 8 8.96934 7.94444 8.70823 7.83333C8.44712 7.72222 8.21656 7.56667 8.01656 7.36667C7.82767 7.56667 7.60823 7.72222 7.35823 7.83333C7.10823 7.94444 6.83879 8 6.5499 8C6.22767 8 5.93601 7.94444 5.6749 7.83333C5.41379 7.72222 5.18323 7.56667 4.98323 7.36667C4.7499 7.6 4.51934 7.76389 4.29156 7.85833C4.06379 7.95278 3.80545 8 3.51656 8H3.44156C3.41378 8 3.38323 7.98889 3.3499 7.96667V12.6667Z"
                    fill="#2A1246"
                />
            </G>
        </Svg>
    );
};

export const SettingIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <Mask id="mask0_8106_30206" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                <Rect width="16" height="16" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_8106_30206)">
                <Path
                    d="M6.16672 14.6668L5.90005 12.5335C5.7556 12.4779 5.61949 12.4113 5.49172 12.3335C5.36394 12.2557 5.23894 12.1724 5.11672 12.0835L3.13338 12.9168L1.30005 9.75016L3.01672 8.45016C3.0056 8.37239 3.00005 8.29739 3.00005 8.22516V7.77516C3.00005 7.70294 3.0056 7.62794 3.01672 7.55016L1.30005 6.25016L3.13338 3.0835L5.11672 3.91683C5.23894 3.82794 5.36672 3.74461 5.50005 3.66683C5.63338 3.58905 5.76672 3.52239 5.90005 3.46683L6.16672 1.3335H9.83338L10.1 3.46683C10.2445 3.52239 10.3806 3.58905 10.5084 3.66683C10.6362 3.74461 10.7612 3.82794 10.8834 3.91683L12.8667 3.0835L14.7 6.25016L12.9834 7.55016C12.9945 7.62794 13 7.70294 13 7.77516V8.22516C13 8.29739 12.9889 8.37239 12.9667 8.45016L14.6834 9.75016L12.85 12.9168L10.8834 12.0835C10.7612 12.1724 10.6334 12.2557 10.5 12.3335C10.3667 12.4113 10.2334 12.4779 10.1 12.5335L9.83338 14.6668H6.16672ZM7.33338 13.3335H8.65005L8.88338 11.5668C9.22783 11.4779 9.54727 11.3474 9.84171 11.1752C10.1362 11.0029 10.4056 10.7946 10.65 10.5502L12.3 11.2335L12.95 10.1002L11.5167 9.01683C11.5723 8.86127 11.6112 8.69739 11.6334 8.52516C11.6556 8.35294 11.6667 8.17794 11.6667 8.00016C11.6667 7.82239 11.6556 7.64739 11.6334 7.47516C11.6112 7.30294 11.5723 7.13905 11.5167 6.9835L12.95 5.90016L12.3 4.76683L10.65 5.46683C10.4056 5.21127 10.1362 4.99739 9.84171 4.82516C9.54727 4.65294 9.22783 4.52239 8.88338 4.4335L8.66672 2.66683H7.35005L7.11672 4.4335C6.77227 4.52239 6.45283 4.65294 6.15838 4.82516C5.86394 4.99739 5.59449 5.20572 5.35005 5.45016L3.70005 4.76683L3.05005 5.90016L4.48338 6.96683C4.42783 7.1335 4.38894 7.30016 4.36672 7.46683C4.34449 7.6335 4.33338 7.81127 4.33338 8.00016C4.33338 8.17794 4.34449 8.35016 4.36672 8.51683C4.38894 8.6835 4.42783 8.85016 4.48338 9.01683L3.05005 10.1002L3.70005 11.2335L5.35005 10.5335C5.59449 10.7891 5.86394 11.0029 6.15838 11.1752C6.45283 11.3474 6.77227 11.4779 7.11672 11.5668L7.33338 13.3335ZM8.03338 10.3335C8.67783 10.3335 9.22783 10.1057 9.68338 9.65016C10.1389 9.19461 10.3667 8.64461 10.3667 8.00016C10.3667 7.35572 10.1389 6.80572 9.68338 6.35016C9.22783 5.89461 8.67783 5.66683 8.03338 5.66683C7.37783 5.66683 6.82505 5.89461 6.37505 6.35016C5.92505 6.80572 5.70005 7.35572 5.70005 8.00016C5.70005 8.64461 5.92505 9.19461 6.37505 9.65016C6.82505 10.1057 7.37783 10.3335 8.03338 10.3335Z"
                    fill="#2A1246"
                />
            </G>
        </Svg>
    );
};
export const TeamIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <Mask id="mask0_8106_30212" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                <Rect width="16" height="16" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_8106_30212)">
                <Path
                    d="M0 12V10.95C0 10.4722 0.244444 10.0833 0.733333 9.78333C1.22222 9.48333 1.86667 9.33333 2.66667 9.33333C2.81111 9.33333 2.95 9.33611 3.08333 9.34167C3.21667 9.34722 3.34444 9.36111 3.46667 9.38333C3.31111 9.61667 3.19444 9.86111 3.11667 10.1167C3.03889 10.3722 3 10.6389 3 10.9167V12H0ZM4 12V10.9167C4 10.5611 4.09722 10.2361 4.29167 9.94167C4.48611 9.64722 4.76111 9.38889 5.11667 9.16667C5.47222 8.94444 5.89722 8.77778 6.39167 8.66667C6.88611 8.55556 7.42222 8.5 8 8.5C8.58889 8.5 9.13056 8.55556 9.625 8.66667C10.1194 8.77778 10.5444 8.94444 10.9 9.16667C11.2556 9.38889 11.5278 9.64722 11.7167 9.94167C11.9056 10.2361 12 10.5611 12 10.9167V12H4ZM13 12V10.9167C13 10.6278 12.9639 10.3556 12.8917 10.1C12.8194 9.84445 12.7111 9.60556 12.5667 9.38333C12.6889 9.36111 12.8139 9.34722 12.9417 9.34167C13.0694 9.33611 13.2 9.33333 13.3333 9.33333C14.1333 9.33333 14.7778 9.48056 15.2667 9.775C15.7556 10.0694 16 10.4611 16 10.95V12H13ZM5.41667 10.6667H10.6C10.4889 10.4444 10.1806 10.25 9.675 10.0833C9.16944 9.91667 8.61111 9.83333 8 9.83333C7.38889 9.83333 6.83056 9.91667 6.325 10.0833C5.81944 10.25 5.51667 10.4444 5.41667 10.6667ZM2.66667 8.66667C2.3 8.66667 1.98611 8.53611 1.725 8.275C1.46389 8.01389 1.33333 7.7 1.33333 7.33333C1.33333 6.95556 1.46389 6.63889 1.725 6.38333C1.98611 6.12778 2.3 6 2.66667 6C3.04444 6 3.36111 6.12778 3.61667 6.38333C3.87222 6.63889 4 6.95556 4 7.33333C4 7.7 3.87222 8.01389 3.61667 8.275C3.36111 8.53611 3.04444 8.66667 2.66667 8.66667ZM13.3333 8.66667C12.9667 8.66667 12.6528 8.53611 12.3917 8.275C12.1306 8.01389 12 7.7 12 7.33333C12 6.95556 12.1306 6.63889 12.3917 6.38333C12.6528 6.12778 12.9667 6 13.3333 6C13.7111 6 14.0278 6.12778 14.2833 6.38333C14.5389 6.63889 14.6667 6.95556 14.6667 7.33333C14.6667 7.7 14.5389 8.01389 14.2833 8.275C14.0278 8.53611 13.7111 8.66667 13.3333 8.66667ZM8 8C7.44444 8 6.97222 7.80556 6.58333 7.41667C6.19444 7.02778 6 6.55556 6 6C6 5.43333 6.19444 4.95833 6.58333 4.575C6.97222 4.19167 7.44444 4 8 4C8.56667 4 9.04167 4.19167 9.425 4.575C9.80833 4.95833 10 5.43333 10 6C10 6.55556 9.80833 7.02778 9.425 7.41667C9.04167 7.80556 8.56667 8 8 8ZM8 6.66667C8.18889 6.66667 8.34722 6.60278 8.475 6.475C8.60278 6.34722 8.66667 6.18889 8.66667 6C8.66667 5.81111 8.60278 5.65278 8.475 5.525C8.34722 5.39722 8.18889 5.33333 8 5.33333C7.81111 5.33333 7.65278 5.39722 7.525 5.525C7.39722 5.65278 7.33333 5.81111 7.33333 6C7.33333 6.18889 7.39722 6.34722 7.525 6.475C7.65278 6.60278 7.81111 6.66667 8 6.66667Z"
                    fill="#2A1246"
                />
            </G>
        </Svg>
    );
};
export const ProfileIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <Mask id="mask0_8106_30221" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                <Rect width="16" height="16" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_8106_30221)">
                <Path
                    d="M3.89992 11.4002C4.46659 10.9668 5.09992 10.6252 5.79992 10.3752C6.49992 10.1252 7.23325 10.0002 7.99992 10.0002C8.76659 10.0002 9.49992 10.1252 10.1999 10.3752C10.8999 10.6252 11.5333 10.9668 12.0999 11.4002C12.4888 10.9446 12.7916 10.4279 13.0083 9.85016C13.2249 9.27239 13.3333 8.65572 13.3333 8.00016C13.3333 6.52239 12.8138 5.26405 11.7749 4.22516C10.736 3.18627 9.4777 2.66683 7.99992 2.66683C6.52214 2.66683 5.26381 3.18627 4.22492 4.22516C3.18603 5.26405 2.66659 6.52239 2.66659 8.00016C2.66659 8.65572 2.77492 9.27239 2.99159 9.85016C3.20825 10.4279 3.51103 10.9446 3.89992 11.4002ZM7.99992 8.66683C7.34436 8.66683 6.79159 8.44183 6.34159 7.99183C5.89159 7.54183 5.66659 6.98905 5.66659 6.3335C5.66659 5.67794 5.89159 5.12516 6.34159 4.67516C6.79159 4.22516 7.34436 4.00016 7.99992 4.00016C8.65547 4.00016 9.20825 4.22516 9.65825 4.67516C10.1083 5.12516 10.3333 5.67794 10.3333 6.3335C10.3333 6.98905 10.1083 7.54183 9.65825 7.99183C9.20825 8.44183 8.65547 8.66683 7.99992 8.66683ZM7.99992 14.6668C7.0777 14.6668 6.21103 14.4918 5.39992 14.1418C4.58881 13.7918 3.88325 13.3168 3.28325 12.7168C2.68325 12.1168 2.20825 11.4113 1.85825 10.6002C1.50825 9.78905 1.33325 8.92239 1.33325 8.00016C1.33325 7.07794 1.50825 6.21127 1.85825 5.40016C2.20825 4.58905 2.68325 3.8835 3.28325 3.2835C3.88325 2.6835 4.58881 2.2085 5.39992 1.8585C6.21103 1.5085 7.0777 1.3335 7.99992 1.3335C8.92214 1.3335 9.78881 1.5085 10.5999 1.8585C11.411 2.2085 12.1166 2.6835 12.7166 3.2835C13.3166 3.8835 13.7916 4.58905 14.1416 5.40016C14.4916 6.21127 14.6666 7.07794 14.6666 8.00016C14.6666 8.92239 14.4916 9.78905 14.1416 10.6002C13.7916 11.4113 13.3166 12.1168 12.7166 12.7168C12.1166 13.3168 11.411 13.7918 10.5999 14.1418C9.78881 14.4918 8.92214 14.6668 7.99992 14.6668ZM7.99992 13.3335C8.58881 13.3335 9.14436 13.2474 9.66659 13.0752C10.1888 12.9029 10.6666 12.6557 11.0999 12.3335C10.6666 12.0113 10.1888 11.7641 9.66659 11.5918C9.14436 11.4196 8.58881 11.3335 7.99992 11.3335C7.41103 11.3335 6.85547 11.4196 6.33325 11.5918C5.81103 11.7641 5.33325 12.0113 4.89992 12.3335C5.33325 12.6557 5.81103 12.9029 6.33325 13.0752C6.85547 13.2474 7.41103 13.3335 7.99992 13.3335ZM7.99992 7.3335C8.28881 7.3335 8.5277 7.23905 8.71659 7.05016C8.90547 6.86127 8.99992 6.62239 8.99992 6.3335C8.99992 6.04461 8.90547 5.80572 8.71659 5.61683C8.5277 5.42794 8.28881 5.3335 7.99992 5.3335C7.71103 5.3335 7.47214 5.42794 7.28325 5.61683C7.09436 5.80572 6.99992 6.04461 6.99992 6.3335C6.99992 6.62239 7.09436 6.86127 7.28325 7.05016C7.47214 7.23905 7.71103 7.3335 7.99992 7.3335Z"
                    fill="#2A1246"
                />
            </G>
        </Svg>
    );
};
export const NotificationIcon = ({ size, color }: IconProps) => {
    return (
        <Svg height={`${size}px`} viewBox="0 -960 960 960" width={`${size}px`} fill={color}>
            <Path d="M80-560q0-100 44.5-183.5T244-882l47 64q-60 44-95.5 111T160-560H80Zm720 0q0-80-35.5-147T669-818l47-64q75 55 119.5 138.5T880-560h-80ZM160-200v-80h80v-280q0-83 50-147.5T420-792v-28q0-25 17.5-42.5T480-880q25 0 42.5 17.5T540-820v28q80 20 130 84.5T720-560v280h80v80H160Zm320-300Zm0 420q-33 0-56.5-23.5T400-160h160q0 33-23.5 56.5T480-80ZM320-280h320v-280q0-66-47-113t-113-47q-66 0-113 47t-47 113v280Z" />
        </Svg>
    );
};
export const LogoutIcon = ({ size, color }: IconProps) => {
    return (
        <Svg height="16px" viewBox="0 -960 960 960" width="16px" fill={color}>
            <Path d="M200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h280v80H200v560h280v80H200Zm440-160-55-58 102-102H360v-80h327L585-622l55-58 200 200-200 200Z" />
        </Svg>
    );
};

export const UpsellIcon = ({ color }: IconProps) => {
    return (
        <Svg stroke={color} fill={color} stroke-width="0" viewBox="0 0 512 512" height="16px" width="16px">
            <Path d="M470.7 9.4c3 3.1 5.3 6.6 6.9 10.3s2.4 7.8 2.4 12.2l0 .1v0 96c0 17.7-14.3 32-32 32s-32-14.3-32-32V109.3L310.6 214.6c-11.8 11.8-30.8 12.6-43.5 1.7L176 138.1 84.8 216.3c-13.4 11.5-33.6 9.9-45.1-3.5s-9.9-33.6 3.5-45.1l112-96c12-10.3 29.7-10.3 41.7 0l89.5 76.7L370.7 64H352c-17.7 0-32-14.3-32-32s14.3-32 32-32h96 0c8.8 0 16.8 3.6 22.6 9.3l.1 .1zM0 304c0-26.5 21.5-48 48-48H464c26.5 0 48 21.5 48 48V464c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V304zM48 416v48H96c0-26.5-21.5-48-48-48zM96 304H48v48c26.5 0 48-21.5 48-48zM464 416c-26.5 0-48 21.5-48 48h48V416zM416 304c0 26.5 21.5 48 48 48V304H416zm-96 80a64 64 0 1 0 -128 0 64 64 0 1 0 128 0z"></Path>
        </Svg>
    );
};
