import { Circle, Defs, G, LinearGradient, Mask, Path, Rect, Stop, Svg } from "react-native-svg";
export const <PERSON><PERSON><PERSON>ontentCellDeleteBundles = () => {
    return (
        <Svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M7.15482 2.15417C7.46738 1.84161 7.89131 1.66602 8.33333 1.66602H11.6667C12.1087 1.66602 12.5326 1.84161 12.8452 2.15417C13.1577 2.46673 13.3333 2.89065 13.3333 3.33268V4.99935H15.8247C15.8299 4.9993 15.835 4.9993 15.8402 4.99935H16.6667C17.1269 4.99935 17.5 5.37245 17.5 5.83268C17.5 6.29292 17.1269 6.66602 16.6667 6.66602H16.6093L15.9421 16.0102C15.9421 16.0102 15.9421 16.0101 15.9421 16.0102C15.8971 16.6408 15.6149 17.2312 15.1522 17.6621C14.6895 18.0931 14.0806 18.3327 13.4483 18.3327H6.55169C5.91936 18.3327 5.31052 18.0931 4.84779 17.6621C4.38511 17.2312 4.10289 16.641 4.05795 16.0104C4.05795 16.0103 4.05795 16.0104 4.05795 16.0104L3.39072 6.66602H3.33333C2.8731 6.66602 2.5 6.29292 2.5 5.83268C2.5 5.37245 2.8731 4.99935 3.33333 4.99935H4.15979C4.16496 4.9993 4.17012 4.9993 4.17527 4.99935H6.66667V3.33268C6.66667 2.89065 6.84226 2.46673 7.15482 2.15417ZM5.06163 6.66602L5.72038 15.8917C5.73535 16.1019 5.82944 16.2989 5.98368 16.4425C6.13792 16.5862 6.34087 16.666 6.55167 16.666H13.4484C13.6591 16.666 13.8621 16.5862 14.0163 16.4425C14.1706 16.2989 14.2646 16.1021 14.2796 15.8919L14.9384 6.66602H5.06163ZM11.6667 4.99935H8.33333V3.33268H11.6667V4.99935ZM8.33333 8.33268C8.79357 8.33268 9.16667 8.70578 9.16667 9.16602V14.166C9.16667 14.6263 8.79357 14.9993 8.33333 14.9993C7.8731 14.9993 7.5 14.6263 7.5 14.166V9.16602C7.5 8.70578 7.8731 8.33268 8.33333 8.33268ZM11.6667 8.33268C12.1269 8.33268 12.5 8.70578 12.5 9.16602V14.166C12.5 14.6263 12.1269 14.9993 11.6667 14.9993C11.2064 14.9993 10.8333 14.6263 10.8333 14.166V9.16602C10.8333 8.70578 11.2064 8.33268 11.6667 8.33268Z"
                fill="#E53E3E"
            />
        </Svg>
    );
};
export const TabsContentCellDelete = () => {
    return (
        <Svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M7.15482 2.15417C7.46738 1.84161 7.89131 1.66602 8.33333 1.66602H11.6667C12.1087 1.66602 12.5326 1.84161 12.8452 2.15417C13.1577 2.46673 13.3333 2.89065 13.3333 3.33268V4.99935H15.8247C15.8299 4.9993 15.835 4.9993 15.8402 4.99935H16.6667C17.1269 4.99935 17.5 5.37245 17.5 5.83268C17.5 6.29292 17.1269 6.66602 16.6667 6.66602H16.6093L15.9421 16.0102C15.9421 16.0102 15.9421 16.0101 15.9421 16.0102C15.8971 16.6408 15.6149 17.2312 15.1522 17.6621C14.6895 18.0931 14.0806 18.3327 13.4483 18.3327H6.55169C5.91936 18.3327 5.31052 18.0931 4.84779 17.6621C4.38511 17.2312 4.10289 16.641 4.05795 16.0104C4.05795 16.0103 4.05795 16.0104 4.05795 16.0104L3.39072 6.66602H3.33333C2.8731 6.66602 2.5 6.29292 2.5 5.83268C2.5 5.37245 2.8731 4.99935 3.33333 4.99935H4.15979C4.16496 4.9993 4.17012 4.9993 4.17527 4.99935H6.66667V3.33268C6.66667 2.89065 6.84226 2.46673 7.15482 2.15417ZM5.06163 6.66602L5.72038 15.8917C5.73535 16.1019 5.82944 16.2989 5.98368 16.4425C6.13792 16.5862 6.34087 16.666 6.55167 16.666H13.4484C13.6591 16.666 13.8621 16.5862 14.0163 16.4425C14.1706 16.2989 14.2646 16.1021 14.2796 15.8919L14.9384 6.66602H5.06163ZM11.6667 4.99935H8.33333V3.33268H11.6667V4.99935ZM8.33333 8.33268C8.79357 8.33268 9.16667 8.70578 9.16667 9.16602V14.166C9.16667 14.6263 8.79357 14.9993 8.33333 14.9993C7.8731 14.9993 7.5 14.6263 7.5 14.166V9.16602C7.5 8.70578 7.8731 8.33268 8.33333 8.33268ZM11.6667 8.33268C12.1269 8.33268 12.5 8.70578 12.5 9.16602V14.166C12.5 14.6263 12.1269 14.9993 11.6667 14.9993C11.2064 14.9993 10.8333 14.6263 10.8333 14.166V9.16602C10.8333 8.70578 11.2064 8.33268 11.6667 8.33268Z"
                fill="white"
            />
        </Svg>
    );
};
export const Starsfalse = () => {
    <Svg width="26" height="25" viewBox="0 0 26 25" fill="none">
        <Path
            d="M25.5 9.55L16.5125 8.775L13 0.5L9.4875 8.7875L0.5 9.55L7.325 15.4625L5.275 24.25L13 19.5875L20.725 24.25L18.6875 15.4625L25.5 9.55ZM13 17.25L8.3 20.0875L9.55 14.7375L5.4 11.1375L10.875 10.6625L13 5.625L15.1375 10.675L20.6125 11.15L16.4625 14.75L17.7125 20.1L13 17.25Z"
            fill="#FFE70A"
        />
    </Svg>;
};
export const Starstrue = () => {
    <Svg width="26" height="25" viewBox="0 0 26 25" fill="none">
        <Path
            d="M13 19.5875L20.725 24.25L18.675 15.4625L25.5 9.55L16.5125 8.7875L13 0.5L9.4875 8.7875L0.5 9.55L7.325 15.4625L5.275 24.25L13 19.5875Z"
            fill="#FFE70A"
        />
    </Svg>;
};
