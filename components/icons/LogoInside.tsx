import React from "react";
import { Defs, LinearGradient, Path, Stop, Svg } from "react-native-svg";

export default function LogoInside() {
    return (
        <Svg width={200} height={80} viewBox="0 0 150 60" fill="none">
            <Path
                d="M61.788 14.699c-1.64 1.554-3.656 3.474-5.684 5.424a.9.9 0 01-1.222.032 12.81 12.81 0 00-6.722-3.095.904.904 0 01-.776-.898V8.11a.74.74 0 01.755-.743h.018c5.49.282 9.855 2.515 13.629 6.015a.901.901 0 01.003 1.315l-.002.002z"
                fill="url(#paint0_linear_1538_36468)"
            />
            <Path
                d="M61.801 45.376l-5.808-5.677a.905.905 0 00-1.206-.058 12.8 12.8 0 01-8.387 2.917 12.755 12.755 0 01-7.798-2.835c-2.958-2.38-4.85-6.039-4.814-10.09a12.79 12.79 0 015.064-10.08c1.656-1.258 4.01-2.122 6.158-2.46a.912.912 0 00.78-.895v-7.85a.905.905 0 00-.989-.903c-3.19.308-6.57 1.277-9.232 2.77-.38.212-.754.435-1.117.67A22.66 22.66 0 0025.73 21.25a22.48 22.48 0 00-1.726 9.078c.215 12.13 10.15 22.002 22.308 22.168a22.58 22.58 0 0015.47-5.805.899.899 0 00.022-1.313l-.003-.002z"
                fill="#682EB0"
            />
            <Path
                d="M41.313 27.568a4.805 4.805 0 011.86-1.934c.794-.459 1.703-.686 2.727-.686 1.324 0 2.416.321 3.276.962.86.64 1.425 1.53 1.701 2.67H48.95a2.757 2.757 0 00-1.072-1.55c-.532-.38-1.19-.57-1.977-.57-1.024 0-1.85.352-2.48 1.05-.63.7-.945 1.692-.945 2.973 0 1.282.314 2.297.944 3.004.63.706 1.457 1.06 2.48 1.06.788 0 1.443-.184 1.968-.55.525-.367.887-.89 1.083-1.57h1.927c-.288 1.1-.866 1.98-1.732 2.64-.866.66-1.948.992-3.245.992-1.024 0-1.932-.23-2.727-.686a4.793 4.793 0 01-1.86-1.944c-.446-.838-.67-1.82-.67-2.946 0-1.125.222-2.083.67-2.915zM55.214 35.37a4.904 4.904 0 01-1.928-1.953c-.467-.844-.699-1.822-.699-2.936 0-1.113.24-2.07.718-2.915a4.938 4.938 0 011.959-1.944c.825-.45 1.752-.678 2.773-.678 1.023 0 1.949.225 2.775.678a4.965 4.965 0 011.958 1.934c.477.838.717 1.812.717 2.925 0 1.114-.245 2.092-.738 2.936a5.116 5.116 0 01-1.998 1.953c-.84.46-1.77.687-2.793.687-1.024 0-1.926-.23-2.744-.687zm4.553-1.334c.564-.302 1.02-.753 1.368-1.355.349-.603.523-1.335.523-2.2 0-.864-.17-1.596-.513-2.199-.342-.602-.786-1.05-1.338-1.345a3.727 3.727 0 00-1.79-.44c-.642 0-1.256.147-1.8.44-.544.296-.98.743-1.307 1.345-.328.603-.492 1.335-.492 2.2 0 .864.16 1.616.482 2.218.322.603.75 1.05 1.288 1.345a3.616 3.616 0 001.77.441c.642 0 1.245-.15 1.81-.45zM74.29 26.115c.813.793 1.22 1.934 1.22 3.427v6.341h-1.77v-6.086c0-1.074-.27-1.894-.806-2.464-.538-.57-1.272-.854-2.204-.854-.933 0-1.697.295-2.252.884-.558.588-.837 1.446-.837 2.572v5.948h-1.79V25.125h1.79v1.531a3.543 3.543 0 011.447-1.275 4.489 4.489 0 012.016-.451c1.311 0 2.374.395 3.188 1.187l-.002-.002zM82.456 34.233l3.343-9.11h1.908l-4.23 10.758h-2.086l-4.23-10.758h1.928l3.365 9.11h.002zM99.257 31.17H90.64c.067 1.06.43 1.888 1.093 2.482.662.597 1.466.894 2.41.894.773 0 1.42-.18 1.938-.538.517-.36.882-.84 1.091-1.443H99.1a4.623 4.623 0 01-1.731 2.522c-.867.649-1.942.972-3.227.972-1.023 0-1.938-.23-2.744-.686a4.871 4.871 0 01-1.898-1.954c-.46-.844-.688-1.822-.688-2.935 0-1.114.224-2.088.668-2.926a4.678 4.678 0 011.88-1.934c.806-.45 1.735-.678 2.785-.678s1.927.223 2.715.668a4.635 4.635 0 011.82 1.836c.427.779.64 1.659.64 2.64 0 .342-.02.7-.058 1.08h-.004zm-2.244-3.21a2.945 2.945 0 00-1.23-1.12 3.84 3.84 0 00-1.722-.384c-.906 0-1.676.288-2.312.865-.635.576-1 1.374-1.091 2.394h6.808c0-.68-.152-1.267-.453-1.758v.002zM104.815 25.439c.584-.341 1.296-.511 2.134-.511v1.846h-.472c-2.006 0-3.01 1.085-3.01 3.258v5.851h-1.79V25.125h1.79v1.749a3.42 3.42 0 011.348-1.433v-.002zM111.201 26.596v6.342c0 .523.112.894.334 1.11.222.215.61.323 1.16.323h1.318v1.51h-1.614c-.998 0-1.744-.23-2.242-.686-.498-.46-.748-1.211-.748-2.257v-6.342h-1.396v-1.473h1.396v-2.71h1.79v2.71h2.814v1.473h-2.814.002zM125.465 25.123l-6.491 15.823h-1.85l2.124-5.182-4.348-10.64h1.988l3.384 8.716 3.343-8.717h1.85z"
                fill="#231F20"
            />
            <Path
                d="M99.39 40.888a.372.372 0 01-.112-.271c0-.106.037-.198.111-.272a.373.373 0 01.273-.111c.105 0 .19.038.263.111a.368.368 0 01.108.272.376.376 0 01-.644.271zM101.283 40.886a1.172 1.172 0 01-.49-.371.978.978 0 01-.198-.547h.582c.016.17.096.307.238.413.142.108.33.162.56.162.214 0 .384-.048.508-.142a.432.432 0 00.186-.359.359.359 0 00-.198-.331c-.132-.074-.336-.144-.612-.214a4.457 4.457 0 01-.616-.201 1.089 1.089 0 01-.408-.304c-.114-.133-.17-.309-.17-.528 0-.174.052-.332.154-.475.104-.144.25-.26.44-.344.19-.083.406-.127.65-.127.376 0 .68.096.91.283.23.19.354.449.372.778h-.564a.582.582 0 00-.214-.427c-.13-.108-.304-.161-.524-.161-.202 0-.364.044-.484.13a.4.4 0 00-.18.339c0 .111.036.203.108.275.072.072.164.13.272.173.11.044.262.092.456.146.244.066.442.132.594.194.152.064.284.16.394.287.11.128.166.296.17.501a.863.863 0 01-.154.5 1.034 1.034 0 01-.436.35 1.577 1.577 0 01-.648.126c-.26 0-.494-.044-.7-.132l.002.006zM106.394 37.68c.206.11.368.273.486.49.118.218.176.485.176.796v1.996h-.558v-1.916c0-.337-.084-.596-.254-.776-.17-.18-.4-.27-.694-.27-.294 0-.534.094-.708.278-.176.186-.262.455-.262.81v1.872h-.564v-4.572h.564v1.669c.112-.174.264-.308.462-.402.198-.093.416-.141.66-.141.256 0 .488.054.694.163l-.002.002zM108.593 40.802a1.534 1.534 0 01-.606-.614 1.885 1.885 0 01-.22-.924c0-.352.076-.653.226-.918.15-.266.356-.47.616-.61.26-.142.552-.214.874-.214.322 0 .612.072.872.213.26.142.466.345.616.609.15.263.226.57.226.92 0 .349-.078.658-.232.924a1.6 1.6 0 01-.628.614 1.812 1.812 0 01-.88.216c-.322 0-.606-.072-.864-.216zm1.434-.419c.178-.094.32-.237.43-.427.11-.19.164-.419.164-.692 0-.274-.054-.503-.162-.693a1.049 1.049 0 00-.422-.423 1.192 1.192 0 00-.564-.14c-.202 0-.396.046-.566.14a1.026 1.026 0 00-.412.423c-.104.19-.154.421-.154.693 0 .271.05.509.152.698.1.19.236.331.406.423a1.206 1.206 0 001.128-.002zM113.001 37.717c.22-.13.479-.195.773-.195.294 0 .573.072.819.215.246.144.439.347.581.609.14.261.21.565.21.912 0 .347-.07.646-.21.914a1.542 1.542 0 01-1.4.846c-.3 0-.544-.064-.764-.194a1.388 1.388 0 01-.505-.485v2.231h-.564v-4.99h.564v.624c.112-.193.278-.355.498-.485l-.002-.002zm1.65.874a1.077 1.077 0 00-.417-.433 1.174 1.174 0 00-.579-.147 1.102 1.102 0 00-.992.59 1.35 1.35 0 00-.158.665c0 .251.052.479.158.67.106.192.247.337.421.44.176.101.365.15.571.15.206 0 .405-.049.579-.15.176-.1.313-.248.417-.44.104-.191.154-.417.154-.676 0-.26-.052-.477-.154-.667v-.002z"
                fill="#737577"
            />
            <Defs>
                <LinearGradient
                    id="paint0_linear_1538_36468"
                    x1={47.3835}
                    y1={13.8728}
                    x2={62.0715}
                    y2={13.8728}
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="gold" />
                    <Stop offset={1} stopColor="#D5B519" />
                </LinearGradient>
            </Defs>
        </Svg>
    );
}
