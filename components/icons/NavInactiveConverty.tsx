import colors from "@styles/colors";
import React from "react";
import { StyleSheet } from "react-native";
import Svg, { Circle, Defs, LinearGradient, Path, Rect, Stop } from "react-native-svg";

export default function NavInactiveConverty() {
    return (
        // <View style={styles.buttonicon}>
        //     <View style={styles.iconconverty}>
        //         <Svg
        //             style={styles.group}
        //             width="20"
        //             height="24"
        //             viewBox="0 0 20 24"
        //             fill="none">
        //             <Path
        //                 d="M19.7605 4.32943C18.9361 5.11304 17.9239 6.07959 16.9042 7.06096C16.7348 7.22411 16.4666 7.234 16.2899 7.07703C15.3629 6.25633 14.1986 5.69767 12.9119 5.51845C12.687 5.48755 12.5214 5.2935 12.5214 5.06608V1.01079C12.5214 0.801913 12.6919 0.632582 12.9008 0.63629C12.9045 0.63629 12.9082 0.63629 12.9095 0.63629C15.6682 0.777193 17.8608 1.90194 19.7568 3.6657C19.9484 3.84368 19.9484 4.14774 19.7581 4.32819L19.7605 4.32943Z"
        //                 fill="url(#paint0_linear_4973_27299)"
        //             />
        //             <Path
        //                 d="M19.7667 19.7793L16.8486 16.9205C16.6829 16.7586 16.4209 16.7437 16.2429 16.8908C15.1009 17.8326 13.6288 18.3888 12.0282 18.3604C10.5487 18.3344 9.18913 17.803 8.11012 16.9316C6.62322 15.7327 5.67275 13.8911 5.69129 11.8504C5.69252 11.6144 5.70859 11.3808 5.73455 11.1521C5.93107 9.46375 6.78143 7.97439 8.0236 6.94234C8.09281 6.88424 8.16327 6.82862 8.23495 6.77424C9.06677 6.14018 10.2509 5.70511 11.3286 5.53454C11.5523 5.4987 11.7204 5.30959 11.7204 5.08341V1.12948C11.7204 0.861268 11.4905 0.648678 11.2248 0.674634C9.62173 0.830369 7.92348 1.31858 6.58614 2.06883C6.3958 2.17512 6.20793 2.2876 6.02377 2.40625C5.53308 2.71896 5.06958 3.06874 4.63452 3.4519C3.34291 4.58901 2.31209 6.01411 1.64095 7.62707C1.05633 9.03116 0.746095 10.5774 0.774522 12.199C0.883289 18.3073 5.87421 23.2784 11.9837 23.3625C14.9736 23.4032 17.7039 22.2896 19.7568 20.4393C19.9509 20.2651 19.9546 19.9598 19.768 19.7781L19.7667 19.7793Z"
        //                 fill="#682EB0"
        //             />
        //             <Defs>
        //                 <LinearGradient
        //                     id="paint0_linear_4973_27299"
        //                     x1="12.5226"
        //                     y1="3.91413"
        //                     x2="19.9027"
        //                     y2="3.91413"
        //                     gradientUnits="userSpaceOnUse">
        //                     <Stop stopColor="#FFD700" />
        //                     <Stop offset="1" stopColor="#D5B519" />
        //                 </LinearGradient>
        //             </Defs>
        //         </Svg>
        //     </View>
        // </View>
        <Svg width="43" height="43" viewBox="0 0 43 43" fill="none">
            <Defs>
                <LinearGradient
                    id="paint0_linear_8162_6747"
                    x1="23.3406"
                    y1="13.233"
                    x2="30.7207"
                    y2="13.233"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#FFD700" />
                    <Stop offset="1" stopColor="#D5B519" />
                </LinearGradient>
            </Defs>
            <Rect x="2.5" y="2" width="38.6364" height="38.6364" rx="19.3182" fill="white" />
            <Path
                d="M30.5785 13.6483C29.7541 14.4319 28.7419 15.3984 27.7222 16.3798C27.5528 16.543 27.2846 16.5528 27.1079 16.3959C26.1809 15.5752 25.0166 15.0165 23.7299 14.8373C23.505 14.8064 23.3394 14.6123 23.3394 14.3849V10.3296C23.3394 10.1208 23.5099 9.95143 23.7188 9.95514C23.7225 9.95514 23.7262 9.95514 23.7275 9.95514C26.4862 10.096 28.6788 11.2208 30.5748 12.9845C30.7664 13.1625 30.7664 13.4666 30.5761 13.647L30.5785 13.6483Z"
                fill="url(#paint0_linear_8162_6747)"
            />
            <Path
                d="M30.5848 29.098L27.6666 26.2392C27.501 26.0773 27.239 26.0625 27.061 26.2095C25.919 27.1514 24.4469 27.7076 22.8463 27.6791C21.3668 27.6532 20.0072 27.1217 18.9282 26.2503C17.4413 25.0514 16.4908 23.2098 16.5094 21.1692C16.5106 20.9331 16.5267 20.6995 16.5526 20.4708C16.7492 18.7825 17.5995 17.2931 18.8417 16.2611C18.9109 16.203 18.9814 16.1473 19.053 16.093C19.8849 15.4589 21.0689 15.0238 22.1467 14.8533C22.3704 14.8174 22.5385 14.6283 22.5385 14.4021V10.4482C22.5385 10.18 22.3086 9.9674 22.0429 9.99336C20.4398 10.1491 18.7416 10.6373 17.4042 11.3876C17.2139 11.4939 17.026 11.6063 16.8419 11.725C16.3512 12.0377 15.8877 12.3875 15.4526 12.7706C14.161 13.9077 13.1302 15.3328 12.459 16.9458C11.8744 18.3499 11.5642 19.8961 11.5926 21.5177C11.7014 27.626 16.6923 32.5971 22.8018 32.6812C25.7917 32.722 28.522 31.6083 30.5749 29.7581C30.769 29.5838 30.7727 29.2785 30.5861 29.0968L30.5848 29.098Z"
                fill="#682EB0"
            />
            <Circle cx="21.8182" cy="21.3182" r="20.1705" stroke="#FFD700" strokeWidth="1.70455" />
        </Svg>
    );
}

const styles = StyleSheet.create({
    buttonicon: {
        flexShrink: 0,
        backgroundColor: "rgba(255, 255, 255, 1)",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        columnGap: 11.363636016845703,
        padding: 5.681818008422852,
        borderRadius: 1135.227294921875,
    },
    iconconverty: {
        flexShrink: 0,
        height: 39,
        width: 39,
        backgroundColor: colors.white,
        shadowColor: "rgb(0, 0, 0)",
        shadowOpacity: 0.25,
        shadowRadius: 4.545454502105713,
        rowGap: 0,
        borderRadius: 112.49999237060547,
    },
    group: {
        position: "absolute",
        flexShrink: 0,
        top: 8,
        height: 23,
        left: 9,
        width: 19,
    },
});
