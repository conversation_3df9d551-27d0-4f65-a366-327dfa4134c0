import React from "react";
import { Platform, StyleSheet, TouchableOpacity, View } from "react-native";
import { Circle, G, Mask, Path, Rect, Svg } from "react-native-svg";

import NavActiveConverty from "./NavActiveConverty";
import NavInactiveConverty from "./NavInactiveConverty";
import colors from "../../styles/colors";

interface NavIcon {
    active: boolean;
    onPress?: () => void;
    onLongPress?: () => void;
    accessibilityLabel?: string;
    icon?: any;
}

const Ellipse = () => {
    return (
        <View
            style={{
                width: 6,
                height: 6,
            }}
        >
            <Svg width="6" height="6" viewBox="0 0 6 6" fill="none">
                <Circle cx="3" cy="3" r="3" fill="#682EB0" />
            </Svg>
        </View>
    );
};

const Icon = ({ active, icon, accessibilityLabel, onPress, onLongPress }: NavIcon) => {
    return (
        <TouchableOpacity
            accessibilityRole="button"
            accessibilityState={active ? { selected: true } : {}}
            accessibilityLabel={accessibilityLabel}
            onPress={onPress}
            onLongPress={onLongPress}
            style={styles.navIconTouch}
        >
            <View style={styles.navIcon}>
                {icon}
                {active ? <Ellipse /> : <View style={{ width: 6, height: 6 }} />}
            </View>
        </TouchableOpacity>
    );
};

export const HiOutlineQrCode = ({ active, accessibilityLabel, onPress, onLongPress }: NavIcon) => {
    return (
        <Icon
            active={active}
            accessibilityLabel={accessibilityLabel}
            onPress={onPress}
            onLongPress={onLongPress}
            icon={
                <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <Path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H7C7.53043 3 8.03914 3.21071 8.41421 3.58579C8.78929 3.96086 9 4.46957 9 5V7C9 7.53043 8.78929 8.03914 8.41421 8.41421C8.03914 8.78929 7.53043 9 7 9H5C4.46957 9 3.96086 8.78929 3.58579 8.41421C3.21071 8.03914 3 7.53043 3 7V5C3 4.46957 3.21071 3.96086 3.58579 3.58579ZM7 5L5 5L5 7H7V5ZM12 3C12.5523 3 13 3.44772 13 4V5C13 5.55228 12.5523 6 12 6C11.4477 6 11 5.55228 11 5V4C11 3.44772 11.4477 3 12 3ZM15.5858 3.58579C15.9609 3.21071 16.4696 3 17 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V7C21 7.53043 20.7893 8.03914 20.4142 8.41421C20.0391 8.78929 19.5304 9 19 9H17C16.4696 9 15.9609 8.78929 15.5858 8.41421C15.2107 8.03914 15 7.53043 15 7V5C15 4.46957 15.2107 3.96086 15.5858 3.58579ZM19 5H17L17 7H19V5ZM12 8C12.5523 8 13 8.44772 13 9V11H16.01C16.5623 11 17.01 11.4477 17.01 12C17.01 12.5523 16.5623 13 16.01 13H12C11.4477 13 11 12.5523 11 12V9C11 8.44772 11.4477 8 12 8ZM3 12C3 11.4477 3.44772 11 4 11H8C8.55228 11 9 11.4477 9 12C9 12.5523 8.55228 13 8 13H4C3.44772 13 3 12.5523 3 12ZM19 12C19 11.4477 19.4477 11 20 11H20.01C20.5623 11 21.01 11.4477 21.01 12C21.01 12.5523 20.5623 13 20.01 13H20C19.4477 13 19 12.5523 19 12ZM3.58579 15.5858C3.96086 15.2107 4.46957 15 5 15H7C7.53043 15 8.03914 15.2107 8.41421 15.5858C8.78929 15.9609 9 16.4696 9 17V19C9 19.5304 8.78929 20.0391 8.41421 20.4142C8.03914 20.7893 7.53043 21 7 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V17C3 16.4696 3.21071 15.9609 3.58579 15.5858ZM7 17L5 17L5 19H7V17ZM11 16C11 15.4477 11.4477 15 12 15H14C14.5523 15 15 15.4477 15 16C15 16.5523 14.5523 17 14 17H13V20C13 20.5523 12.5523 21 12 21C11.4477 21 11 20.5523 11 20V16ZM17 16C17 15.4477 17.4477 15 18 15H20C20.5523 15 21 15.4477 21 16C21 16.5523 20.5523 17 20 17H18C17.4477 17 17 16.5523 17 16ZM15 20C15 19.4477 15.4477 19 16 19H20C20.5523 19 21 19.4477 21 20C21 20.5523 20.5523 21 20 21H16C15.4477 21 15 20.5523 15 20Z"
                        fill={active ? colors.primary[500] : colors.gray[400]}
                    />
                </Svg>
            }
        />
    );
};

export const HiOutlineShoppingCart = ({ active, accessibilityLabel, onPress, onLongPress }: NavIcon) => {
    return (
        <Icon
            active={active}
            accessibilityLabel={accessibilityLabel}
            onPress={onPress}
            onLongPress={onLongPress}
            icon={
                <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <Path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M2 3C2 2.44772 2.44772 2 3 2H5C5.47668 2 5.8871 2.33646 5.98058 2.80388L6.2198 4H21C21.3466 4 21.6684 4.17945 21.8507 4.47427C22.0329 4.76909 22.0494 5.13723 21.8944 5.44721L17.8944 13.4472C17.725 13.786 17.3788 14 17 14H7.41421L5.41421 16H17C17.7957 16 18.5587 16.3161 19.1213 16.8787C19.6839 17.4413 20 18.2043 20 19C20 19.7957 19.6839 20.5587 19.1213 21.1213C18.5587 21.6839 17.7957 22 17 22C16.2043 22 15.4413 21.6839 14.8787 21.1213C14.3161 20.5587 14 19.7957 14 19C14 18.6561 14.0591 18.3182 14.1716 18H9.82843C9.94094 18.3182 10 18.6561 10 19C10 19.7957 9.68393 20.5587 9.12132 21.1213C8.55871 21.6839 7.79565 22 7 22C6.20435 22 5.44129 21.6839 4.87868 21.1213C4.31607 20.5587 4 19.7957 4 19C4 18.5359 4.10754 18.0829 4.3088 17.6743C3.32906 17.0242 3.04025 15.5455 3.99989 14.5859L5.91446 12.6713L4.42347 5.21636C4.42066 5.20365 4.4181 5.19084 4.41579 5.17795L4.1802 4H3C2.44772 4 2 3.55228 2 3ZM6.6198 6L7.8198 12H16.382L19.382 6H6.6198ZM7 18C6.73478 18 6.48043 18.1054 6.29289 18.2929C6.10536 18.4804 6 18.7348 6 19C6 19.2652 6.10536 19.5196 6.29289 19.7071C6.48043 19.8946 6.73478 20 7 20C7.26522 20 7.51957 19.8946 7.70711 19.7071C7.89464 19.5196 8 19.2652 8 19C8 18.7348 7.89464 18.4804 7.70711 18.2929C7.51957 18.1054 7.26522 18 7 18ZM17 18C16.7348 18 16.4804 18.1054 16.2929 18.2929C16.1054 18.4804 16 18.7348 16 19C16 19.2652 16.1054 19.5196 16.2929 19.7071C16.4804 19.8946 16.7348 20 17 20C17.2652 20 17.5196 19.8946 17.7071 19.7071C17.8946 19.5196 18 19.2652 18 19C18 18.7348 17.8946 18.4804 17.7071 18.2929C17.5196 18.1054 17.2652 18 17 18Z"
                        fill={active ? colors.primary[500] : colors.gray[400]}
                    />
                </Svg>
            }
        />
    );
};

export const OrdersIcons = ({ active, accessibilityLabel, onPress, onLongPress }: NavIcon) => {
    return (
        <Icon
            active={active}
            accessibilityLabel={accessibilityLabel}
            onPress={onPress}
            onLongPress={onLongPress}
            icon={
                <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <Mask id="mask0_8164_6767" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                        <Rect width="24" height="24" fill="#D9D9D9" />
                    </Mask>
                    <G mask="url(#mask0_8164_6767)">
                        <Path
                            d="M11 19.425V12.575L5 9.1V15.95L11 19.425ZM13 19.425L19 15.95V9.1L13 12.575V19.425ZM11 21.725L4 17.7C3.68333 17.5167 3.4375 17.275 3.2625 16.975C3.0875 16.675 3 16.3417 3 15.975V8.025C3 7.65833 3.0875 7.325 3.2625 7.025C3.4375 6.725 3.68333 6.48333 4 6.3L11 2.275C11.3167 2.09167 11.65 2 12 2C12.35 2 12.6833 2.09167 13 2.275L20 6.3C20.3167 6.48333 20.5625 6.725 20.7375 7.025C20.9125 7.325 21 7.65833 21 8.025V15.975C21 16.3417 20.9125 16.675 20.7375 16.975C20.5625 17.275 20.3167 17.5167 20 17.7L13 21.725C12.6833 21.9083 12.35 22 12 22C11.65 22 11.3167 21.9083 11 21.725ZM16 8.525L17.925 7.425L12 4L10.05 5.125L16 8.525ZM12 10.85L13.95 9.725L8.025 6.3L6.075 7.425L12 10.85Z"
                            fill={active ? colors.primary[500] : colors.gray[400]}
                        />
                    </G>
                </Svg>
            }
        />
    );
};

export const HiOutlineChartPie = ({ active, accessibilityLabel, onPress, onLongPress }: NavIcon) => {
    return (
        <Icon
            active={active}
            accessibilityLabel={accessibilityLabel}
            onPress={onPress}
            onLongPress={onLongPress}
            icon={
                <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <Path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M11.6667 2.30972C11.8788 2.49943 12 2.7705 12 3.05506V12.0001H20.945C21.2296 12.0001 21.5006 12.1213 21.6903 12.3334C21.88 12.5455 21.9704 12.8283 21.9388 13.1111C21.7296 14.9828 20.9962 16.7573 19.823 18.2306C18.6498 19.7038 17.0844 20.8158 15.3071 21.4387C13.5297 22.0615 11.6127 22.1698 9.77647 21.7511C7.94028 21.3325 6.25962 20.4038 4.92791 19.0721C3.59621 17.7404 2.66759 16.0598 2.24893 14.2236C1.83027 12.3874 1.93859 10.4703 2.56141 8.69297C3.18423 6.91563 4.29625 5.35026 5.76948 4.17704C7.24271 3.00381 9.01728 2.27041 10.8889 2.06125C11.1717 2.02964 11.4546 2.12001 11.6667 2.30972ZM10 4.25329C8.9157 4.53317 7.89796 5.03871 7.0154 5.74155C5.83679 6.68015 4.94715 7.93248 4.44888 9.35439C3.95061 10.7763 3.86395 12.31 4.19889 13.779C4.53382 15.248 5.27674 16.5925 6.34213 17.6579C7.40752 18.7233 8.75208 19.4662 10.2211 19.8012C11.6901 20.1361 13.2238 20.0495 14.6457 19.5512C16.0676 19.0529 17.3199 18.1633 18.2585 16.9847C18.9613 16.1021 19.4669 15.0844 19.7468 14.0001H11C10.4477 14.0001 10 13.5523 10 13.0001V4.25329Z"
                        fill={active ? colors.primary[500] : colors.gray[400]}
                    />
                    <Path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M14.4231 2.69516C14.6884 2.50776 15.0282 2.46091 15.3344 2.56952C16.7399 3.06819 18.0166 3.8742 19.0712 4.9288C20.1258 5.9834 20.9318 7.26002 21.4304 8.66561L20.488 8.99997V9.99997H15C14.4477 9.99997 14 9.55225 14 8.99997V3.51197C14 3.18714 14.1578 2.88255 14.4231 2.69516ZM18.9293 7.99997C18.5803 7.39637 18.153 6.83902 17.657 6.34302C17.161 5.84701 16.6036 5.4197 16 5.07063V7.99997H18.9293Z"
                        fill={active ? colors.primary[500] : colors.gray[400]}
                    />
                </Svg>
            }
        />
    );
};

export const ToolsIcon = ({ active, accessibilityLabel, onPress, onLongPress }: NavIcon) => {
    return (
        <Icon
            active={active}
            accessibilityLabel={accessibilityLabel}
            onPress={onPress}
            onLongPress={onLongPress}
            icon={
                <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <Mask id="mask0_8173_6776" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                        <Rect width="24" height="24" fill="#D9D9D9" />
                    </Mask>
                    <G mask="url(#mask0_8173_6776)">
                        <Path
                            d="M8 18H9.5V16H11.5V14.5H9.5V12.5H8V14.5H6V16H8V18ZM13 17.25H18V15.75H13V17.25ZM13 14.75H18V13.25H13V14.75ZM14.1 10.95L15.5 9.55L16.9 10.95L17.95 9.9L16.55 8.45L17.95 7.05L16.9 6L15.5 7.4L14.1 6L13.05 7.05L14.45 8.45L13.05 9.9L14.1 10.95ZM6.25 9.2H11.25V7.7H6.25V9.2ZM5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H19C19.55 3 20.0208 3.19583 20.4125 3.5875C20.8042 3.97917 21 4.45 21 5V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5ZM5 19H19V5H5V19Z"
                            fill={active ? colors.primary[500] : colors.gray[400]}
                        />
                    </G>
                </Svg>
            }
        />
    );
};

export const HiOutlineUser = ({ active, accessibilityLabel, onPress, onLongPress }: NavIcon) => {
    return (
        <Icon
            active={active}
            accessibilityLabel={accessibilityLabel}
            onPress={onPress}
            onLongPress={onLongPress}
            icon={
                <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <Path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M12 4C11.2044 4 10.4413 4.31607 9.87868 4.87868C9.31607 5.44129 9 6.20435 9 7C9 7.79565 9.31607 8.55871 9.87868 9.12132C10.4413 9.68393 11.2044 10 12 10C12.7956 10 13.5587 9.68393 14.1213 9.12132C14.6839 8.55871 15 7.79565 15 7C15 6.20435 14.6839 5.44129 14.1213 4.87868C13.5587 4.31607 12.7956 4 12 4ZM8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7C17 8.32608 16.4732 9.59785 15.5355 10.5355C14.5979 11.4732 13.3261 12 12 12C10.6739 12 9.40215 11.4732 8.46447 10.5355C7.52678 9.59785 7 8.32608 7 7C7 5.67392 7.52678 4.40215 8.46447 3.46447ZM6.34315 15.3431C7.84344 13.8429 9.87827 13 12 13C14.1217 13 16.1566 13.8429 17.6569 15.3431C19.1571 16.8434 20 18.8783 20 21C20 21.5523 19.5523 22 19 22H5C4.73478 22 4.48043 21.8946 4.29289 21.7071C4.10536 21.5196 4 21.2652 4 21C4 18.8783 4.84285 16.8434 6.34315 15.3431ZM12 15C10.4087 15 8.88258 15.6321 7.75736 16.7574C6.87067 17.6441 6.29016 18.7797 6.08389 20H17.9161C17.7098 18.7797 17.1293 17.6441 16.2426 16.7574C15.1174 15.6321 13.5913 15 12 15Z"
                        fill={active ? colors.primary[500] : colors.gray[400]}
                    />
                </Svg>
            }
        />
    );
};

export const AccountIcon = ({ active, accessibilityLabel, onPress, onLongPress }: NavIcon) => (
    <Icon
        active={active}
        accessibilityLabel={accessibilityLabel}
        onPress={onPress}
        onLongPress={onLongPress}
        icon={
            <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <Mask id="mask0_8164_6770" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                    <Rect width="24" height="24" fill="#D9D9D9" />
                </Mask>
                <G mask="url(#mask0_8164_6770)">
                    <Path
                        d="M5.85 17.1C6.7 16.45 7.65 15.9375 8.7 15.5625C9.75 15.1875 10.85 15 12 15C13.15 15 14.25 15.1875 15.3 15.5625C16.35 15.9375 17.3 16.45 18.15 17.1C18.7333 16.4167 19.1875 15.6417 19.5125 14.775C19.8375 13.9083 20 12.9833 20 12C20 9.78333 19.2208 7.89583 17.6625 6.3375C16.1042 4.77917 14.2167 4 12 4C9.78333 4 7.89583 4.77917 6.3375 6.3375C4.77917 7.89583 4 9.78333 4 12C4 12.9833 4.1625 13.9083 4.4875 14.775C4.8125 15.6417 5.26667 16.4167 5.85 17.1ZM12 13C11.0167 13 10.1875 12.6625 9.5125 11.9875C8.8375 11.3125 8.5 10.4833 8.5 9.5C8.5 8.51667 8.8375 7.6875 9.5125 7.0125C10.1875 6.3375 11.0167 6 12 6C12.9833 6 13.8125 6.3375 14.4875 7.0125C15.1625 7.6875 15.5 8.51667 15.5 9.5C15.5 10.4833 15.1625 11.3125 14.4875 11.9875C13.8125 12.6625 12.9833 13 12 13ZM12 22C10.6167 22 9.31667 21.7375 8.1 21.2125C6.88333 20.6875 5.825 19.975 4.925 19.075C4.025 18.175 3.3125 17.1167 2.7875 15.9C2.2625 14.6833 2 13.3833 2 12C2 10.6167 2.2625 9.31667 2.7875 8.1C3.3125 6.88333 4.025 5.825 4.925 4.925C5.825 4.025 6.88333 3.3125 8.1 2.7875C9.31667 2.2625 10.6167 2 12 2C13.3833 2 14.6833 2.2625 15.9 2.7875C17.1167 3.3125 18.175 4.025 19.075 4.925C19.975 5.825 20.6875 6.88333 21.2125 8.1C21.7375 9.31667 22 10.6167 22 12C22 13.3833 21.7375 14.6833 21.2125 15.9C20.6875 17.1167 19.975 18.175 19.075 19.075C18.175 19.975 17.1167 20.6875 15.9 21.2125C14.6833 21.7375 13.3833 22 12 22ZM12 20C12.8833 20 13.7167 19.8708 14.5 19.6125C15.2833 19.3542 16 18.9833 16.65 18.5C16 18.0167 15.2833 17.6458 14.5 17.3875C13.7167 17.1292 12.8833 17 12 17C11.1167 17 10.2833 17.1292 9.5 17.3875C8.71667 17.6458 8 18.0167 7.35 18.5C8 18.9833 8.71667 19.3542 9.5 19.6125C10.2833 19.8708 11.1167 20 12 20ZM12 11C12.4333 11 12.7917 10.8583 13.075 10.575C13.3583 10.2917 13.5 9.93333 13.5 9.5C13.5 9.06667 13.3583 8.70833 13.075 8.425C12.7917 8.14167 12.4333 8 12 8C11.5667 8 11.2083 8.14167 10.925 8.425C10.6417 8.70833 10.5 9.06667 10.5 9.5C10.5 9.93333 10.6417 10.2917 10.925 10.575C11.2083 10.8583 11.5667 11 12 11Z"
                        fill={active ? colors.primary[500] : colors.gray[400]}
                    />
                </G>
            </Svg>
        }
    />
);

export const ConvertyNavIcon = ({ active, onPress, onLongPress, accessibilityLabel }: NavIcon) => {
    const shadowStyle = Platform.select({
        android: {
            elevation: 0,
        },
        ios: {
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.2,
            shadowRadius: 4,
            backgroundColor: colors.secondary[500],
        },
    });
    return (
        <TouchableOpacity
            accessibilityRole="button"
            onPress={onPress}
            onLongPress={onLongPress}
            accessibilityLabel={accessibilityLabel}
            accessibilityState={active ? { selected: true } : {}}
            style={styles.navIconTouch}
        >
            <View
                style={[
                    {
                        backgroundColor: "rgba(255,255,255,0)",
                        borderRadius: 200,
                        padding: 0,
                    },
                    shadowStyle,
                ]}
            >
                {active ? <NavActiveConverty /> : <NavInactiveConverty />}
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    navIcon: {
        // icon with a dot under it when active
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "space-between",
    },
    navIconTouch: {
        shadowOffset: { width: 15, height: 15 },
        width: 50,
        height: 50,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
    },
});
