import { G, Mask, Path, Rect, Svg } from "react-native-svg";

export type IconProp = {
    color: string;
};

export const DepositIcon = ({ color }: IconProp) => {
    return (
        <Svg width="36" height="36" viewBox="0 0 36 36" fill="none">
            <Mask id="mask0_7765_31073" maskUnits="userSpaceOnUse" x="0" y="0" width="36" height="36">
                <Rect width="36" height="36" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_7765_31073)">
                <Path
                    d="M6 28.5H9V16.5H27V28.5H30V12.525L18 7.725L6 12.525V28.5ZM3 31.5V10.5L18 4.5L33 10.5V31.5H24V19.5H12V31.5H3ZM13.5 31.5V28.5H16.5V31.5H13.5ZM16.5 27V24H19.5V27H16.5ZM19.5 31.5V28.5H22.5V31.5H19.5Z"
                    fill={color}
                />
            </G>
        </Svg>
    );
};

export const DeliveredIcon = ({ color }: IconProp) => {
    return (
        <Svg width="37" height="36" viewBox="0 0 37 36" fill="none">
            <Mask id="mask0_7768_6128" maskUnits="userSpaceOnUse" x="0" y="0" width="37" height="36">
                <Rect x="0.75" width="36" height="36" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_7768_6128)">
                <Path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M27.75 18.1244V13.65L18.75 18.8625V22.0233C17.8022 23.4488 17.25 25.1599 17.25 27C17.25 29.0309 17.9227 30.9047 19.0574 32.4107L18.75 32.5875C18.275 32.8625 17.775 33 17.25 33C16.725 33 16.225 32.8625 15.75 32.5875L5.25 26.55C4.775 26.275 4.40625 25.9125 4.14375 25.4625C3.88125 25.0125 3.75 24.5125 3.75 23.9625V12.0375C3.75 11.4875 3.88125 10.9875 4.14375 10.5375C4.40625 10.0875 4.775 9.725 5.25 9.45L15.75 3.4125C16.225 3.1375 16.725 3 17.25 3C17.775 3 18.275 3.1375 18.75 3.4125L29.25 9.45C29.725 9.725 30.0938 10.0875 30.3563 10.5375C30.6188 10.9875 30.75 11.4875 30.75 12.0375V19.204C29.8389 18.677 28.8269 18.3051 27.75 18.1244ZM15.75 18.8625V29.1375L6.75 23.925V13.65L15.75 18.8625ZM26.1375 11.1375L23.25 12.7875L14.325 7.6875L17.25 6L26.1375 11.1375ZM20.175 14.5875L17.25 16.275L8.3625 11.1375L11.2875 9.45L20.175 14.5875Z"
                    fill={color}
                />
                <Path
                    d="M24.25 31.3998L20.25 27.3998L21.65 25.9998L24.25 28.5998L30.85 21.9998L32.25 23.3998L24.25 31.3998Z"
                    fill={color}
                />
            </G>
        </Svg>
    );
};

export const TransitIcon = ({ color }: IconProp) => {
    return (
        <Svg width="37" height="36" viewBox="0 0 37 36" fill="none">
            <Path
                d="M14 25.5C14 26.2956 13.6839 27.0587 13.1213 27.6213C12.5587 28.1839 11.7956 28.5 11 28.5C10.2044 28.5 9.44129 28.1839 8.87868 27.6213C8.31607 27.0587 8 26.2956 8 25.5C8 24.7044 8.31607 23.9413 8.87868 23.3787C9.44129 22.8161 10.2044 22.5 11 22.5C11.7956 22.5 12.5587 22.8161 13.1213 23.3787C13.6839 23.9413 14 24.7044 14 25.5ZM29 25.5C29 26.2956 28.6839 27.0587 28.1213 27.6213C27.5587 28.1839 26.7956 28.5 26 28.5C25.2044 28.5 24.4413 28.1839 23.8787 27.6213C23.3161 27.0587 23 26.2956 23 25.5C23 24.7044 23.3161 23.9413 23.8787 23.3787C24.4413 22.8161 25.2044 22.5 26 22.5C26.7956 22.5 27.5587 22.8161 28.1213 23.3787C28.6839 23.9413 29 24.7044 29 25.5Z"
                fill={color}
            />
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M4.37868 6.87868C4.94129 6.31607 5.70435 6 6.5 6H18.5C19.2957 6 20.0587 6.31607 20.6213 6.87868C21.1839 7.44129 21.5 8.20435 21.5 9H25.379C26.1745 9.00017 26.9377 9.31629 27.5002 9.87884C27.5001 9.87879 27.5002 9.87889 27.5002 9.87884L32.621 14.9997C33.1837 15.5622 33.4998 16.3251 33.5 17.1207V24C33.5 24.7956 33.1839 25.5587 32.6213 26.1213C32.0587 26.6839 31.2956 27 30.5 27H30.2426C30.021 27.6269 29.6611 28.2029 29.182 28.682C28.3381 29.5259 27.1935 30 26 30C24.8065 30 23.6619 29.5259 22.818 28.682C22.3389 28.2029 21.979 27.6269 21.7574 27H21.5C20.9688 27 20.4522 26.8591 20 26.5981C19.5478 26.8591 19.0312 27 18.5 27H15.2426C15.021 27.6269 14.6611 28.2029 14.182 28.682C13.3381 29.5259 12.1935 30 11 30C9.80653 30 8.66193 29.5259 7.81802 28.682C7.33891 28.2029 6.979 27.6269 6.75736 27H6.5C5.70435 27 4.94129 26.6839 4.37868 26.1213C3.81607 25.5587 3.5 24.7956 3.5 24V9C3.5 8.20435 3.81607 7.44129 4.37868 6.87868ZM9.5 25.5C9.5 25.8978 9.65804 26.2794 9.93934 26.5607C10.2206 26.842 10.6022 27 11 27C11.3978 27 11.7794 26.842 12.0607 26.5607C12.342 26.2794 12.5 25.8978 12.5 25.5C12.5 25.1022 12.342 24.7206 12.0607 24.4393C11.7794 24.158 11.3978 24 11 24C10.6022 24 10.2206 24.158 9.93934 24.4393C9.65804 24.7206 9.5 25.1022 9.5 25.5ZM15.2426 24H18.5V9L6.5 9V24H6.75736C6.979 23.3731 7.33891 22.7971 7.81802 22.318C8.66193 21.4741 9.80653 21 11 21C12.1935 21 13.3381 21.4741 14.182 22.318C14.6611 22.7971 15.021 23.3731 15.2426 24ZM21.5 24H21.7574C21.979 23.3731 22.3389 22.7971 22.818 22.318C23.6619 21.4741 24.8065 21 26 21C27.1935 21 28.3381 21.4741 29.182 22.318C29.6611 22.7971 30.021 23.3731 30.2426 24H30.5V17.1213L25.3788 12.0002L21.5 12V24ZM26 24C25.6022 24 25.2206 24.158 24.9393 24.4393C24.658 24.7206 24.5 25.1022 24.5 25.5C24.5 25.8978 24.658 26.2794 24.9393 26.5607C25.2206 26.842 25.6022 27 26 27C26.3978 27 26.7794 26.842 27.0607 26.5607C27.342 26.2794 27.5 25.8978 27.5 25.5C27.5 25.1022 27.342 24.7206 27.0607 24.4393C26.7794 24.158 26.3978 24 26 24Z"
                fill={color}
            />
        </Svg>
    );
};

export const ReturendIcon = ({ color }: IconProp) => {
    return (
        <Svg width="36" height="36" viewBox="0 0 36 36" fill="none">
            <Mask id="mask0_7768_6151" maskUnits="userSpaceOnUse" x="0" y="0" width="36" height="36">
                <Rect width="36" height="36" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_7768_6151)">
                <Path
                    d="M31.4875 12.0375V19.1824C30.583 18.6621 29.5796 18.2942 28.5125 18.1139V13.65V13.6283L28.4937 13.6392L19.4937 18.8517L19.4875 18.8409V18.8625V22.0195C18.5397 23.4463 17.9875 25.1587 17.9875 27C17.9875 29.0286 18.6578 30.9008 19.7889 32.4069L19.4938 32.5767L19.4937 32.5767C19.0205 32.8506 18.5227 32.9875 18 32.9875C17.4773 32.9875 16.9795 32.8506 16.5063 32.5767L16.5062 32.5767L6.00626 26.5392C5.53304 26.2652 5.16589 25.9042 4.90455 25.4562C4.64323 25.0082 4.5125 24.5104 4.5125 23.9625V12.0375C4.5125 11.4896 4.64323 10.9918 4.90455 10.5438C5.16589 10.0958 5.53304 9.73479 6.00626 9.46082L16.5062 3.42334L16.5063 3.42332C16.9795 3.14937 17.4773 3.0125 18 3.0125C18.5227 3.0125 19.0205 3.14937 19.4937 3.42332L19.4938 3.42334L29.9937 9.46082C30.467 9.73479 30.8341 10.0958 31.0955 10.5438C31.3568 10.9918 31.4875 11.4896 31.4875 12.0375ZM16.5125 18.8625H16.5249L16.5063 18.8517L7.50626 13.6392L7.4875 13.6283V13.65V23.925H7.47506L7.49374 23.9358L16.4937 29.1483L16.5125 29.1592V29.1375V18.8625ZM26.8937 11.1484L26.9126 11.1376L26.8938 11.1267L18.0063 5.98918L18.0125 5.97835L17.9938 5.98917L15.0688 7.67667L15.0499 7.68755L15.0688 7.69835L23.9938 12.7984L23.9878 12.8089L24.0062 12.7984L26.8937 11.1484ZM20.9312 14.5983L20.95 14.5875L20.9313 14.5767L12.0438 9.43918L12.05 9.42835L12.0313 9.43917L9.10625 11.1267L9.0875 11.1375L9.10624 11.1483L17.9937 16.2858L17.9875 16.2967L18.0062 16.2858L20.9312 14.5983ZM26.7375 25.5125H32.9875V28.4875H26.7375H26.7077L26.7286 28.5088L29.0824 30.8999L27 32.9823L21.0177 27L27 21.0177L29.0824 23.1001L26.7286 25.4912L26.7077 25.5125H26.7375Z"
                    fill={color}
                />
            </G>
        </Svg>
    );
};

export const ProfitGainIcon = ({ color }: IconProp) => {
    return (
        <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <Path
                d="M19.71 9.71L22 12V6H16L18.29 8.29L14.12 12.46C14.0275 12.5527 13.9176 12.6263 13.7966 12.6764C13.6757 12.7266 13.546 12.7524 13.415 12.7524C13.284 12.7524 13.1543 12.7266 13.0334 12.6764C12.9124 12.6263 12.8025 12.5527 12.71 12.46L11.54 11.29C10.9775 10.7282 10.215 10.4126 9.42 10.4126C8.625 10.4126 7.8625 10.7282 7.3 11.29L2 16.59L3.41 18L8.7 12.71C8.79251 12.6173 8.9024 12.5437 9.02338 12.4936C9.14435 12.4434 9.27403 12.4176 9.405 12.4176C9.53597 12.4176 9.66565 12.4434 9.78662 12.4936C9.9076 12.5437 10.0175 12.6173 10.11 12.71L11.28 13.88C11.8425 14.4418 12.605 14.7574 13.4 14.7574C14.195 14.7574 14.9575 14.4418 15.52 13.88L19.71 9.71Z"
                fill={color}
            />
        </Svg>
    );
};
export const ProfitLossIcon = ({ color }: IconProp) => {
    return (
        <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <Path
                d="M19.71 14.29L22 12V18H16L18.29 15.71L14.12 11.54C14.0275 11.4473 13.9176 11.3737 13.7966 11.3236C13.6757 11.2734 13.546 11.2476 13.415 11.2476C13.284 11.2476 13.1543 11.2734 13.0334 11.3236C12.9124 11.3737 12.8025 11.4473 12.71 11.54L11.54 12.71C10.9775 13.2718 10.215 13.5874 9.42 13.5874C8.625 13.5874 7.8625 13.2718 7.3 12.71L2 7.41L3.41 6L8.7 11.29C8.79251 11.3827 8.9024 11.4563 9.02338 11.5064C9.14435 11.5566 9.27403 11.5824 9.405 11.5824C9.53597 11.5824 9.66565 11.5566 9.78662 11.5064C9.9076 11.4563 10.0175 11.3827 10.11 11.29L11.28 10.12C11.8425 9.5582 12.605 9.24264 13.4 9.24264C14.195 9.24264 14.9575 9.5582 15.52 10.12L19.71 14.29Z"
                fill={color}
            />
        </Svg>
    );
};
export const MoneyIcon = ({ color }: IconProp) => {
    return (
        <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <Path
                d="M19 14V6C19 4.9 18.1 4 17 4H3C1.9 4 1 4.9 1 6V14C1 15.1 1.9 16 3 16H17C18.1 16 19 15.1 19 14ZM17 14H3V6H17V14ZM10 7C8.34 7 7 8.34 7 10C7 11.66 8.34 13 10 13C11.66 13 13 11.66 13 10C13 8.34 11.66 7 10 7ZM23 7V18C23 19.1 22.1 20 21 20H4V18H21V7H23Z"
                fill={color}
            />
        </Svg>
    );
};

export const TotalIcon = ({ color }: IconProp) => {
    return (
        <Svg width="36" height="36" viewBox="0 0 36 36" fill="none">
            <Mask id="mask0_7768_6521" maskUnits="userSpaceOnUse" x="0" y="0" width="36" height="36">
                <Rect width="36" height="36" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_7768_6521)">
                <Path
                    d="M24.975 19.5008L16.5 11.0258L24.975 2.55078L33.45 11.0258L24.975 19.5008ZM4.5 16.5008V4.50078H16.5V16.5008H4.5ZM19.5 31.5008V19.5008H31.5V31.5008H19.5ZM4.5 31.5008V19.5008H16.5V31.5008H4.5ZM7.5 13.5008H13.5V7.50078H7.5V13.5008ZM25.0125 15.3008L29.25 11.0633L25.0125 6.82578L20.775 11.0633L25.0125 15.3008ZM22.5 28.5008H28.5V22.5008H22.5V28.5008ZM7.5 28.5008H13.5V22.5008H7.5V28.5008Z"
                    fill={color}
                />
            </G>
        </Svg>
    );
};

export const AttemptIcon = ({ color }: IconProp) => {
    return (
        <Svg width="36" height="36" viewBox="0 0 36 36" fill="none">
            <Path
                d="M17.924 2.617C17.8741 2.49665 17.8011 2.38725 17.709 2.295L17.705 2.291C17.5179 2.1044 17.2643 1.99973 17 2H13C12.7348 2 12.4805 2.10536 12.2929 2.29289C12.1054 2.48043 12 2.73478 12 3C12 3.26522 12.1054 3.51957 12.2929 3.70711C12.4805 3.89464 12.7348 4 13 4H14.586L11.293 7.293C11.1109 7.4816 11.0101 7.73421 11.0124 7.9964C11.0146 8.2586 11.1198 8.50941 11.3052 8.69482C11.4906 8.88023 11.7414 8.9854 12.0036 8.98768C12.2658 8.98995 12.5184 8.88916 12.707 8.707L16 5.414V7C16 7.26522 16.1054 7.51957 16.2929 7.70711C16.4805 7.89464 16.7348 8 17 8C17.2653 8 17.5196 7.89464 17.7071 7.70711C17.8947 7.51957 18 7.26522 18 7V3C18.0002 2.86857 17.9744 2.73841 17.924 2.617V2.617Z"
                fill={color}
            />
            <Path
                d="M2 3C2 2.73478 2.10536 2.48043 2.29289 2.29289C2.48043 2.10536 2.73478 2 3 2H5.153C5.38971 2.00011 5.6187 2.08418 5.79924 2.23726C5.97979 2.39034 6.10018 2.6025 6.139 2.836L6.879 7.271C6.91436 7.48222 6.88097 7.69921 6.78376 7.89003C6.68655 8.08085 6.53065 8.23543 6.339 8.331L4.791 9.104C5.34611 10.4797 6.17283 11.7293 7.22178 12.7782C8.27072 13.8272 9.52035 14.6539 10.896 15.209L11.67 13.661C11.7655 13.4695 11.9199 13.3138 12.1106 13.2166C12.3012 13.1194 12.5179 13.0859 12.729 13.121L17.164 13.861C17.3975 13.8998 17.6097 14.0202 17.7627 14.2008C17.9158 14.3813 17.9999 14.6103 18 14.847V17C18 17.2652 17.8946 17.5196 17.7071 17.7071C17.5196 17.8946 17.2652 18 17 18H15C7.82 18 2 12.18 2 5V3Z"
                fill={color}
            />
        </Svg>
    );
};
export const ConfirmedIcon = ({ color }: IconProp) => {
    return (
        <Svg width="36" height="36" viewBox="0 0 36 36" fill="none">
            <Path
                d="M2 3C2 2.73478 2.10536 2.48043 2.29289 2.29289C2.48043 2.10536 2.73478 2 3 2H5.153C5.38971 2.00011 5.6187 2.08418 5.79924 2.23726C5.97979 2.39034 6.10018 2.6025 6.139 2.836L6.879 7.271C6.91436 7.48222 6.88097 7.69921 6.78376 7.89003C6.68655 8.08085 6.53065 8.23543 6.339 8.331L4.791 9.104C5.34611 10.4797 6.17283 11.7293 7.22178 12.7782C8.27072 13.8272 9.52035 14.6539 10.896 15.209L11.67 13.661C11.7655 13.4695 11.9199 13.3138 12.1106 13.2166C12.3012 13.1194 12.5179 13.0859 12.729 13.121L17.164 13.861C17.3975 13.8998 17.6097 14.0202 17.7627 14.2008C17.9158 14.3813 17.9999 14.6103 18 14.847V17C18 17.2652 17.8946 17.5196 17.7071 17.7071C17.5196 17.8946 17.2652 18 17 18H15C7.82 18 2 12.18 2 5V3Z"
                fill={color}
            />
            <Path
                d="M18.5364 2.55646C18.7239 2.74399 18.8292 2.9983 18.8292 3.26346C18.8292 3.52862 18.7239 3.78293 18.5364 3.97046L17.2434 5.26346C17.2434 5.26346 15.974 6.53291 14.5364 7.97046C14.4442 8.06597 14.3339 8.14215 14.2118 8.19456C14.0898 8.24697 13.9586 8.27456 13.8258 8.27571C13.6931 8.27686 13.5614 8.25156 13.4385 8.20128C13.3156 8.151 13.2039 8.07675 13.11 7.98285C13.0162 7.88896 12.8084 7.67726 12.8084 7.67726L11.0854 5.93439C10.9032 5.74578 10.8024 5.49318 10.8047 5.23099C10.807 4.96879 10.9122 4.71798 11.0976 4.53257C11.283 4.34716 11.617 4.21914 11.8792 4.21686C12.1414 4.21458 12.3108 4.33823 12.4994 4.52039L13.7924 5.81339L17.1224 2.55646C17.31 2.36899 17.5643 2.26367 17.8294 2.26367C18.0946 2.26367 18.3489 2.36899 18.5364 2.55646Z"
                fill={color}
            />
        </Svg>
    );
};
export const CreatedIcon = ({ color }: IconProp) => {
    return (
        <Svg width="36" height="36" viewBox="0 0 36 36" fill="none">
            <Path
                d="M2 3C2 2.73478 2.10536 2.48043 2.29289 2.29289C2.48043 2.10536 2.73478 2 3 2H5.153C5.38971 2.00011 5.6187 2.08418 5.79924 2.23726C5.97979 2.39034 6.10018 2.6025 6.139 2.836L6.879 7.271C6.91436 7.48222 6.88097 7.69921 6.78376 7.89003C6.68655 8.08085 6.53065 8.23543 6.339 8.331L4.791 9.104C5.34611 10.4797 6.17283 11.7293 7.22178 12.7782C8.27072 13.8272 9.52035 14.6539 10.896 15.209L11.67 13.661C11.7655 13.4695 11.9199 13.3138 12.1106 13.2166C12.3012 13.1194 12.5179 13.0859 12.729 13.121L17.164 13.861C17.3975 13.8998 17.6097 14.0202 17.7627 14.2008C17.9158 14.3813 17.9999 14.6103 18 14.847V17C18 17.2652 17.8946 17.5196 17.7071 17.7071C17.5196 17.8946 17.2652 18 17 18H15C7.82 18 2 12.18 2 5V3Z"
                fill={color}
            />
            <Path
                d="M13.9937 2.16679C14.2589 2.16683 14.5132 2.27218 14.7007 2.45968C14.8882 2.64718 14.9935 2.90147 14.9936 3.16664L14.9936 4.99522L16.8222 4.99522C17.0843 4.99977 17.3342 5.10712 17.518 5.29413C17.7018 5.48114 17.8048 5.73286 17.8048 5.99507C17.8048 6.25727 17.7018 6.50899 17.518 6.696C17.3342 6.88301 17.0843 6.99036 16.8222 6.99491L14.9936 6.99491L14.9936 8.82349C14.9959 8.95626 14.9717 9.08815 14.9225 9.21148C14.8733 9.33481 14.8 9.4471 14.707 9.54181C14.6139 9.63651 14.5029 9.71173 14.3804 9.76308C14.258 9.81443 14.1265 9.84087 13.9937 9.84087C13.8609 9.84087 13.7295 9.81443 13.607 9.76308C13.4846 9.71173 13.3736 9.63651 13.2805 9.54181C13.1874 9.4471 13.1142 9.33481 13.0649 9.21148C13.0157 9.08815 12.9916 8.95626 12.9939 8.82349L12.9939 6.99491L11.1653 6.99491C10.9031 6.99036 10.6532 6.88301 10.4695 6.696C10.2857 6.50899 10.1827 6.25727 10.1827 5.99507C10.1827 5.73286 10.2857 5.48114 10.4695 5.29413C10.6532 5.10712 10.9031 4.99977 11.1653 4.99522L12.9939 4.99522L12.9939 3.16664C12.9939 2.90147 13.0993 2.64718 13.2868 2.45968C13.4743 2.27218 13.7286 2.16683 13.9937 2.16679Z"
                fill={color}
            />
        </Svg>
    );
};
export const RejectedIcon = ({ color }: IconProp) => {
    return (
        <Svg width="36" height="36" viewBox="0 0 36 36" fill="none">
            <Path
                d="M2 3C2 2.73478 2.10536 2.48043 2.29289 2.29289C2.48043 2.10536 2.73478 2 3 2H5.153C5.38971 2.00011 5.6187 2.08418 5.79924 2.23726C5.97979 2.39034 6.10018 2.6025 6.139 2.836L6.879 7.271C6.91436 7.48222 6.88097 7.69921 6.78376 7.89003C6.68655 8.08085 6.53065 8.23543 6.339 8.331L4.791 9.104C5.34611 10.4797 6.17283 11.7293 7.22178 12.7782C8.27072 13.8272 9.52035 14.6539 10.896 15.209L11.67 13.661C11.7655 13.4695 11.9199 13.3138 12.1106 13.2166C12.3012 13.1194 12.5179 13.0859 12.729 13.121L17.164 13.861C17.3975 13.8998 17.6097 14.0202 17.7627 14.2008C17.9158 14.3813 17.9999 14.6103 18 14.847V17C18 17.2652 17.8946 17.5196 17.7071 17.7071C17.5196 17.8946 17.2652 18 17 18H15C7.82 18 2 12.18 2 5V3Z"
                fill={color}
            />
            <Path
                d="M16.707 3.29279C16.8944 3.48031 16.9997 3.73462 16.9997 3.99979C16.9997 4.26495 16.8944 4.51926 16.707 4.70679L15.414 5.99979L16.707 7.29279C16.8891 7.48139 16.9899 7.73399 16.9876 7.99619C16.9854 8.25839 16.8802 8.5092 16.6948 8.69461C16.5094 8.88001 16.2586 8.98518 15.9964 8.98746C15.7342 8.98974 15.4816 8.88894 15.293 8.70679L14 7.41379L12.707 8.70679C12.6147 8.8023 12.5044 8.87848 12.3824 8.93089C12.2604 8.9833 12.1291 9.01088 11.9964 9.01204C11.8636 9.01319 11.7319 8.98789 11.609 8.93761C11.4861 8.88733 11.3745 8.81307 11.2806 8.71918C11.1867 8.62529 11.1124 8.51364 11.0621 8.39074C11.0119 8.26784 10.9866 8.13617 10.9877 8.00339C10.9889 7.87061 11.0164 7.73939 11.0689 7.61738C11.1213 7.49538 11.1974 7.38503 11.293 7.29279L12.586 5.99979L11.293 4.70679C11.1108 4.51818 11.01 4.26558 11.0123 4.00339C11.0146 3.74119 11.1197 3.49038 11.3051 3.30497C11.4905 3.11956 11.7414 3.01439 12.0036 3.01211C12.2658 3.00983 12.5184 3.11063 12.707 3.29279L14 4.58579L15.293 3.29279C15.4805 3.10532 15.7348 3 16 3C16.2651 3 16.5194 3.10532 16.707 3.29279Z"
                fill={color}
            />
        </Svg>
    );
};
