import React from "react";
import Svg, { Path } from "react-native-svg";

// Type definitions for icon props
export interface WarningIconProps {
    size: number;
    color: string;
}

export interface DeleteIconProps {
    size: number;
    color: string;
}

export const WarningIcon: React.FC<WarningIconProps> = ({ size, color }) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        <Path
            d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1-7v2h2v-2h-2zm0-8v6h2V7h-2z"
            fill={color}
        />
    </Svg>
);

export const DeleteIcon: React.FC<DeleteIconProps> = ({ size, color }) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        <Path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill={color} />
    </Svg>
);
