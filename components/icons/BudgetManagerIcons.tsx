import { Path, Svg } from "react-native-svg";
type IconProps = {
    size?: number;
    color: string;
};
export const EditIcon = ({ color, size }: { color?: string; size?: number }) => {
    return (
        <Svg width={size ? size : "30"} height={size ? size : "30"} viewBox="0 0 30 30" fill="none">
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M24.2541 5.06183C24.1004 4.99898 23.9358 4.96736 23.7699 4.9688C23.6039 4.97024 23.4399 5.00472 23.2874 5.07023C23.1348 5.13574 22.9969 5.23097 22.8816 5.35036L22.8664 5.36585L12.5 15.7322V17.4995H14.2672L24.6336 7.13309L24.6491 7.11786C24.7685 7.00255 24.8637 6.86462 24.9292 6.71212C24.9947 6.55961 25.0292 6.39559 25.0307 6.22961C25.0321 6.06364 25.0005 5.89904 24.9376 5.74542C24.8748 5.5918 24.782 5.45223 24.6646 5.33487C24.5472 5.2175 24.4077 5.12468 24.2541 5.06183ZM23.7481 2.46889C24.2461 2.46456 24.7399 2.55945 25.2007 2.748C25.6616 2.93655 26.0803 3.215 26.4324 3.5671C26.7845 3.9192 27.0629 4.33789 27.2515 4.79875C27.44 5.25961 27.5349 5.75341 27.5306 6.25134C27.5263 6.74926 27.4228 7.24133 27.2263 7.69885C27.0313 8.15268 26.7486 8.56349 26.3945 8.90772L15.6689 19.6334C15.4345 19.8678 15.1165 19.9995 14.785 19.9995H11.25C10.5596 19.9995 10 19.4398 10 18.7495V15.2145C10 14.8829 10.1317 14.565 10.3661 14.3306L21.0917 3.60498C21.436 3.25085 21.8468 2.96816 22.3006 2.7732C22.7581 2.57667 23.2502 2.47322 23.7481 2.46889ZM4.84835 6.09782C5.55161 5.39456 6.50544 4.99947 7.5 4.99947H13.75C14.4404 4.99947 15 5.55911 15 6.24947C15 6.93983 14.4404 7.49947 13.75 7.49947H7.5C7.16848 7.49947 6.85054 7.63117 6.61612 7.86559C6.3817 8.10001 6.25 8.41795 6.25 8.74947V22.4995C6.25 22.831 6.3817 23.1489 6.61612 23.3834C6.85054 23.6178 7.16848 23.7495 7.5 23.7495H21.25C21.5815 23.7495 21.8995 23.6178 22.1339 23.3834C22.3683 23.1489 22.5 22.831 22.5 22.4995V16.2495C22.5 15.5591 23.0596 14.9995 23.75 14.9995C24.4404 14.9995 25 15.5591 25 16.2495V22.4995C25 23.494 24.6049 24.4479 23.9016 25.1511C23.1984 25.8544 22.2446 26.2495 21.25 26.2495H7.5C6.50544 26.2495 5.55161 25.8544 4.84835 25.1511C4.14509 24.4479 3.75 23.494 3.75 22.4995V8.74947C3.75 7.75491 4.14509 6.80108 4.84835 6.09782Z"
                fill={color ? color : "#6A7AFF"}
            />
        </Svg>
    );
};

export const RevenueIcon = ({ color }: IconProps) => {
    return (
        <Svg width="25" height="24" viewBox="0 0 25 24" fill="none">
            <Path
                d="M20.46 9.71L22.75 12V6H16.75L19.04 8.29L14.87 12.46C14.7775 12.5527 14.6676 12.6263 14.5466 12.6764C14.4257 12.7266 14.296 12.7524 14.165 12.7524C14.034 12.7524 13.9043 12.7266 13.7834 12.6764C13.6624 12.6263 13.5525 12.5527 13.46 12.46L12.29 11.29C11.7275 10.7282 10.965 10.4126 10.17 10.4126C9.375 10.4126 8.6125 10.7282 8.05 11.29L2.75 16.59L4.16 18L9.45 12.71C9.54251 12.6173 9.6524 12.5437 9.77338 12.4936C9.89435 12.4434 10.024 12.4176 10.155 12.4176C10.286 12.4176 10.4157 12.4434 10.5366 12.4936C10.6576 12.5437 10.7675 12.6173 10.86 12.71L12.03 13.88C12.5925 14.4418 13.355 14.7574 14.15 14.7574C14.945 14.7574 15.7075 14.4418 16.27 13.88L20.46 9.71Z"
                fill={color}
            />
        </Svg>
    );
};

export const ExpenseIcon = ({ color }: IconProps) => {
    return (
        <Svg width="25" height="24" viewBox="0 0 25 24" fill="none">
            <Path
                d="M19.96 14.29L22.25 12V18H16.25L18.54 15.71L14.37 11.54C14.2775 11.4473 14.1676 11.3737 14.0466 11.3236C13.9257 11.2734 13.796 11.2476 13.665 11.2476C13.534 11.2476 13.4043 11.2734 13.2834 11.3236C13.1624 11.3737 13.0525 11.4473 12.96 11.54L11.79 12.71C11.2275 13.2718 10.465 13.5874 9.67 13.5874C8.875 13.5874 8.1125 13.2718 7.55 12.71L2.25 7.41L3.66 6L8.95 11.29C9.04251 11.3827 9.1524 11.4563 9.27338 11.5064C9.39435 11.5566 9.52403 11.5824 9.655 11.5824C9.78597 11.5824 9.91565 11.5566 10.0366 11.5064C10.1576 11.4563 10.2675 11.3827 10.36 11.29L11.53 10.12C12.0925 9.5582 12.855 9.24264 13.65 9.24264C14.445 9.24264 15.2075 9.5582 15.77 10.12L19.96 14.29Z"
                fill={color}
            />
        </Svg>
    );
};

export const TabsRowExpandIcon = () => (
    <Svg width="15" height="15" viewBox="0 0 15 15" fill="none">
        <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M5.18306 2.68306C5.42714 2.43898 5.82286 2.43898 6.06694 2.68306L10.4419 7.05806C10.686 7.30214 10.686 7.69786 10.4419 7.94194L6.06694 12.3169C5.82286 12.561 5.42714 12.561 5.18306 12.3169C4.93898 12.0729 4.93898 11.6771 5.18306 11.4331L9.11612 7.5L5.18306 3.56694C4.93898 3.32286 4.93898 2.92714 5.18306 2.68306Z"
            fill="#682EB0"
        />
    </Svg>
);

export const TabsContentRowAdd = () => {
    return (
        <Svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.00004 3.33398C8.36823 3.33398 8.66671 3.63246 8.66671 4.00065V7.33398H12C12.3682 7.33398 12.6667 7.63246 12.6667 8.00065C12.6667 8.36884 12.3682 8.66732 12 8.66732H8.66671V12.0007C8.66671 12.3688 8.36823 12.6673 8.00004 12.6673C7.63185 12.6673 7.33337 12.3688 7.33337 12.0007V8.66732H4.00004C3.63185 8.66732 3.33337 8.36884 3.33337 8.00065C3.33337 7.63246 3.63185 7.33398 4.00004 7.33398H7.33337V4.00065C7.33337 3.63246 7.63185 3.33398 8.00004 3.33398Z"
                fill="#682EB0"
            />
        </Svg>
    );
};

export const AddIcon = ({ color, size }: { color?: string; size?: string }) => {
    return (
        <Svg width={size ? size : "16"} height={size ? size : "16"} viewBox="0 0 16 16" fill="none">
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.00004 3.33398C8.36823 3.33398 8.66671 3.63246 8.66671 4.00065V7.33398H12C12.3682 7.33398 12.6667 7.63246 12.6667 8.00065C12.6667 8.36884 12.3682 8.66732 12 8.66732H8.66671V12.0007C8.66671 12.3688 8.36823 12.6673 8.00004 12.6673C7.63185 12.6673 7.33337 12.3688 7.33337 12.0007V8.66732H4.00004C3.63185 8.66732 3.33337 8.36884 3.33337 8.00065C3.33337 7.63246 3.63185 7.33398 4.00004 7.33398H7.33337V4.00065C7.33337 3.63246 7.63185 3.33398 8.00004 3.33398Z"
                fill={color ? color : "#682EB0"}
            />
        </Svg>
    );
};

export const TabsContentCellEditIcon = ({ size }: { size: number }) => {
    return (
        <Svg width={size} height={size} viewBox="0 0 16 16" fill="none">
            <Path
                d="M12.6966 4.9349C12.9486 4.6829 13.0873 4.34823 13.0873 3.99223C13.0873 3.63623 12.9486 3.30156 12.6966 3.04956L11.6393 1.99223C11.3873 1.74023 11.0526 1.60156 10.6966 1.60156C10.3406 1.60156 10.006 1.74023 9.75463 1.99156L2.66663 9.05756V12.0009H5.60863L12.6966 4.9349ZM10.6966 2.9349L11.7546 3.99156L10.6946 5.04756L9.63729 3.9909L10.6966 2.9349ZM3.99996 10.6676V9.6109L8.69329 4.93223L9.75063 5.98956L5.05796 10.6676H3.99996ZM2.66663 13.3342H13.3333V14.6676H2.66663V13.3342Z"
                fill="#682EB0"
            />
        </Svg>
    );
};

export const TabsContentCellDelete = () => {
    return (
        <Svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M7.15482 2.15417C7.46738 1.84161 7.89131 1.66602 8.33333 1.66602H11.6667C12.1087 1.66602 12.5326 1.84161 12.8452 2.15417C13.1577 2.46673 13.3333 2.89065 13.3333 3.33268V4.99935H15.8247C15.8299 4.9993 15.835 4.9993 15.8402 4.99935H16.6667C17.1269 4.99935 17.5 5.37245 17.5 5.83268C17.5 6.29292 17.1269 6.66602 16.6667 6.66602H16.6093L15.9421 16.0102C15.9421 16.0102 15.9421 16.0101 15.9421 16.0102C15.8971 16.6408 15.6149 17.2312 15.1522 17.6621C14.6895 18.0931 14.0806 18.3327 13.4483 18.3327H6.55169C5.91936 18.3327 5.31052 18.0931 4.84779 17.6621C4.38511 17.2312 4.10289 16.641 4.05795 16.0104C4.05795 16.0103 4.05795 16.0104 4.05795 16.0104L3.39072 6.66602H3.33333C2.8731 6.66602 2.5 6.29292 2.5 5.83268C2.5 5.37245 2.8731 4.99935 3.33333 4.99935H4.15979C4.16496 4.9993 4.17012 4.9993 4.17527 4.99935H6.66667V3.33268C6.66667 2.89065 6.84226 2.46673 7.15482 2.15417ZM5.06163 6.66602L5.72038 15.8917C5.73535 16.1019 5.82944 16.2989 5.98368 16.4425C6.13792 16.5862 6.34087 16.666 6.55167 16.666H13.4484C13.6591 16.666 13.8621 16.5862 14.0163 16.4425C14.1706 16.2989 14.2646 16.1021 14.2796 15.8919L14.9384 6.66602H5.06163ZM11.6667 4.99935H8.33333V3.33268H11.6667V4.99935ZM8.33333 8.33268C8.79357 8.33268 9.16667 8.70578 9.16667 9.16602V14.166C9.16667 14.6263 8.79357 14.9993 8.33333 14.9993C7.8731 14.9993 7.5 14.6263 7.5 14.166V9.16602C7.5 8.70578 7.8731 8.33268 8.33333 8.33268ZM11.6667 8.33268C12.1269 8.33268 12.5 8.70578 12.5 9.16602V14.166C12.5 14.6263 12.1269 14.9993 11.6667 14.9993C11.2064 14.9993 10.8333 14.6263 10.8333 14.166V9.16602C10.8333 8.70578 11.2064 8.33268 11.6667 8.33268Z"
                fill="#E53E3E"
            />
        </Svg>
    );
};

export const CalendarDayIcon = ({ color }: IconProps) => {
    return (
        <Svg width="25" height="24" viewBox="0 0 25 24" fill="none">
            <Path
                d="M19.5 3H18.5V1H16.5V3H8.5V1H6.5V3H5.5C4.39 3 3.51 3.9 3.51 5L3.5 19C3.5 19.5304 3.71071 20.0391 4.08579 20.4142C4.46086 20.7893 4.96957 21 5.5 21H19.5C20.6 21 21.5 20.1 21.5 19V5C21.5 3.9 20.6 3 19.5 3ZM19.5 19H5.5V8H19.5V19ZM7.5 10H12.5V15H7.5V10Z"
                fill={color}
            />
        </Svg>
    );
};

export const ViewAllIcon = ({ color, size }: { color?: string; size?: number }) => {
    return (
        <Svg
            height={size ? size : 30}
            width={size ? size : 30}
            viewBox="0 -960 960 960"
            fill={color ? color : "#6A7AFF"}
        >
            <Path d="M280-600v-80h560v80H280Zm0 160v-80h560v80H280Zm0 160v-80h560v80H280ZM160-600q-17 0-28.5-11.5T120-640q0-17 11.5-28.5T160-680q17 0 28.5 11.5T200-640q0 17-11.5 28.5T160-600Zm0 160q-17 0-28.5-11.5T120-480q0-17 11.5-28.5T160-520q17 0 28.5 11.5T200-480q0 17-11.5 28.5T160-440Zm0 160q-17 0-28.5-11.5T120-320q0-17 11.5-28.5T160-360q17 0 28.5 11.5T200-320q0 17-11.5 28.5T160-280Z" />
        </Svg>
    );
};

export const UncheckedIcon = () => {
    return (
        <Svg height="24" viewBox="0 -960 960 960" width="24">
            <Path d="M480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z" />
        </Svg>
    );
};

export const CheckedIcon = () => {
    return (
        <Svg height="24" viewBox="0 -960 960 960" width="24">
            <Path d="M480-280q83 0 141.5-58.5T680-480q0-83-58.5-141.5T480-680q-83 0-141.5 58.5T280-480q0 83 58.5 141.5T480-280Zm0 200q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z" />
        </Svg>
    );
};
