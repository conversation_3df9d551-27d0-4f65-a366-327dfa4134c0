import { Path, Svg } from "react-native-svg";

type IconProps = {
    size?: number;
    color?: string;
};
export const DolarIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width={`${size}px`} height={`${size}px`} viewBox={`0 0 20 20`} fill="none">
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M9.99996 3.33366C8.23185 3.33366 6.53616 4.03604 5.28591 5.28628C4.03567 6.53652 3.33329 8.23222 3.33329 10.0003C3.33329 10.8758 3.50573 11.7427 3.84076 12.5515C4.17579 13.3604 4.66686 14.0953 5.28591 14.7144C5.90497 15.3334 6.6399 15.8245 7.44874 16.1595C8.25757 16.4946 9.12448 16.667 9.99996 16.667C10.8754 16.667 11.7423 16.4946 12.5512 16.1595C13.36 15.8245 14.0949 15.3334 14.714 14.7144C15.3331 14.0953 15.8241 13.3604 16.1592 12.5515C16.4942 11.7427 16.6666 10.8758 16.6666 10.0003C16.6666 8.23222 15.9642 6.53652 14.714 5.28628C13.4638 4.03604 11.7681 3.33366 9.99996 3.33366ZM4.1074 4.10777C5.67021 2.54497 7.78982 1.66699 9.99996 1.66699C12.2101 1.66699 14.3297 2.54497 15.8925 4.10777C17.4553 5.67057 18.3333 7.79019 18.3333 10.0003C18.3333 11.0947 18.1177 12.1783 17.699 13.1894C17.2802 14.2004 16.6663 15.1191 15.8925 15.8929C15.1187 16.6667 14.2 17.2805 13.189 17.6993C12.1779 18.1181 11.0943 18.3337 9.99996 18.3337C8.90561 18.3337 7.82198 18.1181 6.81093 17.6993C5.79988 17.2805 4.88122 16.6667 4.1074 15.8929C3.33358 15.1191 2.71975 14.2004 2.30096 13.1894C1.88217 12.1783 1.66663 11.0947 1.66663 10.0003C1.66663 7.79019 2.5446 5.67057 4.1074 4.10777ZM9.99996 5.00033C10.4602 5.00033 10.8333 5.37342 10.8333 5.83366V5.91063C11.6169 6.05792 12.3262 6.41381 12.7952 6.95411C13.0968 7.30169 13.0596 7.82802 12.712 8.12968C12.3644 8.43135 11.8381 8.39413 11.5364 8.04654C11.4009 7.89039 11.1592 7.73172 10.8333 7.62594V9.24362C11.3513 9.34086 11.8304 9.52858 12.23 9.79495C12.8315 10.1959 13.3333 10.8413 13.3333 11.667C13.3333 12.4926 12.8315 13.1381 12.23 13.539C11.8304 13.8054 11.3513 13.9931 10.8333 14.0904V14.167C10.8333 14.6272 10.4602 15.0003 9.99996 15.0003C9.53972 15.0003 9.16663 14.6272 9.16663 14.167V14.09C8.38306 13.9427 7.67369 13.5868 7.20477 13.0465C6.9031 12.699 6.94032 12.1726 7.28791 11.871C7.6355 11.5693 8.16182 11.6065 8.46349 11.9541C8.59901 12.1103 8.84072 12.2689 9.16663 12.3747V10.757C8.64864 10.6598 8.16953 10.4721 7.76993 10.2057C7.16842 9.80474 6.66663 9.15931 6.66663 8.33366C6.66663 7.50801 7.16842 6.86258 7.76993 6.46161C8.16953 6.19524 8.64864 6.00752 9.16663 5.91029V5.83366C9.16663 5.37342 9.53972 5.00033 9.99996 5.00033ZM9.16663 7.62609C8.98273 7.68557 8.82336 7.76242 8.69436 7.84841C8.39109 8.05057 8.33329 8.23847 8.33329 8.33366C8.33329 8.42885 8.39109 8.61675 8.69436 8.81891C8.82336 8.90489 8.98273 8.98175 9.16663 9.04123V7.62609ZM10.8333 10.9594V12.3746C11.0172 12.3151 11.1766 12.2382 11.3056 12.1522C11.6088 11.9501 11.6666 11.7622 11.6666 11.667C11.6666 11.5718 11.6088 11.3839 11.3056 11.1817C11.1766 11.0958 11.0172 11.0189 10.8333 10.9594Z"
                fill={color}
            />
        </Svg>
    );
};
export const CartIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width={`${size}px`} height={`${size}px`} viewBox="0 0 20 20" fill="none">
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M1.66675 2.50033C1.66675 2.04009 2.03984 1.66699 2.50008 1.66699H4.16675C4.56398 1.66699 4.906 1.94738 4.9839 2.3369L5.18325 3.33366H17.5001C17.7889 3.33366 18.0571 3.4832 18.209 3.72888C18.3608 3.97456 18.3746 4.28135 18.2454 4.53967L14.9121 11.2063C14.7709 11.4887 14.4824 11.667 14.1667 11.667H6.17859L4.51193 13.3337H14.1667C14.8298 13.3337 15.4657 13.5971 15.9345 14.0659C16.4034 14.5347 16.6667 15.1706 16.6667 15.8337C16.6667 16.4967 16.4034 17.1326 15.9345 17.6014C15.4657 18.0703 14.8298 18.3337 14.1667 18.3337C13.5037 18.3337 12.8678 18.0703 12.399 17.6014C11.9301 17.1326 11.6667 16.4967 11.6667 15.8337C11.6667 15.547 11.716 15.2655 11.8097 15.0003H8.19044C8.2842 15.2655 8.33342 15.547 8.33342 15.8337C8.33342 16.4967 8.07002 17.1326 7.60118 17.6014C7.13234 18.0703 6.49646 18.3337 5.83342 18.3337C5.17037 18.3337 4.53449 18.0703 4.06565 17.6014C3.59681 17.1326 3.33341 16.4967 3.33341 15.8337C3.33341 15.4469 3.42303 15.0694 3.59075 14.7289C2.7743 14.1871 2.53362 12.9549 3.33333 12.1552L4.9288 10.5598L3.68631 4.34729C3.68397 4.3367 3.68183 4.32603 3.6799 4.31529L3.48358 3.33366H2.50008C2.03984 3.33366 1.66675 2.96056 1.66675 2.50033ZM5.51659 5.00033L6.51659 10.0003H13.6517L16.1517 5.00033H5.51659ZM5.83342 15.0003C5.6124 15.0003 5.40044 15.0881 5.24416 15.2444C5.08788 15.4007 5.00008 15.6126 5.00008 15.8337C5.00008 16.0547 5.08788 16.2666 5.24416 16.4229C5.40044 16.5792 5.6124 16.667 5.83342 16.667C6.05443 16.667 6.26639 16.5792 6.42267 16.4229C6.57895 16.2666 6.66675 16.0547 6.66675 15.8337C6.66675 15.6126 6.57895 15.4007 6.42267 15.2444C6.26639 15.0881 6.05443 15.0003 5.83342 15.0003ZM14.1667 15.0003C13.9457 15.0003 13.7338 15.0881 13.5775 15.2444C13.4212 15.4007 13.3334 15.6126 13.3334 15.8337C13.3334 16.0547 13.4212 16.2666 13.5775 16.4229C13.7338 16.5792 13.9457 16.667 14.1667 16.667C14.3878 16.667 14.5997 16.5792 14.756 16.4229C14.9123 16.2666 15.0001 16.0547 15.0001 15.8337C15.0001 15.6126 14.9123 15.4007 14.756 15.2444C14.5997 15.0881 14.3878 15.0003 14.1667 15.0003Z"
                fill={color}
            />
        </Svg>
    );
};

export const StatsIcon = ({ size, color }: IconProps) => {
    return (
        //todo: add the notifaction
        <Svg width={size} height={size} viewBox="0 0 20 20" fill="none">
            <Path
                d="M4.0625 19.375H2.8125C2.56386 19.375 2.3254 19.2762 2.14959 19.1004C1.97377 18.9246 1.875 18.6861 1.875 18.4375V12.8125C1.875 12.5639 1.97377 12.3254 2.14959 12.1496C2.3254 11.9738 2.56386 11.875 2.8125 11.875H4.0625C4.31114 11.875 4.5496 11.9738 4.72541 12.1496C4.90123 12.3254 5 12.5639 5 12.8125V18.4375C5 18.6861 4.90123 18.9246 4.72541 19.1004C4.5496 19.2762 4.31114 19.375 4.0625 19.375ZM12.8125 19.375H11.5625C11.3139 19.375 11.0754 19.2762 10.8996 19.1004C10.7238 18.9246 10.625 18.6861 10.625 18.4375V9.0625C10.625 8.81386 10.7238 8.5754 10.8996 8.39959C11.0754 8.22377 11.3139 8.125 11.5625 8.125H12.8125C13.0611 8.125 13.2996 8.22377 13.4754 8.39959C13.6512 8.5754 13.75 8.81386 13.75 9.0625V18.4375C13.75 18.6861 13.6512 18.9246 13.4754 19.1004C13.2996 19.2762 13.0611 19.375 12.8125 19.375ZM17.1875 19.375H15.9375C15.6889 19.375 15.4504 19.2762 15.2746 19.1004C15.0988 18.9246 15 18.6861 15 18.4375V4.6875C15 4.43886 15.0988 4.2004 15.2746 4.02459C15.4504 3.84877 15.6889 3.75 15.9375 3.75H17.1875C17.4361 3.75 17.6746 3.84877 17.8504 4.02459C18.0262 4.2004 18.125 4.43886 18.125 4.6875V18.4375C18.125 18.6861 18.0262 18.9246 17.8504 19.1004C17.6746 19.2762 17.4361 19.375 17.1875 19.375ZM8.4375 19.375H7.1875C6.93886 19.375 6.7004 19.2762 6.52459 19.1004C6.34877 18.9246 6.25 18.6861 6.25 18.4375V1.5625C6.25 1.31386 6.34877 1.0754 6.52459 0.899587C6.7004 0.723772 6.93886 0.625 7.1875 0.625H8.4375C8.68614 0.625 8.9246 0.723772 9.10041 0.899587C9.27623 1.0754 9.375 1.31386 9.375 1.5625V18.4375C9.375 18.6861 9.27623 18.9246 9.10041 19.1004C8.9246 19.2762 8.68614 19.375 8.4375 19.375Z"
                fill={color}
            />
        </Svg>
    );
};

export const NotifIcon = ({ size, color }: IconProps) => {
    return (
        <Svg width={`${size}px`} height={`${size}px`} viewBox="0 0 20 20" fill="none">
            <Path
                d="M3.33325 15.8332V14.1665H4.99992V8.33317C4.99992 7.18039 5.34714 6.15609 6.04159 5.26025C6.73603 4.36442 7.63881 3.77762 8.74992 3.49984V2.9165C8.74992 2.56928 8.87145 2.27414 9.1145 2.03109C9.35756 1.78803 9.6527 1.6665 9.99992 1.6665C10.3471 1.6665 10.6423 1.78803 10.8853 2.03109C11.1284 2.27414 11.2499 2.56928 11.2499 2.9165V3.49984C12.361 3.77762 13.2638 4.36442 13.9583 5.26025C14.6527 6.15609 14.9999 7.18039 14.9999 8.33317V14.1665H16.6666V15.8332H3.33325ZM9.99992 18.3332C9.54158 18.3332 9.14922 18.17 8.82284 17.8436C8.49645 17.5172 8.33325 17.1248 8.33325 16.6665H11.6666C11.6666 17.1248 11.5034 17.5172 11.177 17.8436C10.8506 18.17 10.4583 18.3332 9.99992 18.3332ZM6.66659 14.1665H13.3333V8.33317C13.3333 7.4165 13.0069 6.63178 12.3541 5.979C11.7013 5.32623 10.9166 4.99984 9.99992 4.99984C9.08325 4.99984 8.29853 5.32623 7.64575 5.979C6.99297 6.63178 6.66659 7.4165 6.66659 8.33317V14.1665Z"
                fill={color}
            />
        </Svg>
    );
};

export const BackIcon = () => {
    return (
        <Svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M13.0893 3.57709C13.4147 3.90252 13.4147 4.43016 13.0893 4.7556L7.84522 9.99967L13.0893 15.2438C13.4147 15.5692 13.4147 16.0968 13.0893 16.4223C12.7639 16.7477 12.2362 16.7477 11.9108 16.4223L6.07745 10.5889C5.75201 10.2635 5.75201 9.73586 6.07745 9.41042L11.9108 3.57709C12.2362 3.25165 12.7639 3.25165 13.0893 3.57709Z"
                fill="#2A1246"
            />
        </Svg>
    );
};

export const CloseIcon = ({ color }: IconProps) => {
    return (
        <Svg height="24px" viewBox="0 -960 960 960" width="24px" fill={color}>
            <Path d="M480-416.35 287.83-224.17Q275.15-211.5 256-211.5t-31.83-12.67Q211.5-236.85 211.5-256t12.67-31.83L416.35-480 224.17-672.17Q211.5-684.85 211.5-704t12.67-31.83Q236.85-748.5 256-748.5t31.83 12.67L480-543.65l192.17-192.18Q684.85-748.5 704-748.5t31.83 12.67Q748.5-723.15 748.5-704t-12.67 31.83L543.65-480l192.18 192.17Q748.5-275.15 748.5-256t-12.67 31.83Q723.15-211.5 704-211.5t-31.83-12.67L480-416.35Z" />
        </Svg>
    );
};
