import { Path, Svg } from "react-native-svg";
import colors from "@styles/colors";
import React from "react";
import { IconProp } from "@components/stats/Props";

export const arrowSvg = () => {
    return (
        <Svg height="24px" viewBox="0 -960 960 960" width="24px" fill={colors.primary["500"]}>
            <Path d="M440-800v487L216-537l-56 57 320 320 320-320-56-57-224 224v-487h-80Z" />
        </Svg>
    );
};

export const phoneIconSvg = () => {
    return (
        <Svg height="24px" viewBox="0 -960 960 960" width="24px" fill={colors.white}>
            <Path d="M798-120q-125 0-247-54.5T329-329Q229-429 174.5-551T120-798q0-18 12-30t30-12h162q14 0 25 9.5t13 22.5l26 140q2 16-1 27t-11 19l-97 98q20 37 47.5 71.5T387-386q31 31 65 57.5t72 48.5l94-94q9-9 23.5-13.5T670-390l138 28q14 4 23 14.5t9 23.5v162q0 18-12 30t-30 12ZM241-600l66-66-17-94h-89q5 41 14 81t26 79Zm358 358q39 17 79.5 27t81.5 13v-88l-94-19-67 67ZM241-600Zm358 358Z" />
        </Svg>
    );
};

export const editIconSvg = () => {
    return (
        <Svg height="30px" viewBox="0 -960 960 960" width="30px" fill={colors.white}>
            <Path d="M200-200h57l391-391-57-57-391 391v57Zm-80 80v-170l528-527q12-11 26.5-17t30.5-6q16 0 31 6t26 18l55 56q12 11 17.5 26t5.5 30q0 16-5.5 30.5T817-647L290-120H120Zm640-584-56-56 56 56Zm-141 85-28-29 57 57-29-28Z" />
        </Svg>
    );
};

export const swipeDownIconSvg = () => {
    return (
        <Svg height="30px" viewBox="0 -960 960 960" width="30px" fill={colors.white}>
            <Path d="M180-360 40-500l42-42 70 70q-6-27-9-54t-3-54q0-82 27-159t78-141l43 43q-43 56-65.5 121.5T200-580q0 26 3 51.5t10 50.5l65-64 42 42-140 140Zm478 233q-23 8-46.5 7.5T566-131L304-253l18-40q10-20 28-32.5t40-14.5l68-5-112-307q-6-16 1-30.5t23-20.5q16-6 30.5 1t20.5 23l148 407-100 7 131 61q7 3 15 3.5t15-1.5l157-57q31-11 45-41.5t3-61.5l-55-150q-6-16 1-30.5t23-20.5q16-6 30.5 1t20.5 23l55 150q23 63-4.5 122.5T815-184l-157 57Zm-90-265-54-151q-6-16 1-30.5t23-20.5q16-6 30.5 1t20.5 23l55 150-76 28Zm113-41-41-113q-6-16 1-30.5t23-20.5q16-6 30.5 1t20.5 23l41 112-75 28Zm8 78Z" />
        </Svg>
    );
};

export const ExpandIconSVG = ({ color, size }: IconProp) => {
    return (
        <Svg height="24px" viewBox="0 -960 960 960" width="24px" fill={color}>
            <Path d="M480-344 240-584l56-56 184 184 184-184 56 56-240 240Z" />
        </Svg>
    );
};
