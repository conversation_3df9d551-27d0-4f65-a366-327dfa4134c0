import React from "react";
import Svg, { Defs, LinearGradient, Path, Rect, Stop } from "react-native-svg";

export default function Buttonicon() {
    return (
        // <View style={styles.buttonicon}>
        //     <View style={styles.iconconverty}>
        //         <Svg
        //             style={styles.group}
        //             width="20"
        //             height="24"
        //             viewBox="0 0 20 24"
        //             fill="none">
        //             <Path
        //                 d="M19.7606 4.32943C18.9362 5.11304 17.9239 6.07959 16.9042 7.06096C16.7349 7.22411 16.4666 7.234 16.2899 7.07703C15.3629 6.25633 14.1986 5.69767 12.9119 5.51845C12.687 5.48755 12.5214 5.2935 12.5214 5.06608V1.01079C12.5214 0.801913 12.6919 0.632582 12.9008 0.63629C12.9045 0.63629 12.9082 0.63629 12.9095 0.63629C15.6682 0.777193 17.8608 1.90194 19.7568 3.6657C19.9484 3.84368 19.9484 4.14774 19.7581 4.32819L19.7606 4.32943Z"
        //                 fill="url(#paint0_linear_4973_29160)"
        //             />
        //             <Path
        //                 d="M19.7667 19.7793L16.8486 16.9205C16.6829 16.7586 16.4209 16.7437 16.2429 16.8908C15.1009 17.8326 13.6288 18.3888 12.0282 18.3604C10.5487 18.3344 9.18913 17.803 8.11012 16.9316C6.62322 15.7327 5.67275 13.8911 5.69129 11.8504C5.69252 11.6144 5.70859 11.3808 5.73455 11.1521C5.93107 9.46375 6.78143 7.97439 8.0236 6.94234C8.09281 6.88424 8.16327 6.82862 8.23495 6.77424C9.06677 6.14018 10.2509 5.70511 11.3286 5.53454C11.5523 5.4987 11.7204 5.30959 11.7204 5.08341V1.12948C11.7204 0.861268 11.4905 0.648678 11.2248 0.674634C9.62173 0.830369 7.92348 1.31858 6.58614 2.06883C6.3958 2.17512 6.20793 2.2876 6.02377 2.40625C5.53308 2.71896 5.06958 3.06874 4.63452 3.4519C3.34291 4.58901 2.31209 6.01411 1.64095 7.62707C1.05633 9.03116 0.746095 10.5774 0.774522 12.199C0.883289 18.3073 5.87421 23.2784 11.9837 23.3625C14.9736 23.4032 17.7039 22.2896 19.7568 20.4393C19.9509 20.2651 19.9546 19.9598 19.768 19.7781L19.7667 19.7793Z"
        //                 fill="white"
        //             />
        //             <Defs>
        //                 <LinearGradient
        //                     id="paint0_linear_4973_29160"
        //                     x1="12.5226"
        //                     y1="3.91413"
        //                     x2="19.9027"
        //                     y2="3.91413"
        //                     gradientUnits="userSpaceOnUse">
        //                     <Stop stopColor="#FFD700" />
        //                     <Stop offset="1" stopColor="#D5B519" />
        //                 </LinearGradient>
        //             </Defs>
        //         </Svg>
        //     </View>
        // </View>
        <Svg width="39" height="39" viewBox="0 0 39 39" fill="none">
            <Defs>
                <LinearGradient
                    id="paint0_linear_8162_6742"
                    x1="21.0226"
                    y1="11.233"
                    x2="28.4027"
                    y2="11.233"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#FFD700" />
                    <Stop offset="1" stopColor="#D5B519" />
                </LinearGradient>
            </Defs>
            <Rect x="0.181763" width="38.6364" height="38.6364" rx="19.3182" fill="#682EB0" />
            <Path
                d="M28.2605 11.6483C27.4361 12.4319 26.4239 13.3984 25.4042 14.3798C25.2348 14.543 24.9666 14.5528 24.7899 14.3959C23.8629 13.5752 22.6986 13.0165 21.4119 12.8373C21.187 12.8064 21.0214 12.6123 21.0214 12.3849V8.32964C21.0214 8.12076 21.1919 7.95143 21.4008 7.95514C21.4045 7.95514 21.4082 7.95514 21.4095 7.95514C24.1682 8.09604 26.3608 9.22079 28.2568 10.9845C28.4484 11.1625 28.4484 11.4666 28.2581 11.647L28.2605 11.6483Z"
                fill="url(#paint0_linear_8162_6742)"
            />
            <Path
                d="M28.2667 27.098L25.3485 24.2392C25.1829 24.0773 24.9209 24.0625 24.7429 24.2095C23.6008 25.1514 22.1288 25.7076 20.5282 25.6791C19.0487 25.6532 17.6891 25.1217 16.6101 24.2503C15.1232 23.0514 14.1727 21.2098 14.1913 19.1692C14.1925 18.9331 14.2086 18.6995 14.2345 18.4708C14.431 16.7825 15.2814 15.2931 16.5236 14.2611C16.5928 14.203 16.6632 14.1473 16.7349 14.093C17.5668 13.4589 18.7508 13.0238 19.8286 12.8533C20.0523 12.8174 20.2204 12.6283 20.2204 12.4021V8.4482C20.2204 8.17999 19.9905 7.9674 19.7248 7.99336C18.1217 8.14909 16.4235 8.63731 15.0861 9.38756C14.8958 9.49385 14.7079 9.60632 14.5237 9.72498C14.0331 10.0377 13.5696 10.3875 13.1345 10.7706C11.8429 11.9077 10.8121 13.3328 10.1409 14.9458C9.5563 16.3499 9.24607 17.8961 9.2745 19.5177C9.38327 25.626 14.3742 30.5971 20.4837 30.6812C23.4735 30.722 26.2038 29.6083 28.2568 27.7581C28.4509 27.5838 28.4546 27.2785 28.2679 27.0968L28.2667 27.098Z"
                fill="white"
            />
        </Svg>
    );
}

// const styles = StyleSheet.create({
//     buttonicon: {
//         flexShrink: 0,
//         backgroundColor: 'rgba(255, 255, 255, 1)',
//         flexDirection: 'row',
//         alignItems: 'center',
//         justifyContent: 'center',
//         columnGap: 11.363636016845703,
//         padding: 5.681818008422852,
//         borderRadius: 1135.227294921875,
//     },
//     iconconverty: {
//         flexShrink: 0,
//         height: 39,
//         width: 39,
//         backgroundColor: 'rgba(104, 46, 176, 1)',
//         shadowColor: 'rgb(0, 0, 0)',
//         shadowOpacity: 0.5,
//         shadowRadius: 4.545454502105713,
//         rowGap: 0,
//         borderRadius: 112.49999237060547,
//     },
//     group: {
//         position: 'absolute',
//         flexShrink: 0,
//         top: 8,
//         height: 23,
//         left: 9,
//         width: 19,
//     },
// });
