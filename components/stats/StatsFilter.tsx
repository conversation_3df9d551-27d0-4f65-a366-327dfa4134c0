import React, { useEffect, useLayoutEffect, useState } from "react";
import { ScrollView, StyleSheet } from "react-native";
import StatsDateChip from "./StatsDateChip";
import { useStatsStore } from "../../store/statsStore";

const StatsFilter = () => {
    const chips = ["this week", "this month", "last 3 months", "all-times"];

    const { selectRange, setLoading, loading, range } = useStatsStore();

    const [activeChip, setActiveChip] = useState<number>(0);

    useLayoutEffect(() => {
        setActiveChip(range.length <= 7 ? 0 : range.length <= 30 ? 1 : range.length <= 90 ? 2 : 3);
    }, [range]);

    useEffect(() => {
        //console.log(loading);
        //! ????????
    }, [loading]);

    return (
        <ScrollView
            horizontal
            style={{ width: "105%", flexGrow: 0, alignSelf: "center" }}
            contentContainerStyle={{
                flexDirection: "row",
                alignItems: "center",
                gap: 5,
                paddingVertical: 2,
                paddingHorizontal: 10,
            }}
        >
            {chips.map((chip, index) => {
                return (
                    <StatsDateChip
                        label={chip}
                        key={index}
                        index={index}
                        activeChip={activeChip}
                        setActiveChip={(x: number) => {
                            setLoading(true);
                            setTimeout(() => {
                                if (x === 0) selectRange(7);
                                if (x === 1) selectRange(30);
                                if (x === 2) selectRange(90);
                                if (x === 3) selectRange(365);
                                setActiveChip(x);
                                setLoading(false);
                            }, 0);
                        }}
                    />
                );
            })}
        </ScrollView>
    );
};

export default StatsFilter;

const styles = StyleSheet.create({});
