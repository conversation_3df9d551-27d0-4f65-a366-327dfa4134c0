import { Platform, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import { typography } from "@styles/typography";
import colors from "@styles/colors";
import { handleStatus } from "@components/inputs/chips/Tag/Tag";
import Background from "@components/charts/HomeChart/HomeChartComponent/Background";
import chroma from "chroma-js";
import { Path, Svg } from "react-native-svg";

const FilterChip = ({
    title,
    active,
    action,
    type,
    disabled,
}: {
    title: string;
    active: boolean;
    action: () => void;
    type?: "orders";
    disabled?: boolean;
}) => {
    let backgroundColor = handleStatus(title).color;
    let color = "#ffffff";
    let contrastRatio = chroma.contrast(backgroundColor, color);

    if (contrastRatio <= 3.5) {
        color = chroma(backgroundColor).darken(3).hex();
    }

    if (type === "orders")
        return (
            <TouchableOpacity
                disabled={disabled}
                style={[
                    styles.container,
                    {
                        backgroundColor,
                    },
                    disabled && {
                        opacity: 0.5,
                    },
                ]}
                onPress={action}
            >
                <Text style={[styles.text, { color }]}>{title}</Text>
                {active && <CheckIcon color={color} />}
            </TouchableOpacity>
        );

    return (
        <TouchableOpacity
            disabled={disabled}
            style={[
                styles.container,
                active && {
                    backgroundColor: colors.primary[600],
                    borderColor: colors.primary[600],
                    borderWidth: 1,
                },
                !active && {
                    borderColor: colors.primary[600],
                    borderWidth: 1,
                },
                !active &&
                    disabled && {
                        backgroundColor: colors.gray[100],
                        borderColor: colors.gray[500],
                        borderWidth: 1,
                    },
            ]}
            onPress={action}
        >
            <Text
                style={[
                    styles.text,
                    !active && {
                        color: colors.primary[600],
                    },
                    !active &&
                        disabled && {
                            color: colors.gray[500],
                        },
                    active && {
                        color: colors.white,
                    },
                ]}
            >
                {title}
            </Text>
        </TouchableOpacity>
    );
};

const CheckIcon = ({ color }: { color: string }) => {
    return (
        <Svg height="24px" viewBox="0 -960 960 960" width="24px" fill={color}>
            <Path d="M400-304 240-464l56-56 104 104 264-264 56 56-320 320Z" />
        </Svg>
    );
};

export default FilterChip;

const styles = StyleSheet.create({
    container: {
        flexDirection: "row",
        gap: 5,
        paddingHorizontal: 12,
        paddingTop: 6,
        paddingBottom: Platform.OS == "ios" ? 5 : 2,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 6,
    },
    text: {
        fontFamily: typography.altTextSemiBold.fontFamily,
        fontSize: typography.sm.fontSize,
        textTransform: "capitalize",
        textAlign: "center",
    },
    disabled: {
        borderColor: colors.gray[600],
        borderWidth: 1,
    },
});
