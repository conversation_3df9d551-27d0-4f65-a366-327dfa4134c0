import { Color } from "@components/charts/HomeChart/HomeChartV2";
import { IconProp } from "./Props";
import colors from "@styles/colors";
import { StyleSheet, Text, View } from "react-native";
import { typography } from "@styles/typography";

const DeliveryCard = ({
    variant,
    MainIcon,
    SecondaryIcon,
    TertiaryIcon,
    mainValue,
    mainValueUnit = "unit",
    mainValueLabel,
    secondaryValue,
    secondaryValueUnit = "tnd",
    secondaryValueLabel,
    tertiaryValue,
    tertiaryValueUnit = "tnd",
    tertiaryValueLabel,
    color,
    simple,
}: {
    color?: string;
    variant: Color;
    MainIcon: React.FC<IconProp>;
    mainValue?: number;
    mainValueUnit?: string;
    mainValueLabel?: string;
    SecondaryIcon: React.FC<IconProp>;
    secondaryValue?: number;
    secondaryValueUnit?: string;
    secondaryValueLabel?: string;
    TertiaryIcon: React.FC<IconProp>;
    tertiaryValue?: number;
    tertiaryValueUnit?: string;
    tertiaryValueLabel?: string;
    simple?: boolean;
}) => {
    const backgroundColor = colors[variant][500];
    const textColor = colors.white;
    // const backgroundColor = colors[variant][200];
    // const textColor = colors[variant][600];
    // const backgroundColor = color;
    // const textColor = color;
    // const backgroundColor = chroma(color).darken(2).hex();

    return (
        <View
            style={{
                paddingHorizontal: 10,
                paddingVertical: 10,
                borderRadius: 10,
                backgroundColor: backgroundColor,
                flexDirection: "row",
                alignItems: "flex-end",
                justifyContent: "space-between",
                minWidth: 200,
                elevation: 2,
                gap: 20,
            }}
        >
            <View
                style={{
                    alignSelf: "stretch",
                    justifyContent: "space-between",
                    overflow: "visible",
                }}
            >
                <MainIcon color={textColor} />
                <View style={{ gap: -5, overflow: "visible" }}>
                    <View
                        style={{
                            flexDirection: "row",
                            alignItems: "baseline",
                            flexWrap: "wrap",

                            columnGap: 2,
                            rowGap: -10,
                        }}
                    >
                        <Text style={[cardStyles.mainValue, { color: textColor }]}>
                            {mainValue?.toLocaleString("en-US")}
                        </Text>
                        <Text style={[cardStyles.mainUnit, { color: textColor }]}>{mainValueUnit}</Text>
                    </View>
                    <Text style={[cardStyles.mainLabel, { color: textColor }]}>{mainValueLabel}</Text>
                </View>
            </View>

            <View style={{ gap: 15 }}>
                <View style={[{ alignItems: "flex-end" }, simple ? { opacity: 0 } : {}]}>
                    <Text style={[cardStyles.secondaryValue, { color: textColor }]}>
                        {secondaryValue?.toLocaleString()}
                        <Text style={cardStyles.secondaryUnit}>{" " + secondaryValueUnit}</Text>
                    </Text>

                    <View style={cardStyles.secondaryContainer}>
                        <SecondaryIcon color={textColor} />
                        <Text style={[cardStyles.secondaryLabel, { color: textColor }]}>{secondaryValueLabel}</Text>
                    </View>
                </View>

                <View style={[{ alignItems: "flex-end" }, simple ? { opacity: 0 } : {}]}>
                    <Text style={[cardStyles.secondaryValue, { color: textColor }]}>
                        {tertiaryValue?.toLocaleString()}
                        <Text style={cardStyles.secondaryUnit}>{" " + tertiaryValueUnit}</Text>
                    </Text>
                    <View style={cardStyles.secondaryContainer}>
                        <TertiaryIcon color={textColor} />
                        <Text style={[cardStyles.secondaryLabel, { color: textColor }]}>{tertiaryValueLabel}</Text>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default DeliveryCard;

const cardStyles = StyleSheet.create({
    mainValue: {
        fontSize: typography.xl.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    mainLabel: {
        fontSize: typography.sm.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    mainUnit: {
        fontSize: typography.xs.fontSize,
        fontFamily: typography.fontNormal.fontFamily,
    },

    secondaryContainer: {
        flexDirection: "row",
        alignItems: "center",
        gap: 2,
    },

    secondaryValue: {
        fontSize: typography.lg.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    secondaryLabel: {
        fontSize: typography.sm.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    secondaryUnit: {
        fontSize: typography.xxs.fontSize,
        fontFamily: typography.fontNormal.fontFamily,
        textTransform: "uppercase",
    },
});
