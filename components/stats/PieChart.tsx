import colors from "@styles/colors";
import React from "react";
import { StyleSheet, Text, ViewStyle } from "react-native";
import { Circle, Mask, Polyline, Svg } from "react-native-svg";

type AngleCoupleProps = {
    first: number;
    second: number;
};

type QuadrantCoupleProps = {
    first: number;
    second: number;
};

type CoordinateCouplePrps = {
    first: number[];
    second: number[];
};

const percentageToRad = (percentage: number) => {
    return (percentage * 2 * Math.PI) / 100;
};

const accumulatePercentages = (percentages: number[]) => {
    let value = 0;
    let result: number[] = [];
    percentages.map((percentage, index) => {
        value = value + percentage;
        result.push(value);
    });
    return result;
};

const angleCouples = (percentages: number[]) => {
    let result: Array<{ first: number; second: number }> = [];
    let iterator = 0;
    percentages.map((percentage, index) => {
        let second = percentageToRad(percentage);
        result.push({ first: iterator, second });

        iterator = second;
    });

    return result;
};

let quadrants = (angle: number) => {
    if (angle > Math.PI / 4 && angle <= (3 * Math.PI) / 4) {
        return 2;
    }
    if (angle > (3 * Math.PI) / 4 && angle <= (5 * Math.PI) / 4) {
        return 3;
    }
    if (angle > (5 * Math.PI) / 4 && angle <= (7 * Math.PI) / 4) {
        return 4;
    }
    return 1;
};

let normalizedSin = (angle: number) => {
    return 500 + 500 * Math.sin(angle);
};

let normalizedCon = (angle: number) => {
    return 500 + 500 * Math.cos(angle);
};

const angleCoupleToCoordinates = (angleCouple: AngleCoupleProps) => {
    let first = [normalizedCon(angleCouple.first), normalizedSin(angleCouple.first)];
    let second = [normalizedCon(angleCouple.second), normalizedSin(angleCouple.second)];
    return { first, second };
};

const angleCoupleToQuadrants = (angleCouple: AngleCoupleProps) => {
    return {
        first: quadrants(angleCouple.first),
        second: quadrants(angleCouple.second),
    };
};

let coordinatesCouples = (angleCouples: AngleCoupleProps[]) => {
    let result: CoordinateCouplePrps[] = [];
    angleCouples.map((angleCouple) => {
        result.push(angleCoupleToCoordinates(angleCouple));
    });
    return result;
};
let quadrantCouples = (angleCouples: AngleCoupleProps[]) => {
    let result: QuadrantCoupleProps[] = [];
    angleCouples.map((angleCouple) => {
        result.push(angleCoupleToQuadrants(angleCouple));
    });
    return result;
};
let projectionCouple = (angleCouple: AngleCoupleProps, quadrantCouple: QuadrantCoupleProps) => {
    let result = { first: [0, 0], second: [0, 0] };

    switch (quadrantCouple.first) {
        case 1: {
            result.first = [1000, normalizedSin(angleCouple.first)];
            break;
        }
        case 2: {
            result.first = [normalizedCon(angleCouple.first), 1000];
            break;
        }
        case 3: {
            result.first = [0, normalizedSin(angleCouple.first)];
            break;
        }
        case 4: {
            result.first = [normalizedCon(angleCouple.first), 0];
            break;
        }
    }
    switch (quadrantCouple.second) {
        case 1: {
            result.second = [1000, normalizedSin(angleCouple.second)];
            break;
        }
        case 2: {
            result.second = [normalizedCon(angleCouple.second), 1000];
            break;
        }
        case 3: {
            result.second = [0, normalizedSin(angleCouple.second)];
            break;
        }
        case 4: {
            result.second = [normalizedCon(angleCouple.second), 0];
            break;
        }
    }
    return result;
};

let projectionCouples = (angleCouples: AngleCoupleProps[], quadrantCouples: QuadrantCoupleProps[]) => {
    let result: CoordinateCouplePrps[] = [];
    angleCouples.map((angle, index) => {
        result.push(projectionCouple(angle, quadrantCouples[index]));
    });
    return result;
};
const corners = [
    [1000, 1000],
    [0, 1000],
    [0, 0],
    [1000, 0],
];

let addCorners = (quadrantCouple: QuadrantCoupleProps) => {
    let { first, second } = quadrantCouple;
    let diff = second - first;
    let result: number[][] = [];
    if (diff > 0) {
        for (let i = first; i < second; i++) {
            result.push(corners[i - 1]);
        }
    } else if (diff < 0) {
        for (let i = first; i <= 4; i++) {
            result.push(corners[i - 1]);
        }
    }

    return result;
};

let cornersList = (quadrantCouples: QuadrantCoupleProps[]) => {
    let result: number[][][] = [];
    quadrantCouples.map((quadrantCouple, index) => {
        result.push(addCorners(quadrantCouple));
    });
    return result;
};

let coords = (coordinate: number[]) => {
    return `${coordinate[0]} ${coordinate[1]}`;
};
let coordsListToString = (cornerListItem: number[][]) => {
    let corner = [0, 0];
    let string = `${corner[0]} ${corner[1]}, `;
    let result = "";
    cornerListItem.map((corner) => {
        result = result + `${corner[0]} ${corner[1]},`;
    });
    return result;
};

let createPercentages = (data: DataProps) => {
    let percentages: number[] = [];
    let total = 0;
    data.map((item, index) => {
        if (item.value !== undefined) {
            total = total + item.value;
        } else {
            total = total;
        }
    });
    if (total === 0) {
        return [];
    }
    data.map((item, index) => {
        if (item.value !== undefined) {
            percentages.push((item.value * 100) / total);
        } else {
            percentages.push(0);
        }
    });

    return percentages;
};

type DataProps = {
    name: string;
    color: string;
    value: number;
}[];

const PieChart = ({ data, style }: { data: DataProps; style?: ViewStyle }) => {
    let percentages = createPercentages(data);
    let anglePairs = angleCouples(accumulatePercentages(percentages));
    let coordinatePairs = coordinatesCouples(anglePairs);
    let quadrantPairs = quadrantCouples(anglePairs);
    let cornersItems = cornersList(quadrantPairs);
    let projectionPairs = projectionCouples(anglePairs, quadrantPairs);

    const mycolors = ["primary", "blue", "teal", "cyan", "orange", "pink", "green", "gray", "secondary", "red"];

    return percentages.length > 0 ? (
        <Svg viewBox="0 0 1000 1000" style={style}>
            {data.map((value, index) => {
                return (
                    <Mask id={`mask${index}`} onPress={() => {}}>
                        <Polyline
                            key={index}
                            points={`
														500 500, 
														${coords(coordinatePairs[index].first)}, 
														${coords(projectionPairs[index].first)}, 
														${coordsListToString(cornersItems[index])},
														${coords(projectionPairs[index].second)}, 
														${coords(coordinatePairs[index].second)}, 
														`}
                            fill={"white"}
                        />
                    </Mask>
                );
            })}

            {data.map((value, index) => {
                return (
                    <Circle
                        key={index}
                        cx={500}
                        cy={500}
                        r={500}
                        mask={percentages[index] !== 100 ? `url(#mask${index})` : ""}
                        fill={colors[mycolors[index % 10]][400]}
                    />
                );
            })}
        </Svg>
    ) : (
        <Text>No available data</Text>
    );
};

export default PieChart;

const styles = StyleSheet.create({});
