import { StyleProp, StyleSheet, Text, View, ViewStyle } from "react-native";
import React from "react";
import colors from "@styles/colors";

const StatsCard = ({ children, style }: { children?: React.ReactNode; style?: StyleProp<ViewStyle> }) => {
    return (
        <View
            style={[
                {
                    overflow: "visible",
                    backgroundColor: colors.white,
                    borderRadius: 15,
                    elevation: 2,
                    padding: 20,
                    shadowColor: colors.gray["900"], // IOS
                    shadowOffset: { height: 1, width: 0 }, // IOS
                    shadowOpacity: 0.2, // IOS
                    shadowRadius: 1.5, //IOS
                },
                style,
            ]}
        >
            {children}
        </View>
    );
};

export default StatsCard;

const styles = StyleSheet.create({});
