import { Color } from "@components/charts/HomeChart/HomeChartV2";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

const HorizontalPercentageCharte = ({
    firstValue,
    secondValue,
    firstColor,
    secondColor,
}: {
    firstValue: number;
    secondValue: number;
    firstColor: Color;
    secondColor: Color;
}) => {
    const firstPercentage = `${((firstValue * 100) / (firstValue + secondValue)).toFixed(2)}%`;
    const secondPercentage = `${((secondValue * 100) / (firstValue + secondValue)).toFixed(2)}%`;
    return firstPercentage !== "NaN%" && secondPercentage !== "NaN%" ? (
        <View
            style={{
                flexDirection: "row",
                borderRadius: 5,
                overflow: "hidden",
                alignSelf: "stretch",
                height: 40,
            }}
        >
            <View
                style={{
                    backgroundColor: colors[firstColor][500],
                    width: firstPercentage,
                    alignItems: "center",
                    justifyContent: "center",
                    paddingVertical: 10,
                }}
            >
                <Text
                    style={{
                        fontFamily: typography.fontSemibold.fontFamily,
                        color: colors.white,
                        fontSize: typography.sm.fontSize,
                    }}
                >
                    {firstPercentage}
                </Text>
            </View>
            <View
                style={{
                    backgroundColor: colors[secondColor][500],
                    width: secondPercentage,
                    alignItems: "center",
                    justifyContent: "center",
                    paddingVertical: 10,
                }}
            >
                <Text
                    style={{
                        fontFamily: typography.fontSemibold.fontFamily,
                        color: colors.white,
                        fontSize: typography.sm.fontSize,
                    }}
                >
                    {secondPercentage}
                </Text>
            </View>
        </View>
    ) : (
        <View>
            <Text
                style={{
                    fontFamily: typography.fontSemibold.fontFamily,
                    color: colors.black,
                    fontSize: typography.sm.fontSize,
                    textAlign: "center",
                }}
            >
                No Data
            </Text>
        </View>
    );
};

export default HorizontalPercentageCharte;

const styles = StyleSheet.create({});
