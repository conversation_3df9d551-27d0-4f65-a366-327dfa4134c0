import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { StyleSheet, Text, TouchableOpacity } from "react-native";

const StatsDateChip = ({
    label,
    index,
    activeChip,
    setActiveChip,
}: {
    label: string;
    index: number;
    activeChip: number;
    // setActiveChip: React.Dispatch<React.SetStateAction<number>>;
    setActiveChip: (x: number) => void;
}) => {
    const active = index === activeChip;
    return (
        <TouchableOpacity
            onPress={() => {
                if (index != activeChip) setActiveChip(index);
            }}
            style={[
                styles.container,
                active
                    ? {
                          backgroundColor: colors.primary[600],
                          borderColor: colors.primary[600],
                          borderWidth: 1,
                      }
                    : {
                          borderColor: colors.primary[600],
                          borderWidth: 1,
                      },
            ]}
        >
            <Text
                style={[
                    styles.text,
                    active
                        ? {
                              color: colors.white,
                          }
                        : {
                              color: colors.primary[600],
                          },
                ]}
            >
                {label}
            </Text>
        </TouchableOpacity>
    );
};

export default StatsDateChip;

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 6,
    },
    text: {
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.sm.fontSize,
        textTransform: "capitalize",
        textAlign: "center",
    },
});
