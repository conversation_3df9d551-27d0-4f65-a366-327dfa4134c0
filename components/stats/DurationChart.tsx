import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Platform, Text, View } from "react-native";
import { Path, Svg } from "react-native-svg";

const BackgroundHalfCircleSvg = ({ color, height, width }: { color: string; height?: number; width?: number }) => {
    return (
        <Svg height={111} width={221} viewBox="0 0 221 111" fill="none">
            <Path
                d="M3.6 111C1.61178 111 -0.0061313 109.387 0.0587292 107.4C0.970159 79.4759 12.4579 52.885 32.272 33.061C52.936 12.3867 80.9643 0.764629 110.195 0.750014C139.425 0.735399 167.465 12.3294 188.15 32.983C207.984 52.7872 219.498 79.3666 220.438 107.29C220.504 109.277 218.888 110.891 216.9 110.893L180 110.93C178.012 110.932 176.409 109.321 176.299 107.335C175.399 91.1092 168.549 75.7318 156.99 64.1898C144.579 51.7976 127.755 44.8412 110.217 44.85C92.6786 44.8588 75.8616 51.832 63.4632 64.2366C51.9155 75.7901 45.0813 91.1743 44.1979 107.401C44.0898 109.387 42.4882 111 40.5 111H3.6Z"
                fill={color}
            />
        </Svg>
    );
};
const ValueCircleSvg = ({
    color,
    height,
    width,
    rotate,
}: {
    color: string;
    height?: number;
    width?: number;
    rotate?: number;
}) => {
    return (
        <Svg
            style={{ position: "absolute" }}
            height={222}
            width={221}
            viewBox="0 0 221 222"
            fill="none"
            // rotation={rotate ? rotate : 0}
            transform={[{ rotateZ: `${rotate}deg` }]}
        >
            <Path
                d="M3.6 111C1.61178 111 -0.0061313 109.387 0.0587292 107.4C0.970159 79.4759 12.4579 52.885 32.272 33.061C52.936 12.3867 80.9643 0.764629 110.195 0.750014C139.425 0.735399 167.465 12.3294 188.15 32.983C207.984 52.7872 219.498 79.3666 220.438 107.29C220.504 109.277 218.888 110.891 216.9 110.893L180 110.93C178.012 110.932 176.409 109.321 176.299 107.335C175.399 91.1092 168.549 75.7318 156.99 64.1898C144.579 51.7976 127.755 44.8412 110.217 44.85C92.6786 44.8588 75.8616 51.832 63.4632 64.2366C51.9155 75.7901 45.0813 91.1743 44.1979 107.401C44.0898 109.387 42.4882 111 40.5 111H3.6Z"
                fill={color}
            />
        </Svg>
    );
};

const TimeScaleLabel = ({ label }: { label: string }) => {
    return (
        <Text
            style={{
                width: 44,
                textAlign: "center",
                fontFamily: typography.baseText.fontFamily,
                fontSize: typography.xs.fontSize,
            }}
        >
            {label}
        </Text>
    );
};
const TimeValue = ({ label }: { label: string }) => {
    return (
        <View>
            <Text style={{ width: 44, textAlign: "center" }}>{label}</Text>
        </View>
    );
};
const DurationChart = ({ title, description, value }: { title: string; description: string; value: number }) => {
    const hours = Math.floor(value / (60 * 60));
    const minutes = Math.floor(value / 60) - hours * 60;
    const seconds = value - minutes * 60 - hours * 60 * 60;
    const ratio = value / (72 * 60 * 60);
    const angle = -180 + ratio * 180;
    const addZero = (time: string) => {
        if (time.length < 2) return "0" + time;
        return time;
    };

    return (
        <View style={{ alignItems: "center", alignSelf: "center" }}>
            <View
                style={{
                    width: 1,
                    alignItems: "center",
                    gap: 5,
                    paddingBottom: 20,
                }}
            >
                <Text
                    style={{
                        textAlign: "center",
                        width: 1000,
                        color: colors.primary[500],
                        fontFamily: typography.fontBold.fontFamily,
                        fontSize: typography.lg.fontSize,
                        textTransform: "capitalize",
                    }}
                >
                    {title}
                </Text>
                <Text
                    style={{
                        textAlign: "center",
                        width: 300,
                        color: colors.gray[500],
                        fontFamily: typography.fontBold.fontFamily,
                        fontSize: typography.xs.fontSize,
                    }}
                >
                    {description}
                </Text>
            </View>
            <View style={{ overflow: "hidden" }}>
                <BackgroundHalfCircleSvg color={colors.gray[200]} />
                <ValueCircleSvg color={colors.primary[500]} rotate={angle} />
            </View>
            <View
                style={{
                    flexDirection: "row",
                    alignSelf: "stretch",
                    justifyContent: "space-between",
                }}
            >
                <TimeScaleLabel label="0h" />
                <TimeScaleLabel label="72h" />
            </View>
            <View
                style={{
                    position: "absolute",
                    width: "100%",
                    bottom: 0,
                    gap: -15,
                    alignItems: "center",
                }}
            >
                <Text
                    style={{
                        fontFamily: typography.fontBold.fontFamily,
                        fontSize: typography.xxl.fontSize,
                        color: colors.gray[800],
                    }}
                >
                    {addZero(hours.toFixed(0))}h
                </Text>
                <Text
                    style={{
                        fontFamily: typography.fontBold.fontFamily,
                        fontSize: typography.lg.fontSize,
                        color: colors.gray[600],
                    }}
                >
                    {addZero(minutes.toFixed(0))}m{addZero(seconds.toFixed(0))}s
                </Text>
            </View>
        </View>
    );
};
export default DurationChart;
