import React from "react";
import { View, Text, TouchableOpacity, Linking, StyleSheet, Platform } from "react-native";
import colors from "@styles/colors";
import Button from "@components/inputs/buttons/Button";

const NeedsUpdate = () => {
    const handlePress = () => {
        const url =
            Platform.OS === "ios"
                ? "https://apps.apple.com/tn/app/converty-shop/id6466304252"
                : "https://play.google.com/store/apps/details?id=com.converty.app";
        Linking.openURL(url);
    };

    return (
        <View style={styles.container}>
            <Button
                label={"Please Get The Latest Version"}
                type={"filled"}
                variant={"primary"}
                action={handlePress}
                style={{ height: 40 }}
                labelStyle={{ fontSize: 16 }}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: colors.gray[200],
    },
});

export default NeedsUpdate;
