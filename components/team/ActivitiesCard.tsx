import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { View, Text, StyleSheet } from "react-native";

function formatDate(seconds: number): string {
    return new Date(seconds).toLocaleString();
}

const ActivityCard = ({ user, action, time }: { user: string; action: string; time: number }) => {
    return (
        <View style={styles.firstView}>
            <View style={styles.column}>
                <View>
                    <View style={styles.row}>
                        <Text style={styles.budgetDate}>{"User"}</Text>
                        <Text style={styles.budgetName}>{user}</Text>
                    </View>
                    <View style={styles.row}>
                        <Text style={styles.budgetDate}>{"Action"}</Text>
                        <Text style={styles.budgetName}>{action}</Text>
                    </View>
                    <View style={styles.row}>
                        <Text style={styles.budgetDate}>{"Time"}</Text>
                        <Text style={styles.budgetDate}>{formatDate(time)}</Text>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default ActivityCard;

const styles = StyleSheet.create({
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        height: 30,
    },
    column: {
        flexDirection: "column",
        justifyContent: "space-between",
        alignItems: "flex-start",
    },
    firstView: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderColor: colors.gray[400],
        borderWidth: 1,
        borderRadius: 10,
    },
    budgetName: {
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.lg.fontSize,
        color: colors.gray[600],
    },
    budgetDate: {
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: typography.sm.fontSize,
        color: colors.gray[600],
    },
    buttonLabel: {
        textTransform: "capitalize",
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: typography.sm.fontSize,
        color: colors.gray[600],
    },
});
