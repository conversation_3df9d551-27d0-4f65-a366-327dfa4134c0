import React from "react";
import { View, Text, StyleSheet, SafeAreaView } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import SocketReconnectTester from "./SocketReconnectTester";

/**
 * Example debug screen that includes the SocketReconnectTester
 *
 * This is a complete example of how to integrate the socket tester
 * into your debug/development screens.
 *
 * Usage:
 * 1. Add this screen to your debug navigation
 * 2. Access it during development for socket testing
 * 3. Use for debugging socket issues in development/staging
 */
const DebugSocketScreen: React.FC = () => {
    return (
        <SafeAreaView style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>Socket Debug Tools</Text>
                <Text style={styles.subtitle}>Test and debug socket reconnection behavior</Text>
            </View>

            <SocketReconnectTester />
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
    },
    header: {
        padding: 16,
        backgroundColor: colors.primary[50],
        borderBottomWidth: 1,
        borderBottomColor: colors.gray[200],
    },
    title: {
        ...typography.xl,
        ...typography.fontBold,
        color: colors.primary[700],
        marginBottom: 4,
    },
    subtitle: {
        ...typography.sm,
        color: colors.gray[600],
    },
});

export default DebugSocketScreen;
