# Socket Reconnection Tester - Debug Tool

## 🎯 **Overview**

The `SocketReconnectTester` is a comprehensive manual testing and debugging tool for the socket reconnection system in the Converty Mobile App. It provides real-time monitoring, manual controls, and automated testing scenarios to help developers debug and validate socket behavior.

## 📁 **Location**

```
components/debug/
├── README.md                    # This documentation
├── SocketReconnectTester.tsx    # Main testing component
├── DEBUGGING_GUIDE.md          # Detailed debugging guide
└── DebugSocketScreen.tsx       # Example integration screen
```

## 🚀 **Quick Start**

### **1. Add to Your Debug Screen**

**Option A: Use the Complete Example Screen**

```typescript
import DebugSocketScreen from "@components/debug/DebugSocketScreen";

// Add to your debug navigation
<DebugSocketScreen />;
```

**Option B: Add Component Directly**

```typescript
import SocketReconnectTester from "@components/debug/SocketReconnectTester";

export default function DebugScreen() {
    return (
        <View style={{ flex: 1 }}>
            <SocketReconnectTester />
        </View>
    );
}
```

### **2. Access the Tester**

Navigate to your debug screen in the app to access the socket testing interface.

## 🎮 **Features**

### **Real-time Monitoring**

-   🔴/🟢 **Connection Status Indicator** - Visual connection state
-   📊 **Live Statistics** - Connections, disconnections, errors, streaks
-   📝 **Event Logging** - Real-time event log (up to 100 entries)
-   🔄 **Reconnection Counter** - Current reconnection attempts

### **Manual Controls**

-   **Force Disconnect** - Manually disconnect the socket
-   **Force Connect** - Manually connect the socket
-   **Simulate Network Error** - Trigger network timeout scenarios
-   **Test Reconnection Flow** - Automated reconnection test
-   **Run Stress Test** - 5-cycle rapid connect/disconnect test
-   **Test Network Timeout** - Specific timeout error testing
-   **Clear Logs & Stats** - Reset all data

### **Automated Test Scenarios**

-   **Reconnection Flow Test** - 30-second automated reconnection testing
-   **Stress Test** - Rapid connect/disconnect cycles for stability testing
-   **Network Timeout Test** - Simulates and tests network timeout recovery

## 📊 **Understanding the Interface**

### **Connection Status**

-   🟢 **Green Dot** - Socket is connected
-   🔴 **Red Dot** - Socket is disconnected
-   **Reconnect Attempts** - Shows current attempt count (max 10)

### **Statistics Panel**

-   **Connections** - Total successful connections
-   **Disconnections** - Total disconnections
-   **Errors** - Total connection errors
-   **Reconnect Attempts** - Total reconnection attempts
-   **Current Streak** - Current consecutive successful connections

### **Event Log Colors**

-   🟢 **Green** - Successful connections (`CONNECT`, `INITIAL_STATE`)
-   🔴 **Red** - Errors and disconnections (`DISCONNECT`, `CONNECT_ERROR`, `ERROR`)
-   🟠 **Orange** - Reconnection attempts (`RECONNECT_ATTEMPT`)
-   🔵 **Blue** - Manual actions (`MANUAL_ACTION`)
-   🟣 **Purple** - Test events (`TEST_START`, `STRESS_TEST_START`)
-   ⚪ **Gray** - System messages (`SYSTEM`)

## 🧪 **Testing Scenarios**

### **1. Basic Connection Testing**

1. **Check Initial State** - Verify current connection status
2. **Force Disconnect** - Test disconnection handling
3. **Force Connect** - Test connection establishment
4. **Monitor Logs** - Watch for expected events

### **2. Network Error Testing**

1. **Simulate Network Error** - Test error handling
2. **Watch Reconnection** - Monitor automatic reconnection attempts
3. **Verify Platform Behavior** - Check iOS vs Android differences

### **3. Reconnection Flow Testing**

1. **Start Reconnection Test** - Use automated test
2. **Monitor Progress** - Watch 30-second test cycle
3. **Check Results** - Verify successful reconnection

### **4. Stress Testing**

1. **Run Stress Test** - Execute 5-cycle rapid testing
2. **Monitor Stability** - Check for failures or memory issues
3. **Review Statistics** - Analyze performance metrics

## 🔧 **Platform-Specific Behavior**

### **iOS Behavior**

-   **Timeout**: 30 seconds
-   **Reconnection**: Exponential backoff (5s → 7.5s → 11.25s → ...)
-   **Strategy**: Force disconnect before reconnect
-   **Max Delay**: 30 seconds

### **Android Behavior**

-   **Timeout**: 20 seconds
-   **Reconnection**: Fixed 2-second delays
-   **Strategy**: Direct reconnection
-   **Max Delay**: 2 seconds

## 🐛 **Debugging Common Issues**

### **Connection Not Working**

1. Check server availability
2. Verify network connectivity
3. Look for error messages in logs
4. Test on different networks

### **Reconnection Not Triggering**

1. Check platform-specific behavior
2. Verify error types in logs
3. Test with network timeout simulation
4. Monitor reconnection attempt counter

### **Performance Issues**

1. Run stress test to identify problems
2. Monitor memory usage during testing
3. Check for excessive reconnection attempts
4. Review connection statistics

## 📝 **Log Analysis**

### **Normal Connection Flow**

```
INITIAL_STATE: Socket is disconnected
CONNECT: Socket connected successfully
```

### **Reconnection Flow**

```
DISCONNECT: Socket disconnected
RECONNECT_ATTEMPT: Reconnection attempt #1 (Platform: ios)
CONNECT: Socket connected successfully (reconnect took 2150ms)
```

### **Error Scenario**

```
CONNECT_ERROR: Connection error: Network timeout
RECONNECT_ATTEMPT: Reconnection attempt #1 (Platform: ios)
RECONNECT_ATTEMPT: Reconnection attempt #2 (Platform: ios)
CONNECT: Socket connected successfully (reconnect took 5200ms)
```

## 🔍 **Troubleshooting**

### **Component Not Loading**

-   Check import path: `@components/debug/SocketReconnectTester`
-   Verify socket manager import: `../../socket/socket`
-   Ensure TextButton component is available

### **Buttons Not Working**

-   Check if tests are running (buttons disabled during tests)
-   Verify socket manager methods are accessible
-   Check console for error messages

### **Logs Not Updating**

-   Verify socket event listeners are registered
-   Check if socket manager is properly initialized
-   Ensure component is mounted and not unmounted

### **Statistics Not Accurate**

-   Clear logs and stats to reset counters
-   Verify event handlers are properly updating stats
-   Check for duplicate event listeners

## 📚 **Related Documentation**

-   [Debugging Guide](./DEBUGGING_GUIDE.md) - Detailed debugging instructions
-   [Socket Manager](../../socket/socketManager.ts) - Core socket implementation
-   [Socket Exports](../../socket/socket.ts) - Socket manager exports

## 🎯 **Best Practices**

### **For Development**

1. **Use regularly** during socket-related development
2. **Test on both platforms** (iOS and Android)
3. **Monitor logs** for unexpected behavior
4. **Clear logs periodically** to focus on current issues

### **For Debugging**

1. **Start with basic tests** before complex scenarios
2. **Compare platform behaviors** when debugging
3. **Use stress tests** to identify stability issues
4. **Document findings** for team reference

### **For Production Debugging**

1. **Include in debug builds** for field testing
2. **Use for customer issue reproduction**
3. **Monitor connection patterns** in different environments
4. **Test network condition variations**

This tool provides comprehensive socket testing and debugging capabilities to ensure reliable network connectivity in your React Native application! 🚀
