import React, { useState, useEffect, useRef, useCallback } from "react";
import { View, Text, StyleSheet, ScrollView, Alert, Platform } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { socketManager } from "../../../socket/socket";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";

interface ConnectionLog {
    id: string;
    timestamp: string;
    event: string;
    details: string;
    platform?: string;
    reconnectCount?: number;
}

interface ConnectionStats {
    totalConnections: number;
    totalDisconnections: number;
    totalReconnectAttempts: number;
    totalErrors: number;
    currentStreak: number;
    longestStreak: number;
    averageReconnectTime: number;
}

const SocketReconnectTester: React.FC = () => {
    const [isConnected, setIsConnected] = useState(false);
    const [connectionLogs, setConnectionLogs] = useState<ConnectionLog[]>([]);
    const [reconnectAttempts, setReconnectAttempts] = useState(0);
    const [isTestRunning, setIsTestRunning] = useState(false);
    const [connectionStats, setConnectionStats] = useState<ConnectionStats>({
        totalConnections: 0,
        totalDisconnections: 0,
        totalReconnectAttempts: 0,
        totalErrors: 0,
        currentStreak: 0,
        longestStreak: 0,
        averageReconnectTime: 0,
    });
    const reconnectStartTime = useRef<number | null>(null);
    const testTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const addLog = useCallback(
        (event: string, details: string, extraData?: Partial<ConnectionLog>) => {
            const log: ConnectionLog = {
                id: `${Date.now()}-${Math.random()}`,
                timestamp: new Date().toLocaleTimeString(),
                event,
                details,
                platform: Platform.OS,
                reconnectCount: reconnectAttempts,
                ...extraData,
            };
            setConnectionLogs((prev) => [log, ...prev].slice(0, 100)); // Keep last 100 logs
        },
        [reconnectAttempts]
    );

    // Update stats helper
    const updateStats = useCallback((type: "connect" | "disconnect" | "error" | "reconnect_attempt") => {
        setConnectionStats((prev) => {
            const newStats = { ...prev };
            switch (type) {
                case "connect":
                    newStats.totalConnections++;
                    newStats.currentStreak++;
                    if (newStats.currentStreak > newStats.longestStreak) {
                        newStats.longestStreak = newStats.currentStreak;
                    }
                    break;
                case "disconnect":
                    newStats.totalDisconnections++;
                    newStats.currentStreak = 0;
                    break;
                case "error":
                    newStats.totalErrors++;
                    break;
                case "reconnect_attempt":
                    newStats.totalReconnectAttempts++;
                    break;
            }
            return newStats;
        });
    }, []);

    useEffect(() => {
        // Set initial connection state
        const initialConnected = socketManager.isSocketConnected();
        setIsConnected(initialConnected);

        if (initialConnected) {
            addLog("INITIAL_STATE", "Socket is already connected");
        } else {
            addLog("INITIAL_STATE", "Socket is disconnected");
        }

        // Connection event handlers with better error handling
        const handleConnect = () => {
            const now = Date.now();
            setIsConnected(true);
            setReconnectAttempts(0);
            updateStats("connect");

            if (reconnectStartTime.current) {
                const reconnectTime = now - reconnectStartTime.current;
                addLog("CONNECT", `Socket connected successfully (reconnect took ${reconnectTime}ms)`);
                reconnectStartTime.current = null;
            } else {
                addLog("CONNECT", "Socket connected successfully");
            }
        };

        const handleDisconnect = (reason?: string) => {
            setIsConnected(false);
            updateStats("disconnect");
            addLog("DISCONNECT", `Socket disconnected${reason ? `: ${reason}` : ""}`);
            reconnectStartTime.current = Date.now();
        };

        const handleConnectError = (error: any) => {
            updateStats("error");
            const errorMsg = error?.message || error?.toString() || "Unknown error";
            addLog("CONNECT_ERROR", `Connection error: ${errorMsg}`);
        };

        const handleReconnectAttempt = (attempt: number) => {
            setReconnectAttempts(attempt);
            updateStats("reconnect_attempt");
            addLog("RECONNECT_ATTEMPT", `Reconnection attempt #${attempt} (Platform: ${Platform.OS})`);
        };

        const handleReconnectFailed = () => {
            addLog("RECONNECT_FAILED", "All reconnection attempts failed - max attempts reached");
        };

        // Register event listeners
        socketManager.on("connect", handleConnect);
        socketManager.on("disconnect", handleDisconnect);
        socketManager.on("connect_error", handleConnectError);
        socketManager.on("reconnect_attempt", handleReconnectAttempt);
        socketManager.on("reconnect_failed", handleReconnectFailed);

        return () => {
            // Cleanup listeners
            socketManager.off("connect", handleConnect);
            socketManager.off("disconnect", handleDisconnect);
            socketManager.off("connect_error", handleConnectError);
            socketManager.off("reconnect_attempt", handleReconnectAttempt);
            socketManager.off("reconnect_failed", handleReconnectFailed);

            // Clear any running timeouts
            if (testTimeoutRef.current) {
                clearTimeout(testTimeoutRef.current);
            }
        };
    }, [addLog, updateStats]);

    // Action functions with better error handling and logging
    const forceDisconnect = useCallback(() => {
        try {
            addLog("MANUAL_ACTION", "Forcing socket disconnect");
            (socketManager as any).socket.disconnect();
        } catch (error) {
            addLog("ERROR", `Failed to disconnect: ${error}`);
        }
    }, [addLog]);

    const forceConnect = useCallback(() => {
        try {
            addLog("MANUAL_ACTION", "Forcing socket connect");
            (socketManager as any).socket.connect();
        } catch (error) {
            addLog("ERROR", `Failed to connect: ${error}`);
        }
    }, [addLog]);

    const simulateNetworkError = useCallback(() => {
        try {
            addLog("MANUAL_ACTION", `Simulating network timeout error on ${Platform.OS}`);

            // Create a realistic network error
            const networkError = new Error("Network timeout - simulated error");
            (networkError as any).code = "NETWORK_ERROR";
            (networkError as any).type = "TransportError";

            // First set the connection state to false to simulate disconnection
            (socketManager as any).isConnected = false;

            // Trigger the connect_error handler directly
            (socketManager as any).handleConnectError(networkError);

            addLog("CONNECT_ERROR", `Network error simulated - should trigger reconnection on ${Platform.OS}`);
        } catch (error) {
            addLog("ERROR", `Failed to simulate error: ${error}`);
        }
    }, [addLog]);

    const clearLogs = useCallback(() => {
        setConnectionLogs([]);
        setConnectionStats({
            totalConnections: 0,
            totalDisconnections: 0,
            totalReconnectAttempts: 0,
            totalErrors: 0,
            currentStreak: 0,
            longestStreak: 0,
            averageReconnectTime: 0,
        });
        addLog("SYSTEM", "Logs and stats cleared");
    }, [addLog]);

    const testReconnectionFlow = useCallback(() => {
        Alert.alert(
            "Test Reconnection Flow",
            `This will disconnect the socket and test automatic reconnection.\n\nPlatform: ${Platform.OS}\nCurrent attempts: ${reconnectAttempts}/10\n\nContinue?`,
            [
                { text: "Cancel", style: "cancel" },
                {
                    text: "Test",
                    onPress: () => {
                        setIsTestRunning(true);
                        addLog("TEST_START", `Starting reconnection flow test on ${Platform.OS}`);
                        forceDisconnect();

                        // Set a timeout to stop the test after 30 seconds
                        testTimeoutRef.current = setTimeout(() => {
                            setIsTestRunning(false);
                            addLog("TEST_END", "Reconnection test completed (30s timeout)");
                        }, 30000);
                    },
                },
            ]
        );
    }, [addLog, reconnectAttempts, forceDisconnect]);

    const runStressTest = useCallback(() => {
        Alert.alert("Stress Test", "This will perform 5 rapid connect/disconnect cycles to test stability. Continue?", [
            { text: "Cancel", style: "cancel" },
            {
                text: "Start",
                onPress: () => {
                    setIsTestRunning(true);
                    addLog("STRESS_TEST_START", "Starting stress test - 5 cycles");

                    let cycle = 0;
                    const maxCycles = 5;

                    const runCycle = () => {
                        if (cycle >= maxCycles) {
                            setIsTestRunning(false);
                            addLog("STRESS_TEST_END", "Stress test completed");
                            return;
                        }

                        cycle++;
                        addLog("STRESS_TEST_CYCLE", `Cycle ${cycle}/${maxCycles} - Disconnecting`);
                        forceDisconnect();

                        setTimeout(() => {
                            addLog("STRESS_TEST_CYCLE", `Cycle ${cycle}/${maxCycles} - Reconnecting`);
                            forceConnect();

                            setTimeout(runCycle, 3000); // Wait 3s between cycles
                        }, 2000); // Wait 2s before reconnecting
                    };

                    runCycle();
                },
            },
        ]);
    }, [addLog, forceDisconnect, forceConnect]);

    const testNetworkTimeoutScenario = useCallback(() => {
        Alert.alert(
            "Network Timeout Test",
            `This will simulate a network timeout error and test automatic reconnection on ${Platform.OS}.\n\nExpected behavior:\n• Error logged\n• Automatic reconnection attempts\n• Platform-specific delays\n\nContinue?`,
            [
                { text: "Cancel", style: "cancel" },
                {
                    text: "Test",
                    onPress: () => {
                        setIsTestRunning(true);
                        addLog("TIMEOUT_TEST_START", `Starting network timeout test on ${Platform.OS}`);

                        // Simulate network timeout
                        simulateNetworkError();

                        // Set timeout to end test
                        testTimeoutRef.current = setTimeout(() => {
                            setIsTestRunning(false);
                            addLog("TIMEOUT_TEST_END", "Network timeout test completed");
                        }, 15000); // 15 second test
                    },
                },
            ]
        );
    }, [addLog, simulateNetworkError]);

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>Socket Reconnection Tester</Text>
                <View style={styles.statusContainer}>
                    <View
                        style={[
                            styles.statusIndicator,
                            { backgroundColor: isConnected ? colors.green[500] : colors.red[500] },
                        ]}
                    />
                    <Text style={styles.statusText}>{isConnected ? "Connected" : "Disconnected"}</Text>
                    {reconnectAttempts > 0 && (
                        <Text style={styles.attemptText}>Reconnect attempts: {reconnectAttempts}</Text>
                    )}
                </View>

                {/* Connection Stats */}
                <View style={styles.statsContainer}>
                    <Text style={styles.statsTitle}>Connection Statistics</Text>
                    <View style={styles.statsRow}>
                        <Text style={styles.statLabel}>Connections: {connectionStats.totalConnections}</Text>
                        <Text style={styles.statLabel}>Disconnections: {connectionStats.totalDisconnections}</Text>
                    </View>
                    <View style={styles.statsRow}>
                        <Text style={styles.statLabel}>Errors: {connectionStats.totalErrors}</Text>
                        <Text style={styles.statLabel}>
                            Reconnect Attempts: {connectionStats.totalReconnectAttempts}
                        </Text>
                    </View>
                    <Text style={styles.statLabel}>Current Streak: {connectionStats.currentStreak}</Text>
                </View>
            </View>

            <View style={styles.buttonContainer}>
                <TextButton
                    label="Force Disconnect"
                    onPress={forceDisconnect}
                    color={colors.red[500]}
                    disabled={!isConnected || isTestRunning}
                />
                <TextButton
                    label="Force Connect"
                    onPress={forceConnect}
                    color={colors.green[500]}
                    disabled={isConnected || isTestRunning}
                />
                <TextButton
                    label="Simulate Network Error"
                    onPress={simulateNetworkError}
                    color={colors.orange[500]}
                    disabled={isTestRunning}
                />
                <TextButton
                    label="Test Reconnection Flow"
                    onPress={testReconnectionFlow}
                    color={colors.blue[500]}
                    disabled={isTestRunning}
                />
                <TextButton
                    label="Run Stress Test"
                    onPress={runStressTest}
                    color={colors.primary[500]}
                    disabled={isTestRunning}
                />
                <TextButton
                    label="Test Network Timeout"
                    onPress={testNetworkTimeoutScenario}
                    color={colors.secondary[500]}
                    disabled={isTestRunning}
                />
                <TextButton
                    label="Clear Logs & Stats"
                    onPress={clearLogs}
                    color={colors.gray[500]}
                    disabled={isTestRunning}
                />
            </View>

            <View style={styles.logsContainer}>
                <Text style={styles.logsTitle}>Connection Logs ({connectionLogs.length}/100)</Text>
                {connectionLogs.length === 0 ? (
                    <Text style={styles.noLogs}>No logs yet. Interact with the socket to see events.</Text>
                ) : (
                    connectionLogs.map((log) => (
                        <View key={log.id} style={[styles.logItem, getLogItemStyle(log.event)]}>
                            <View style={styles.logHeader}>
                                <Text style={styles.logTimestamp}>{log.timestamp}</Text>
                                <Text style={[styles.logEvent, getEventStyle(log.event)]}>{log.event}</Text>
                                {log.platform && <Text style={styles.logPlatform}>{log.platform}</Text>}
                            </View>
                            <Text style={styles.logDetails}>{log.details}</Text>
                            {log.reconnectCount !== undefined && log.reconnectCount > 0 && (
                                <Text style={styles.logReconnectCount}>Attempt: {log.reconnectCount}</Text>
                            )}
                        </View>
                    ))
                )}
            </View>
        </ScrollView>
    );
};

const getEventStyle = (event: string) => {
    switch (event) {
        case "CONNECT":
        case "INITIAL_STATE":
            return { color: colors.green[600] };
        case "DISCONNECT":
        case "CONNECT_ERROR":
        case "RECONNECT_FAILED":
        case "ERROR":
            return { color: colors.red[600] };
        case "RECONNECT_ATTEMPT":
            return { color: colors.orange[600] };
        case "MANUAL_ACTION":
            return { color: colors.blue[600] };
        case "TEST_START":
        case "TEST_END":
        case "STRESS_TEST_START":
        case "STRESS_TEST_END":
        case "STRESS_TEST_CYCLE":
            return { color: colors.primary[600] };
        case "SYSTEM":
            return { color: colors.gray[600] };
        default:
            return { color: colors.gray[600] };
    }
};

const getLogItemStyle = (event: string) => {
    switch (event) {
        case "CONNECT":
            return { borderLeftColor: colors.green[500] };
        case "DISCONNECT":
        case "CONNECT_ERROR":
        case "RECONNECT_FAILED":
        case "ERROR":
            return { borderLeftColor: colors.red[500] };
        case "RECONNECT_ATTEMPT":
            return { borderLeftColor: colors.orange[500] };
        case "TEST_START":
        case "STRESS_TEST_START":
            return { borderLeftColor: colors.primary[500] };
        default:
            return { borderLeftColor: colors.blue[500] };
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
        padding: 16,
    },
    header: {
        marginBottom: 20,
    },
    title: {
        ...typography.xl,
        ...typography.fontBold,
        color: colors.gray[900],
        marginBottom: 12,
    },
    statusContainer: {
        flexDirection: "row",
        alignItems: "center",
        gap: 8,
        marginBottom: 16,
    },
    statusIndicator: {
        width: 12,
        height: 12,
        borderRadius: 6,
    },
    statusText: {
        ...typography.md,
        ...typography.fontSemibold,
    },
    attemptText: {
        ...typography.xs,
        color: colors.orange[600],
        marginLeft: 8,
    },
    statsContainer: {
        backgroundColor: colors.gray[50],
        padding: 12,
        borderRadius: 8,
        marginTop: 8,
    },
    statsTitle: {
        ...typography.md,
        ...typography.fontSemibold,
        color: colors.gray[900],
        marginBottom: 8,
    },
    statsRow: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 4,
    },
    statLabel: {
        ...typography.sm,
        color: colors.gray[700],
    },
    buttonContainer: {
        gap: 12,
        marginBottom: 24,
    },
    logsContainer: {
        flex: 1,
    },
    logsTitle: {
        ...typography.lg,
        ...typography.fontSemibold,
        color: colors.gray[900],
        marginBottom: 12,
    },
    noLogs: {
        ...typography.sm,
        color: colors.gray[500],
        textAlign: "center",
        padding: 20,
    },
    logItem: {
        backgroundColor: colors.gray[50],
        padding: 12,
        marginBottom: 8,
        borderRadius: 8,
        borderLeftWidth: 3,
        borderLeftColor: colors.blue[500],
    },
    logHeader: {
        flexDirection: "row",
        alignItems: "center",
        marginBottom: 4,
        gap: 8,
    },
    logTimestamp: {
        ...typography.xs,
        color: colors.gray[500],
    },
    logEvent: {
        ...typography.sm,
        ...typography.fontSemibold,
    },
    logPlatform: {
        ...typography.xs,
        color: colors.gray[400],
        backgroundColor: colors.gray[200],
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 4,
    },
    logDetails: {
        ...typography.sm,
        color: colors.gray[700],
        marginBottom: 4,
    },
    logReconnectCount: {
        ...typography.xs,
        color: colors.orange[600],
        fontStyle: "italic",
    },
});

export default SocketReconnectTester;
