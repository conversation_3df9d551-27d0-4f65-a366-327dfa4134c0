# Socket Debugging Guide

## 🔍 **Comprehensive Socket Debugging with SocketReconnectTester**

This guide provides step-by-step instructions for debugging socket connection issues using the SocketReconnectTester component.

## 🚨 **Common Socket Issues & Solutions**

### **1. Socket Not Connecting Initially**

#### **Symptoms**

-   Red status indicator on app start
-   "Socket is disconnected" in initial state log
-   No connection attempts visible

#### **Debugging Steps**

1. **Check Server Status**

    ```
    • Verify server is running
    • Check server logs for connection attempts
    • Test server endpoint manually
    ```

2. **Use SocketReconnectTester**

    ```
    • Open the tester
    • Check initial state log
    • Try "Force Connect" button
    • Monitor logs for error messages
    ```

3. **Check Network Configuration**
    ```
    • Verify socket URL is correct
    • Check firewall settings
    • Test on different networks (WiFi vs cellular)
    ```

#### **Expected Behavior**

```
INITIAL_STATE: Socket is disconnected
MANUAL_ACTION: Forcing socket connect
CONNECT: Socket connected successfully
```

### **2. Reconnection Not Working After Network Issues**

#### **Symptoms**

-   Socket disconnects but doesn't reconnect
-   No reconnection attempts in logs
-   Connection stays red after network recovery

#### **Debugging Steps**

1. **Test Network Error Simulation**

    ```
    • Click "Simulate Network Error"
    • Watch for CONNECT_ERROR log
    • Monitor for RECONNECT_ATTEMPT logs
    • Check platform-specific behavior
    ```

2. **Check Platform Differences**

    ```
    iOS Expected:
    • CONNECT_ERROR: Network timeout - simulated error
    • RECONNECT_ATTEMPT: #1 (Platform: ios) - 1s delay
    • RECONNECT_ATTEMPT: #2 (Platform: ios) - 2s delay
    • RECONNECT_ATTEMPT: #3 (Platform: ios) - 4s delay

    Android Expected:
    • CONNECT_ERROR: Network timeout - simulated error
    • RECONNECT_ATTEMPT: #1 (Platform: android) - 2s delay
    • RECONNECT_ATTEMPT: #2 (Platform: android) - 2s delay
    ```

3. **Test Real Network Interruption**
    ```
    • Start "Test Reconnection Flow"
    • Turn off WiFi/cellular during test
    • Turn network back on
    • Monitor reconnection attempts
    ```

#### **Common Fixes**

-   Ensure `handleConnectError` method triggers reconnection for both platforms
-   Verify maximum retry attempts (10) are not exceeded
-   Check timer cleanup and management

### **3. Excessive Reconnection Attempts**

#### **Symptoms**

-   Reconnection counter keeps increasing
-   Multiple rapid reconnection attempts
-   Performance degradation

#### **Debugging Steps**

1. **Monitor Reconnection Pattern**

    ```
    • Watch reconnection attempt logs
    • Check timing between attempts
    • Verify exponential backoff (iOS) or fixed delay (Android)
    ```

2. **Check Maximum Attempts**

    ```
    • Let reconnection attempts reach maximum (10)
    • Verify "All reconnection attempts failed" message
    • Check if attempts reset after successful connection
    ```

3. **Test Attempt Reset**
    ```
    • Force disconnect
    • Let some reconnection attempts occur
    • Force connect manually
    • Verify attempt counter resets to 0
    ```

#### **Expected Behavior**

```
RECONNECT_ATTEMPT: #1 (Platform: ios)
RECONNECT_ATTEMPT: #2 (Platform: ios)
...
RECONNECT_ATTEMPT: #10 (Platform: ios)
RECONNECT_FAILED: All reconnection attempts failed - max attempts reached
```

### **4. Platform-Specific Issues**

#### **iOS Issues**

-   **Exponential Backoff Not Working**

    ```
    Expected delays: 1s, 2s, 4s, 8s, 10s (max)
    Check logs for actual timing
    ```

-   **Force Disconnect Strategy**
    ```
    iOS should disconnect before reconnecting
    Check for disconnect calls in logs
    ```

#### **Android Issues**

-   **Fixed Delay Not Consistent**

    ```
    Expected: 2s delay between attempts
    Check timing in logs
    ```

-   **Direct Reconnection**
    ```
    Android should connect directly without disconnect
    Monitor connection strategy
    ```

### **5. Memory Leaks & Performance Issues**

#### **Symptoms**

-   App becomes slow during socket operations
-   Memory usage increases over time
-   Event listeners not cleaned up

#### **Debugging Steps**

1. **Run Stress Test**

    ```
    • Execute "Run Stress Test" (5 cycles)
    • Monitor app performance
    • Check for memory warnings
    • Verify test completes successfully
    ```

2. **Check Event Listener Cleanup**

    ```
    • Navigate away from tester
    • Return to tester
    • Verify logs still work correctly
    • Check for duplicate event handlers
    ```

3. **Monitor Long-term Stability**
    ```
    • Leave tester open for extended period
    • Perform various connection operations
    • Monitor memory usage
    • Check for resource leaks
    ```

## 🧪 **Systematic Debugging Workflow**

### **Step 1: Initial Assessment**

1. Open SocketReconnectTester
2. Check initial connection state
3. Review connection statistics
4. Clear logs for fresh start

### **Step 2: Basic Functionality Test**

1. Test "Force Disconnect" → Should show red status
2. Test "Force Connect" → Should show green status
3. Verify logs show expected events
4. Check statistics update correctly

### **Step 3: Error Simulation**

1. Click "Simulate Network Error"
2. Monitor error handling
3. Watch for reconnection attempts
4. Verify platform-specific behavior

### **Step 4: Automated Testing**

1. Run "Test Reconnection Flow"
2. Monitor 30-second test cycle
3. Check for successful reconnection
4. Review test completion logs

### **Step 5: Stress Testing**

1. Execute "Run Stress Test"
2. Monitor 5-cycle rapid testing
3. Check for any failures
4. Verify performance stability

### **Step 6: Real-world Testing**

1. Test with actual network interruptions
2. Try different network conditions
3. Test background/foreground transitions
4. Verify behavior on both platforms

## 📊 **Log Analysis Patterns**

### **Healthy Connection Pattern**

```
INITIAL_STATE: Socket is already connected
[Normal operation - no logs unless events occur]
```

### **Normal Disconnection/Reconnection**

```
DISCONNECT: Socket disconnected
RECONNECT_ATTEMPT: Reconnection attempt #1 (Platform: ios)
CONNECT: Socket connected successfully (reconnect took 1050ms)
```

### **Network Error Recovery**

```
CONNECT_ERROR: Connection error: Network timeout
RECONNECT_ATTEMPT: Reconnection attempt #1 (Platform: ios)
RECONNECT_ATTEMPT: Reconnection attempt #2 (Platform: ios)
CONNECT: Socket connected successfully (reconnect took 3200ms)
```

### **Maximum Attempts Reached**

```
RECONNECT_ATTEMPT: Reconnection attempt #10 (Platform: ios)
RECONNECT_FAILED: All reconnection attempts failed - max attempts reached
```

## 🔧 **Advanced Debugging Techniques**

### **1. Console Debugging**

Enable additional console logging in socket manager:

```typescript
// Temporarily add to socketManager.ts for debugging
console.log("Socket state:", this.isConnected);
console.log("Reconnect attempts:", this.reconnectAttempts);
console.log("Platform:", Platform.OS);
```

### **2. Network Condition Testing**

-   Test on different networks (WiFi, cellular, poor connection)
-   Use network throttling tools
-   Test with VPN connections
-   Try different geographic locations

### **3. Server-Side Debugging**

-   Monitor server logs during client testing
-   Check server connection limits
-   Verify server socket.io configuration
-   Test server restart scenarios

### **4. Production Debugging**

-   Include SocketReconnectTester in debug builds
-   Use for customer issue reproduction
-   Monitor connection patterns in different environments
-   Document findings for team reference

## 🚨 **Emergency Debugging Checklist**

When socket connections are completely broken:

1. **✅ Check server status** - Is the server running?
2. **✅ Verify network connectivity** - Can the device reach the internet?
3. **✅ Test socket URL** - Is the endpoint correct?
4. **✅ Check platform differences** - Does it work on one platform but not the other?
5. **✅ Review recent changes** - What changed since it last worked?
6. **✅ Test with SocketReconnectTester** - Use all available test functions
7. **✅ Check console logs** - Look for error messages
8. **✅ Test on different devices** - Is it device-specific?
9. **✅ Verify socket.io versions** - Are client and server compatible?
10. **✅ Test in different environments** - Development vs production

## 📞 **Getting Help**

If you're still experiencing issues after following this guide:

1. **Document the problem** with SocketReconnectTester logs
2. **Include platform information** (iOS/Android)
3. **Provide reproduction steps** using the tester
4. **Share connection statistics** from the tester
5. **Include server logs** if available

The SocketReconnectTester provides comprehensive debugging capabilities to identify and resolve socket connection issues efficiently! 🔍
