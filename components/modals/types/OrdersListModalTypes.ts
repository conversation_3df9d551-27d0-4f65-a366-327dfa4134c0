import { Order } from "@components/orders/types";
import { Status } from "../../../types/Order";

/**
 * Modal operation modes
 */
export type ModalMode = "edit" | "delete" | "send" | "restore";

/**
 * Props for OrdersListModal component
 */
export interface OrdersListModalProps {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    mode: ModalMode;
    orderIds: string[];
    onSuccess?: () => void;
    onCancel?: () => void;
    onClear?: () => void;
    setLoading: (loading: boolean) => void;
}

/**
 * Form data for editing orders
 */
export interface OrderEditFormData {
    status: Status;
    deliveryCompany?: string;
    attempt: number;
    rejectionReason?: string;
    note?: string;
}

/**
 * Rejection reason options
 */
export type RejectionReason = 
    | "busy"
    | "expensive" 
    | "didntClick"
    | "betterPrice"
    | "expensiveDelivery"
    | "other";

/**
 * Rejection reason option for UI
 */
export interface RejectionReasonOption {
    value: string;
    label: string;
}

/**
 * API response for order operations
 */
export interface OrderOperationResponse {
    success: boolean;
    message: string;
    data?: Order[];
}

/**
 * Modal state interface
 */
export interface ModalState {
    deliveryVisible: boolean;
    statusVisible: boolean;
    loading: boolean;
    error?: string | null;
}

/**
 * Order selection state
 */
export interface OrderSelectionState {
    selectedOrders: Order[];
    orderIds: string[];
}

/**
 * Constants for rejection reasons
 */
export const REJECTION_REASONS: RejectionReasonOption[] = [
    {
        value: "busy",
        label: "Not Available 📞",
    },
    {
        value: "expensive",
        label: "Expensive 💰",
    },
    {
        value: "didntClick",
        label: "Didn't Click buy 👆",
    },
    {
        value: "betterPrice",
        label: "Better Price 🫰",
    },
    {
        value: "expensiveDelivery",
        label: "Expensive Delivery 🚚",
    },
    {
        value: "other",
        label: "Other 🤔",
    },
];

/**
 * Constants for modal configuration
 */
export const MODAL_CONFIG = {
    WIDTH: 350,
    DEFAULT_ATTEMPT: 1,
    DEFAULT_STATUS: "confirmed" as Status,
    SELLMAX_LOGO_URL: "https://cdn.converty.shop/assets/integrations/sellmax.webp",
} as const;
