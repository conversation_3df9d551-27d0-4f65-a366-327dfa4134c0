import api from "@api/api";
import React from "react";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import ModalBase from "@components/Navigation/ModalView/ModalBase";
import SelectCustom from "@components/Navigation/ModalView/SelectCustom";
import { DeliveryCompanyLogo } from "@components/Navigation/ModalView/SelectDeliveryCompany";
import SelectDeliveryCompanyInput from "@components/Navigation/ModalView/SelectDeliveryCompanyInput";
import SelectStatusInput from "@components/Navigation/ModalView/SelectStatusInput";
import { Order } from "@components/orders/types";
import { typography } from "@styles/typography";
import { useState, useEffect } from "react";
import { View, TouchableOpacity, Platform, Modal } from "react-native";
import Toast from "react-native-toast-message";
import { useOrderStore, useOrderActionsStore, useOrdersStore } from "../../store/orders";
import { Status } from "../../types/Order";
import { Text } from "react-native";
import colors from "@styles/colors";
import Input from "@components/inputs/textInputs/Input";

type ModalMode = "edit" | "delete" | "send" | "restore";

export const OrdersListModal = ({
    visible,
    setVisible,
    mode,
    orderIds,
    onSuccess,
    onCancel,
    onClear,
    setLoading,
}: {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    mode: ModalMode;
    orderIds: string[];
    onSuccess?: () => void;
    onCancel?: () => void;
    onClear?: () => void;
    setLoading: (loading: boolean) => void;
}) => {
    const { orders, setOrders } = useOrderStore();
    const [selectedOrders, setSelectedOrders] = useState<Order[]>([]);

    const handleCancel = () => {
        console.log('Modal handleCancel called');
        onCancel?.(); // This should call the main component's handleModalCancel
        setVisible(false);
    };

    useEffect(() => {
        if (orderIds.length > 0) {
            const selected = orders.filter((order) => orderIds.includes(order._id));
            setSelectedOrders(selected);
        }
    }, [orderIds, orders]);

    const [deliveryVisible, setDeliveryVisible] = useState(false);
    const [statusVisble, setSstatusVisble] = useState(false);
    const [attempt, setAttempt] = useState(1);
    const [deliveryCompany, setDeliveryCompany] = useState<string>();
    const [status, setStatus] = useState<Status>("confirmed");

    const handleSendLeads = async () => {
        try {
            // setLoadingModal(true);
            const response = await api.post("/order/send-leads", {
                references: Array.from(
                    selectedOrders.map((order) => {
                        return order.reference;
                    })
                ),
            });
            if (response.success) onSuccess?.();
            Toast.show({ text1: "Leads Sent", text2: response.message });

            return { orders: response.data };
        } catch (error: any) {
            console.error("Error :", error);
            Toast.show({ text1: "Failed to Send", text2: error.message, type: "error" });

            return { orders: [] };
        } finally {
            // setLoadingModal(false);
        }
    };

    const [rejectionReason, setRejectionReason] = useState<string>();
    const [note, setNote] = useState<string>();

    const handleEdit = async () => {
        try {
            // setLoadingModal(true);
            const body =
                status === "attempt"
                    ? { status, attempt, note }
                    : status === "rejected"
                    ? { status, customer: { rejectionReason }, note }
                    : { status, deliveryCompany, note };
            const response = await api.patch("/order", {
                references: selectedOrders.map((order) => {
                    return order.reference;
                }),
                ...body,
            });
            console.log('bulk edit body =>', body);
            if (response.success) {
                useOrdersStore.getState().updateOrderInList(response.data[0].reference, response.data[0]);
                onClear?.();
                Toast.show({ type: "success", text1: "Orders Updated", text2: response.message });
            } else {
                Toast.show({ type: "error", text1: "Orders Update Failed", text2: response.message });
            }
            return { orders: response.data };
        } catch (error) {
            console.error("Error :", error);
            Toast.show({ text1: "Failed to Update Order", text2: error.message, type: "error" });

            return { orders: [] };
        } finally {
            // setLoadingModal(false);
            setNote(undefined);
            setAttempt(1);
            setDeliveryCompany(undefined);
            setRejectionReason(undefined);
            setStatus("confirmed");
        }
    };

    const handleDelete = async () => {
        try {
            onCancel?.(); // Close swipeable first
            setVisible(false);
            setLoading(true);

            const response = await api.delete(`/order/${selectedOrders[0]._id}`);
            if (!response.success) {
                Toast.show({
                    type: "error",
                    text1: "Failed to Delete Order",
                    text2: response.message,
                });
            } else {
                // Use the store method to ensure consistent state updates
                //   useOrderStore.getState().findOrderAndDelete(selectedOrders[0]._id);
                // onSuccess?.();
            }
        } catch (error: any) {
            Toast.show({
                type: "error",
                text1: `Error Deleting Order ${selectedOrders[0].reference}`,
                text2: error.response?.data?.message || "Unknown error",
            });
        } finally {
            setLoading(false);
        }
    };

    const handleRestore = async () => {
        try {
            onCancel?.(); // Close swipeable first
            setVisible(false);
            setLoading(true);
            useOrderActionsStore
                .getState()
                .restoreOrder(orderIds[0])
                .then(() => {
                    onSuccess?.();
                });
        } catch (error: any) {
            Toast.show({
                type: "error",
                text1: `Error Restoring Order${orderIds.length > 1 ? "s" : ""}`,
                text2: error.response?.data?.message || "Unknown error",
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <ModalBase
            visible={visible}
            setVisible={setVisible}
            onDismiss={handleCancel} // Use handleCancel for consistency
        >
            <View style={{ width: 350 }}>
                {mode === "delete" ? (
                    <View style={{ gap: 10 }}>
                        <Text
                            style={[
                                typography.fontMedium,
                                typography.lg,
                                { color: colors.gray[800], textAlign: "center" },
                            ]}
                        >
                            Delete Order #{selectedOrders[0]?.reference}
                        </Text>
                        <Text style={[typography.fontNormal, typography.md, { color: colors.gray[800] }]}>
                            Are you sure you want to delete Order{" "}
                            <Text style={[typography.altTextMedium, { color: colors.primary[700] }]}>
                                #{selectedOrders[0]?.reference}
                            </Text>
                            ?
                        </Text>
                        <View style={{ flexDirection: "row", gap: 10 }}>
                            <TextButton
                                style={{ flex: 1 }}
                                label="Cancel"
                                variant="outlined"
                                color={colors.red[500]}
                                onPress={handleCancel}
                            />
                            <TextButton
                                style={{ flex: 1 }}
                                label="Delete"
                                variant="contained"
                                color={colors.red[500]}
                                onPress={handleDelete}
                            />
                        </View>
                    </View>
                ) : mode === "restore" ? (
                    <View style={{ gap: 10 }}>
                        <Text
                            style={[
                                typography.fontMedium,
                                typography.lg,
                                { color: colors.gray[800], textAlign: "center" },
                            ]}
                        >
                            Restore Order #{selectedOrders[0]?.reference}
                        </Text>
                        <Text style={[typography.fontNormal, typography.md, { color: colors.gray[800] }]}>
                            Are you sure you want to restore Order{" "}
                            <Text style={[typography.altTextMedium, { color: colors.primary[700] }]}>
                                #{selectedOrders[0]?.reference}
                            </Text>
                            ?
                        </Text>
                        <View style={{ flexDirection: "row", gap: 10 }}>
                            <TextButton
                                style={{ flex: 1 }}
                                label="Cancel"
                                variant="outlined"
                                color={colors.green[500]}
                                onPress={handleCancel}
                            />
                            <TextButton
                                style={{ flex: 1 }}
                                label="Restore"
                                variant="contained"
                                color={colors.green[500]}
                                onPress={handleRestore}
                            />
                        </View>
                    </View>
                ) : mode === "send" ? (
                    <View style={{ gap: 10 }}>
                        <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "center", gap: 5 }}>
                            <Text style={[typography.fontMedium, typography.lg, { color: colors.gray[800] }]}>
                                Send Orders
                            </Text>
                            <DeliveryCompanyLogo
                                source={{ uri: "https://cdn.converty.shop/assets/integrations/sellmax.webp" }}
                            />
                        </View>
                        <View>
                            <Text style={[typography.fontSemibold, typography.sm, { color: colors.gray[800] }]}>
                                Selected Orders:
                            </Text>
                            <View
                                style={{ columnGap: 5, alignItems: "baseline", flexDirection: "row", flexWrap: "wrap" }}
                            >
                                {selectedOrders.map((order, index) => {
                                    return (
                                        <Text
                                            style={[
                                                typography.altTextSemiBold,
                                                typography.md,
                                                { color: colors.primary[600] },
                                            ]}
                                            key={index}
                                        >
                                            {order.reference}
                                        </Text>
                                    );
                                })}
                            </View>
                        </View>
                        <TextButton
                            label="Confirm"
                            variant="outlined"
                            color={colors.primary[500]}
                            onPress={() => {
                                onCancel?.(); // Close swipeable first
                                setVisible(false);
                                handleSendLeads();
                            }}
                        />
                    </View>
                ) : (
                    // Edit mode
                    <View style={{ gap: 5 }}>
                        <Text
                            style={[
                                typography.fontMedium,
                                typography.lg,
                                { color: colors.gray[800], textAlign: "center" },
                            ]}
                        >
                            Edit Orders
                        </Text>
                        <View>
                            <Text style={[typography.fontSemibold, typography.sm, { color: colors.gray[800] }]}>
                                Selected Orders:
                            </Text>
                            <View
                                style={{ columnGap: 5, alignItems: "baseline", flexDirection: "row", flexWrap: "wrap" }}
                            >
                                {selectedOrders.map((order, index) => {
                                    return (
                                        <Text
                                            style={[
                                                typography.altTextSemiBold,
                                                typography.md,
                                                { color: colors.primary[600] },
                                            ]}
                                            key={index}
                                        >
                                            {order.reference}
                                        </Text>
                                    );
                                })}
                            </View>
                        </View>
                        <View style={{ flexDirection: "row", gap: 10 }}>
                            <View style={{ flex: 1, gap: 2 }}>
                                <Text style={[typography.fontSemibold, typography.xs, { color: colors.gray[800] }]}>
                                    Order Status
                                </Text>

                                <SelectStatusInput
                                    style={{ flex: 1 }}
                                    action={(status) => {
                                        setStatus(status);
                                    }}
                                    pageType="edit"
                                    status={status}
                                />
                            </View>
                            {
                                <View style={{ flex: 1, gap: 2 }}>
                                    {!(status === "rejected" || status === "attempt") && (
                                        <>
                                            <Text
                                                style={[
                                                    typography.fontSemibold,
                                                    typography.xs,
                                                    { color: colors.gray[800] },
                                                ]}
                                            >
                                                Delivery Company
                                            </Text>
                                            <SelectDeliveryCompanyInput
                                                action={(company) => {
                                                    setDeliveryCompany(company);
                                                }}
                                                deliveryCompany={deliveryCompany}
                                            />
                                        </>
                                    )}
                                    {status === "rejected" && (
                                        <>
                                            <Text
                                                style={[
                                                    typography.fontSemibold,
                                                    typography.xs,
                                                    { color: colors.gray[800] },
                                                ]}
                                            >
                                                Rejection Reasons
                                            </Text>
                                            <SelectCustom
                                                value={rejectionReason}
                                                action={(value) => {
                                                    setRejectionReason(value);
                                                }}
                                                options={[
                                                    {
                                                        value: "busy",
                                                        label: "Not Available 📞",
                                                    },
                                                    {
                                                        value: "expensive",
                                                        label: "Expensive 💰",
                                                    },
                                                    {
                                                        value: "didntClick",
                                                        label: "Didn't Click buy 👆",
                                                    },
                                                    {
                                                        value: "betterPrice",
                                                        label: "Better Price 🫰",
                                                    },
                                                    {
                                                        value: "expensiveDelivery",
                                                        label: "Expensive Delivery 🚚",
                                                    },
                                                    {
                                                        value: "other",
                                                        label: "Other 🤔",
                                                    },
                                                ]}
                                            />
                                        </>
                                    )}
                                    {status === "attempt" && (
                                        <>
                                            <Text
                                                style={[
                                                    typography.fontSemibold,
                                                    typography.xs,
                                                    { color: colors.gray[800] },
                                                ]}
                                            >
                                                Attemp Number
                                            </Text>
                                            <View
                                                style={[
                                                    {
                                                        alignSelf: "stretch",
                                                        borderWidth: 1,
                                                        borderColor: colors.gray[300],
                                                        borderRadius: 5,
                                                        minHeight: 50,
                                                        flexDirection: "row",
                                                        alignItems: "stretch",
                                                        paddingVertical: 10,
                                                    },
                                                ]}
                                            >
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        setAttempt((prev) => {
                                                            return prev + 1;
                                                        });
                                                    }}
                                                    style={{
                                                        flex: 1,
                                                        paddingHorizontal: 15,
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                    }}
                                                >
                                                    <Text
                                                        style={[
                                                            typography.fontMedium,
                                                            typography.sm,
                                                            { color: colors.gray[600] },
                                                        ]}
                                                    >
                                                        +
                                                    </Text>
                                                </TouchableOpacity>
                                                <View
                                                    style={{
                                                        borderRightWidth: 1,
                                                        borderLeftWidth: 1,
                                                        borderColor: colors.gray[300],
                                                        justifyContent: "center",
                                                        flex: 4,
                                                    }}
                                                >
                                                    <Text
                                                        style={[
                                                            typography.fontMedium,
                                                            typography.sm,
                                                            { color: colors.gray[800], alignSelf: "center" },
                                                        ]}
                                                    >
                                                        {attempt}
                                                    </Text>
                                                </View>
                                                <TouchableOpacity
                                                    onPress={
                                                        attempt > 1
                                                            ? () => {
                                                                  setAttempt((prev) => {
                                                                      return prev - 1;
                                                                  });
                                                              }
                                                            : undefined
                                                    }
                                                    style={{
                                                        flex: 1,
                                                        paddingHorizontal: 15,
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                    }}
                                                >
                                                    <Text
                                                        style={[
                                                            typography.fontMedium,
                                                            typography.sm,
                                                            { color: colors.gray[attempt <= 1 ? 400 : 600] },
                                                        ]}
                                                    >
                                                        -
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                        </>
                                    )}
                                </View>
                            }
                        </View>
                        <View style={{ paddingVertical: 10, gap: 2 }}>
                            <Text style={[typography.fontSemibold, typography.sm, { color: colors.gray[800] }]}>
                                Customer Notes:
                            </Text>
                            <Input
                                onChange={(value) => {
                                    setNote(value);
                                }}
                                noBottomPadding
                                placeholder="Customer Notes"
                                inputProps={{ multiline: true }}
                                labelStyle={[typography.fontSemibold, typography.xs, { color: colors.gray[800] }]}
                                placeholderStyle={{ fontFamily: typography.fontSemibold.fontFamily }}
                                containerStyle={{ padding: 0 }}
                                style={{
                                    flexDirection: "row",
                                    alignItems: "center",
                                    flexShrink: 1,
                                    paddingVertical: 10,
                                    borderWidth: 1,
                                    borderColor: "rgba(203, 213, 224, 1)",
                                    borderRadius: 5,
                                }}
                            />
                        </View>
                        {/* <View>
													<TextButton label="cancel" variant="text" onPress={() => {}} /> */}
                        <TextButton
                            // style={{ flex: 1 }}
                            label="Confirm"
                            variant="contained"
                            color={colors.primary[500]}
                            onPress={() => {
                                handleEdit();
                                onCancel?.(); // Close swipeable first
                                setVisible(false);
                            }}
                        />
                        {/* </View> */}
                    </View>
                )}
            </View>
        </ModalBase>
        
    );
};
