import React from "react";
import ModalBase from "@components/Navigation/ModalView/ModalBase";
import { View } from "react-native";
import { OrdersListModalProps, MODAL_CONFIG } from "./types/OrdersListModalTypes";
import { useOrderActions, useModalState, useOrderSelection } from "../../hooks/useOrdersListModal";
import { DeleteModal, RestoreModal, SendModal, EditModal } from './content';

export const OrdersListModal: React.FC<OrdersListModalProps> = ({
    visible,
    setVisible,
    mode,
    orderIds,
    onSuccess,
    onCancel,
    onClear,
    setLoading,
}) => {
    // Custom hooks
    const { selectedOrders } = useOrderSelection(orderIds);
    const {
        handleSendLeads,
        handleEditOrders,
        handleDeleteOrder,
        handleRestoreOrder,
        loading
    } = useOrderActions();
    const {
        setAttempt,
        setDeliveryCompany,
        setStatus,
        setRejectionReason,
        setNote,
        resetForm,
        getFormData,
    } = useModalState();

    // Handle cancel action
    const handleCancel = () => {
        onCancel?.();
        setVisible(false);
    };

    return (
        <ModalBase
            visible={visible}
            setVisible={setVisible}
            onDismiss={handleCancel} // Use handleCancel for consistency
        >
            <View style={{ width: MODAL_CONFIG.WIDTH }}>
                {mode === "delete" ? (
                    <DeleteModal
                        selectedOrder={selectedOrders[0]}
                        onConfirm={() => {
                            handleDeleteOrder(selectedOrders[0], onCancel, setLoading);
                            setVisible(false);
                        }}
                        onCancel={handleCancel}
                        loading={loading}
                    />
                ) : mode === "restore" ? (
                    <RestoreModal
                        selectedOrder={selectedOrders[0]}
                        onConfirm={() => {
                            handleRestoreOrder(orderIds[0], selectedOrders[0], onCancel, onSuccess, setLoading);
                            setVisible(false);
                        }}
                        onCancel={handleCancel}
                        loading={loading}
                    />
                ) : mode === "send" ? (
                    <SendModal
                        selectedOrders={selectedOrders}
                        onConfirm={async () => {
                            onCancel?.(); // Close swipeable first
                            setVisible(false);
                            const result = await handleSendLeads(selectedOrders);
                            if (result.success) {
                                onSuccess?.();
                            }
                        }}
                        onCancel={handleCancel}
                        loading={loading}
                    />
                ) : (
                    <EditModal
                        selectedOrders={selectedOrders}
                        formData={getFormData()}
                        onFormChange={(field, value) => {
                            switch (field) {
                                case 'status':
                                    setStatus(value);
                                    break;
                                case 'deliveryCompany':
                                    setDeliveryCompany(value);
                                    break;
                                case 'attempt':
                                    setAttempt(value);
                                    break;
                                case 'rejectionReason':
                                    setRejectionReason(value);
                                    break;
                                case 'note':
                                    setNote(value);
                                    break;
                            }
                        }}
                        onConfirm={async () => {
                            const result = await handleEditOrders(selectedOrders, getFormData(), onClear);
                            if (result.success) {
                                resetForm();
                            }
                            onCancel?.(); // Close swipeable first
                            setVisible(false);
                        }}
                        onCancel={handleCancel}
                        loading={loading}
                    />
                )}
            </View>
        </ModalBase>
    );
};
