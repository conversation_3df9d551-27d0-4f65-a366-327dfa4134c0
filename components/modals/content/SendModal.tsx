import React from 'react';
import { View, Image } from 'react-native';
import { Text } from 'react-native';
import colors from '@styles/colors';
import { typography } from '@styles/typography';
import TextButton from '@components/inputs/buttons/TextButton/TextButton';
import { Order } from '@components/orders/types';
import { OrderModalUtils } from '../../../utils/orderModalUtils';
import { MODAL_CONFIG } from '../types/OrdersListModalTypes';

interface SendModalProps {
    selectedOrders: Order[];
    onConfirm: () => void;
    onCancel: () => void;
    loading?: boolean;
}

export const SendModal: React.FC<SendModalProps> = ({
    selectedOrders,
    onConfirm,
    onCancel,
    loading = false
}) => {
    const title = OrderModalUtils.getModalTitle("send", selectedOrders);
    const message = OrderModalUtils.getConfirmationMessage("send", selectedOrders);
    const orderReferences = OrderModalUtils.formatOrderReferencesForDisplay(selectedOrders);

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Image 
                    source={{ uri: MODAL_CONFIG.SELLMAX_LOGO_URL }} 
                    style={styles.logo}
                    resizeMode="contain"
                />
                <Text style={styles.title}>{title}</Text>
            </View>
            
            <View style={styles.content}>
                <Text style={styles.message}>{message}</Text>
                {orderReferences && (
                    <Text style={styles.orderList}>
                        Orders: {orderReferences}
                    </Text>
                )}
                <Text style={styles.info}>
                    Orders will be sent to SellMax for lead generation.
                </Text>
            </View>
            
            <View style={styles.actions}>
                <TextButton
                    style={styles.cancelButton}
                    label="Cancel"
                    variant="outlined"
                    color={colors.gray[500]}
                    onPress={onCancel}
                    disabled={loading}
                />
                <TextButton
                    style={styles.confirmButton}
                    label="Confirm"
                    variant="outlined"
                    color={colors.primary[500]}
                    onPress={onConfirm}
                    loading={loading}
                    disabled={loading}
                />
            </View>
        </View>
    );
};

const styles = {
    container: {
        backgroundColor: colors.white,
        borderRadius: 12,
        padding: 20,
        minWidth: 300,
        maxWidth: 400,
    },
    header: {
        alignItems: 'center' as const,
        marginBottom: 16,
    },
    logo: {
        width: 60,
        height: 60,
        marginBottom: 12,
    },
    title: {
        ...typography.lg,
        ...typography.fontSemibold,
        color: colors.gray[900],
        textAlign: 'center' as const,
    },
    content: {
        marginBottom: 24,
    },
    message: {
        ...typography.md,
        ...typography.fontNormal,
        color: colors.gray[700],
        textAlign: 'center' as const,
        marginBottom: 8,
    },
    orderList: {
        ...typography.sm,
        ...typography.fontMedium,
        color: colors.primary[600],
        textAlign: 'center' as const,
        marginBottom: 8,
    },
    info: {
        ...typography.sm,
        ...typography.fontNormal,
        color: colors.gray[600],
        textAlign: 'center' as const,
        fontStyle: 'italic' as const,
    },
    actions: {
        flexDirection: 'row' as const,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
    },
    confirmButton: {
        flex: 1,
    },
};
