import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native';
import colors from '@styles/colors';
import { typography } from '@styles/typography';
import TextButton from '@components/inputs/buttons/TextButton/TextButton';
import { Order } from '@components/orders/types';

interface RestoreModalProps {
    selectedOrder: Order;
    onConfirm: () => void;
    onCancel: () => void;
    loading?: boolean;
}

export const RestoreModal: React.FC<RestoreModalProps> = ({
    selectedOrder,
    onConfirm,
    onCancel,
    loading = false
}) => {
    const orderReference = selectedOrder?.reference;

    return (
        <View style={styles.container}>
            <Text style={styles.title}>
                Restore Order{" "}
                {orderReference && (
                    <Text style={styles.orderReference}>#{orderReference}</Text>
                )}
            </Text>
            <Text style={styles.description}>
                Are you sure you want to restore Order{" "}
                <Text style={styles.orderReference}>#{orderReference}</Text>?
            </Text>
            <View style={styles.buttonContainer}>
                <TextButton
                    style={styles.button}
                    label="Cancel"
                    variant="outlined"
                    color={colors.green[500]}
                    onPress={onCancel}
                    disabled={loading}
                />
                <TextButton
                    style={styles.button}
                    label="Restore"
                    variant="contained"
                    color={colors.green[500]}
                    onPress={onConfirm}
                    loading={loading}
                    disabled={loading}
                />
            </View>
        </View>
    );
};

const styles = {
    container: {
        gap: 10,
    },
    title: {
        ...typography.fontMedium,
        ...typography.lg,
        color: colors.gray[800],
        textAlign: 'center' as const,
    },
    description: {
        ...typography.fontNormal,
        ...typography.md,
        color: colors.gray[800],
    },
    orderReference: {
        ...typography.altTextMedium,
        color: colors.primary[700],
    },
    buttonContainer: {
        flexDirection: 'row' as const,
        gap: 10,
    },
    button: {
        flex: 1,
    },
};
