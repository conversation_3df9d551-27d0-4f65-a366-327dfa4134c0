import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native';
import colors from '@styles/colors';
import { typography } from '@styles/typography';
import TextButton from '@components/buttons/TextButton';
import { Order } from '@components/orders/types';
import { OrderModalUtils } from '../../../utils/orderModalUtils';

interface RestoreModalProps {
    selectedOrder: Order;
    onConfirm: () => void;
    onCancel: () => void;
    loading?: boolean;
}

export const RestoreModal: React.FC<RestoreModalProps> = ({
    selectedOrder,
    onConfirm,
    onCancel,
    loading = false
}) => {
    const title = OrderModalUtils.getModalTitle("restore", [selectedOrder]);
    const message = OrderModalUtils.getConfirmationMessage("restore", [selectedOrder]);

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>{title}</Text>
            </View>
            
            <View style={styles.content}>
                <Text style={styles.message}>{message}</Text>
                <Text style={styles.info}>
                    The order will be restored to its previous state.
                </Text>
            </View>
            
            <View style={styles.actions}>
                <TextButton
                    style={styles.cancelButton}
                    label="Cancel"
                    variant="outlined"
                    color={colors.gray[500]}
                    onPress={onCancel}
                    disabled={loading}
                />
                <TextButton
                    style={styles.confirmButton}
                    label="Restore"
                    variant="contained"
                    color={colors.green[500]}
                    onPress={onConfirm}
                    loading={loading}
                    disabled={loading}
                />
            </View>
        </View>
    );
};

const styles = {
    container: {
        backgroundColor: colors.white,
        borderRadius: 12,
        padding: 20,
        minWidth: 300,
        maxWidth: 400,
    },
    header: {
        marginBottom: 16,
    },
    title: {
        ...typography.h3,
        color: colors.gray[900],
        textAlign: 'center' as const,
    },
    content: {
        marginBottom: 24,
    },
    message: {
        ...typography.body1,
        color: colors.gray[700],
        textAlign: 'center' as const,
        marginBottom: 8,
    },
    info: {
        ...typography.caption,
        color: colors.green[600],
        textAlign: 'center' as const,
        fontStyle: 'italic' as const,
    },
    actions: {
        flexDirection: 'row' as const,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
    },
    confirmButton: {
        flex: 1,
    },
};
