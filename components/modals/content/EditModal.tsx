import React from 'react';
import { View, ScrollView } from 'react-native';
import { Text } from 'react-native';
import colors from '@styles/colors';
import { typography } from '@styles/typography';
import Input from '@components/inputs/textInputs/Input';
import SelectStatusInput from '@components/Navigation/ModalView/SelectStatusInput';
import { Order } from '@components/orders/types';
import { OrderEditFormData, REJECTION_REASONS } from '../types/OrdersListModalTypes';
import { OrderModalUtils } from '../../../utils/orderModalUtils';
import SelectCustom from '@components/Navigation/ModalView/SelectCustom';
import TextButton from '@components/inputs/buttons/TextButton/TextButton';

interface EditModalProps {
    selectedOrders: Order[];
    formData: OrderEditFormData;
    onFormChange: (field: keyof OrderEditFormData, value: any) => void;
    onConfirm: () => void;
    onCancel: () => void;
    loading?: boolean;
}

export const EditModal: React.FC<EditModalProps> = ({
    selectedOrders,
    formData,
    onFormChange,
    onConfirm,
    onCancel,
    loading = false
}) => {
    const title = OrderModalUtils.getModalTitle("edit", selectedOrders);
    const fieldRequirements = OrderModalUtils.getFieldRequirements(formData.status);

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>{title}</Text>
                <Text style={styles.subtitle}>
                    {selectedOrders.length} order{selectedOrders.length > 1 ? 's' : ''} selected
                </Text>
            </View>
            
            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                {/* Status Selection */}
                <View style={styles.field}>
                    <Text style={styles.label}>Status *</Text>
                    <SelectStatusInput
                        status={formData.status}
                        action={(value: string) => onFormChange('status', value)}
                        pageType="edit"
                    />
                </View>

                {/* Delivery Company - shown for most statuses except rejected and attempt */}
                {fieldRequirements.requiresDeliveryCompany && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Delivery Company *</Text>
                        <Input
                            onChange={(value: string) => onFormChange('deliveryCompany', value)}
                            placeholder="Enter delivery company"
                            style={styles.input}
                            inputProps={{ value: formData.deliveryCompany || '' }}
                        />
                    </View>
                )}

                {/* Attempt Number - shown only for attempt status */}
                {fieldRequirements.requiresAttempt && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Attempt Number *</Text>
                        <Input
                            onChange={(value: string) => onFormChange('attempt', parseInt(value) || 1)}
                            placeholder="Enter attempt number"
                            style={styles.input}
                            inputProps={{
                                value: formData.attempt?.toString() || '',
                                keyboardType: 'numeric'
                            }}
                        />
                    </View>
                )}

                {/* Rejection Reason - shown only for rejected status */}
                {fieldRequirements.requiresRejectionReason && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Rejection Reason *</Text>
                        <SelectCustom
                            value={formData.rejectionReason || ''}
                            action={(value) => onFormChange('rejectionReason', value)}
                            options={REJECTION_REASONS}
                        />
                    </View>
                )}

                {/* Note - always shown */}
                {fieldRequirements.allowsNote && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Note</Text>
                        <Input
                            onChange={(value: string) => onFormChange('note', value)}
                            placeholder="Add a note (optional)"
                            style={styles.input}
                            inputProps={{
                                value: formData.note || '',
                                multiline: true,
                                numberOfLines: 3,
                                style: styles.textArea
                            }}
                        />
                    </View>
                )}
            </ScrollView>
            
            <View style={styles.actions}>
                <TextButton
                    style={styles.cancelButton}
                    label="Cancel"
                    variant="outlined"
                    color={colors.gray[500]}
                    onPress={onCancel}
                    disabled={loading}
                />
                <TextButton
                    style={styles.confirmButton}
                    label="Confirm"
                    variant="contained"
                    color={colors.primary[500]}
                    onPress={onConfirm}
                    loading={loading}
                    disabled={loading}
                />
            </View>
        </View>
    );
};

const styles = {
    container: {
        paddingVertical: 20,
        paddingHorizontal: 10,
        gap: 20,
        backgroundColor: colors.gray[100],
    },
    header: {
        backgroundColor: colors.white,
        borderRadius: 13,
        padding: 15,
        gap: 10,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 5,
        elevation: 3,
        paddingVertical: 20,
        alignItems: 'center' as const,
    },
    title: {
        fontSize: 20,
        fontWeight: "bold" as const,
        color: colors.primary[500],
        textAlign: "center" as const,
    },
    subtitle: {
        ...typography.sm,
        ...typography.fontNormal,
        color: colors.gray[600],
        textAlign: 'center' as const,
    },
    content: {
        backgroundColor: colors.white,
        borderRadius: 13,
        padding: 15,
        gap: 10,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 5,
        elevation: 3,
        paddingVertical: 20,
    },
    field: {
        width: "100%" as const,
        backgroundColor: colors.white,
        gap: 5,
        borderRadius: 5,
        paddingVertical: 5,
    },
    label: {
        fontSize: 14,
        fontWeight: "bold" as const,
        color: colors.gray[800],
    },
    input: {
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.black,
        borderWidth: 0.5,
        borderColor: colors.gray[400],
        borderRadius: 5,
        padding: 10,
        height: 50,
    },
    textArea: {
        minHeight: 80,
        textAlignVertical: 'top' as const,
    },
    actions: {
        flexDirection: "column" as const,
        justifyContent: "space-between" as const,
        gap: 10,
    },
    cancelButton: {
        flex: 1,
    },
    confirmButton: {
        flex: 1,
    },
};
