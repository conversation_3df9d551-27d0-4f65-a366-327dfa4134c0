import React from 'react';
import { View, ScrollView } from 'react-native';
import { Text } from 'react-native';
import colors from '@styles/colors';
import { typography } from '@styles/typography';
import Input from '@components/inputs/textInputs/Input';
import SelectStatusInput from '@components/Navigation/ModalView/SelectStatusInput';
import { Order } from '@components/orders/types';
import { OrderEditFormData, REJECTION_REASONS } from '../types/OrdersListModalTypes';
import { OrderModalUtils } from '../../../utils/orderModalUtils';
import SelectCustom from '@components/Navigation/ModalView/SelectCustom';
import TextButton from '@components/inputs/buttons/TextButton/TextButton';

interface EditModalProps {
    selectedOrders: Order[];
    formData: OrderEditFormData;
    onFormChange: (field: keyof OrderEditFormData, value: any) => void;
    onConfirm: () => void;
    onCancel: () => void;
    loading?: boolean;
}

export const EditModal: React.FC<EditModalProps> = ({
    selectedOrders,
    formData,
    onFormChange,
    onConfirm,
    onCancel,
    loading = false
}) => {
    const title = OrderModalUtils.getModalTitle("edit", selectedOrders);
    const fieldRequirements = OrderModalUtils.getFieldRequirements(formData.status);

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>{title}</Text>
                <Text style={styles.subtitle}>
                    {selectedOrders.length} order{selectedOrders.length > 1 ? 's' : ''} selected
                </Text>
            </View>
            
            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                {/* Status Selection */}
                <View style={styles.field}>
                    <Text style={styles.label}>Status *</Text>
                    <SelectStatusInput
                        status={formData.status}
                        action={(value) => onFormChange('status', value)}
                        pageType="edit"
                    />
                </View>

                {/* Delivery Company - shown for most statuses except rejected and attempt */}
                {fieldRequirements.requiresDeliveryCompany && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Delivery Company *</Text>
                        <Input
                            onChange={(value: string) => onFormChange('deliveryCompany', value)}
                            placeholder="Enter delivery company"
                            style={styles.input}
                            inputProps={{ value: formData.deliveryCompany || '' }}
                        />
                    </View>
                )}

                {/* Attempt Number - shown only for attempt status */}
                {fieldRequirements.requiresAttempt && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Attempt Number *</Text>
                        <Input
                            onChange={(value: string) => onFormChange('attempt', parseInt(value) || 1)}
                            placeholder="Enter attempt number"
                            style={styles.input}
                            inputProps={{
                                value: formData.attempt?.toString() || '',
                                keyboardType: 'numeric'
                            }}
                        />
                    </View>
                )}

                {/* Rejection Reason - shown only for rejected status */}
                {fieldRequirements.requiresRejectionReason && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Rejection Reason *</Text>
                        <SelectCustom
                            value={formData.rejectionReason || ''}
                            action={(value) => onFormChange('rejectionReason', value)}
                            options={REJECTION_REASONS}
                        />
                    </View>
                )}

                {/* Note - always shown */}
                {fieldRequirements.allowsNote && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Note</Text>
                        <Input
                            onChange={(value: string) => onFormChange('note', value)}
                            placeholder="Add a note (optional)"
                            style={styles.input}
                            inputProps={{
                                value: formData.note || '',
                                multiline: true,
                                numberOfLines: 3,
                                style: styles.textArea
                            }}
                        />
                    </View>
                )}
            </ScrollView>
            
            <View style={styles.actions}>
                <TextButton
                    style={styles.cancelButton}
                    label="Cancel"
                    variant="outlined"
                    color={colors.gray[500]}
                    onPress={onCancel}
                    disabled={loading}
                />
                <TextButton
                    style={styles.confirmButton}
                    label="Confirm"
                    variant="contained"
                    color={colors.primary[500]}
                    onPress={onConfirm}
                    loading={loading}
                    disabled={loading}
                />
            </View>
        </View>
    );
};

const styles = {
    container: {
        minWidth: 300,
        maxWidth: 400,
    },
    header: {
        marginBottom: 20,
        alignItems: 'center' as const,
    },
    title: {
        ...typography.lg,
        ...typography.fontSemibold,
        color: colors.gray[900],
        textAlign: 'center' as const,
        marginBottom: 4,
    },
    subtitle: {
        ...typography.sm,
        ...typography.fontNormal,
        color: colors.gray[600],
        textAlign: 'center' as const,
    },
    content: {
        flex: 1,
        marginBottom: 20,
    },
    field: {
        marginBottom: 16,
    },
    label: {
        ...typography.sm,
        ...typography.fontMedium,
        color: colors.gray[700],
        marginBottom: 8,
    },
    input: {
        borderWidth: 1,
        borderColor: colors.gray[300],
        borderRadius: 8,
        padding: 12,
        backgroundColor: colors.white,
        ...typography.md,
        color: colors.gray[900],
    },
    textArea: {
        minHeight: 80,
        textAlignVertical: 'top' as const,
    },
    actions: {
        flexDirection: 'row' as const,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
    },
    confirmButton: {
        flex: 1,
    },
};
