import React from 'react';
import { View, ScrollView } from 'react-native';
import { Text } from 'react-native';
import colors from '@styles/colors';
import { typography } from '@styles/typography';
import Input from '@components/inputs/textInputs/Input';
import SelectStatusInput from '@components/Navigation/ModalView/SelectStatusInput';;
import { Order } from '@components/orders/types';
import { OrderEditFormData, REJECTION_REASONS } from '../types/OrdersListModalTypes';
import { OrderModalUtils } from '../../../utils/orderModalUtils';
import SelectCustom from '@components/Navigation/ModalView/SelectCustom';
import TextButton from '@components/inputs/buttons/TextButton/TextButton';

interface EditModalProps {
    selectedOrders: Order[];
    formData: OrderEditFormData;
    onFormChange: (field: keyof OrderEditFormData, value: any) => void;
    onConfirm: () => void;
    onCancel: () => void;
    loading?: boolean;
}

export const EditModal: React.FC<EditModalProps> = ({
    selectedOrders,
    formData,
    onFormChange,
    onConfirm,
    onCancel,
    loading = false
}) => {
    const title = OrderModalUtils.getModalTitle("edit", selectedOrders);
    const fieldRequirements = OrderModalUtils.getFieldRequirements(formData.status);

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>{title}</Text>
                <Text style={styles.subtitle}>
                    {selectedOrders.length} order{selectedOrders.length > 1 ? 's' : ''} selected
                </Text>
            </View>
            
            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                {/* Status Selection */}
                <View style={styles.field}>
                    <Text style={styles.label}>Status *</Text>
                    <SelectStatusInput
                        value={formData.status}
                        onValueChange={(value) => onFormChange('status', value)}
                    />
                </View>

                {/* Delivery Company - shown for most statuses except rejected and attempt */}
                {fieldRequirements.requiresDeliveryCompany && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Delivery Company *</Text>
                        <Input
                            value={formData.deliveryCompany || ''}
                            onChangeText={(value) => onFormChange('deliveryCompany', value)}
                            placeholder="Enter delivery company"
                            style={styles.input}
                        />
                    </View>
                )}

                {/* Attempt Number - shown only for attempt status */}
                {fieldRequirements.requiresAttempt && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Attempt Number *</Text>
                        <Input
                            value={formData.attempt?.toString() || ''}
                            onChangeText={(value) => onFormChange('attempt', parseInt(value) || 1)}
                            placeholder="Enter attempt number"
                            keyboardType="numeric"
                            style={styles.input}
                        />
                    </View>
                )}

                {/* Rejection Reason - shown only for rejected status */}
                {fieldRequirements.requiresRejectionReason && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Rejection Reason *</Text>
                        <SelectCustom
                            value={formData.rejectionReason || ''}
                            action={(value) => onFormChange('rejectionReason', value)}
                            options={REJECTION_REASONS}
                        />
                    </View>
                )}

                {/* Note - always shown */}
                {fieldRequirements.allowsNote && (
                    <View style={styles.field}>
                        <Text style={styles.label}>Note</Text>
                        <Input
                            value={formData.note || ''}
                            onChangeText={(value) => onFormChange('note', value)}
                            placeholder="Add a note (optional)"
                            multiline
                            numberOfLines={3}
                            style={[styles.input, styles.textArea]}
                        />
                    </View>
                )}
            </ScrollView>
            
            <View style={styles.actions}>
                <TextButton
                    style={styles.cancelButton}
                    label="Cancel"
                    variant="outlined"
                    color={colors.gray[500]}
                    onPress={onCancel}
                    disabled={loading}
                />
                <TextButton
                    style={styles.confirmButton}
                    label="Confirm"
                    variant="contained"
                    color={colors.primary[500]}
                    onPress={onConfirm}
                    loading={loading}
                    disabled={loading}
                />
            </View>
        </View>
    );
};

const styles = {
    container: {
        backgroundColor: colors.white,
        borderRadius: 12,
        padding: 20,
        minWidth: 350,
        maxWidth: 400,
        maxHeight: '80%' as const,
    },
    header: {
        marginBottom: 20,
        alignItems: 'center' as const,
    },
    title: {
        ...typography.h3,
        color: colors.gray[900],
        textAlign: 'center' as const,
        marginBottom: 4,
    },
    subtitle: {
        ...typography.caption,
        color: colors.gray[600],
        textAlign: 'center' as const,
    },
    content: {
        flex: 1,
        marginBottom: 20,
    },
    field: {
        marginBottom: 16,
    },
    label: {
        ...typography.body2,
        color: colors.gray[700],
        marginBottom: 8,
        fontWeight: '600' as const,
    },
    input: {
        borderWidth: 1,
        borderColor: colors.gray[300],
        borderRadius: 8,
        padding: 12,
        backgroundColor: colors.white,
    },
    textArea: {
        minHeight: 80,
        textAlignVertical: 'top' as const,
    },
    actions: {
        flexDirection: 'row' as const,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
    },
    confirmButton: {
        flex: 1,
    },
};
