import colors from "@styles/colors";
import React, { useEffect, useState } from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import Svg, { Path } from "react-native-svg";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import PasswordInput from "@components/inputs/textInputs/PasswordInput";
import Input from "../../components/inputs/textInputs/Input";
import { User } from "../../types/Store";

interface ProfileFormProps {
    user: User;
    onSubmit: (user: any) => void;
    onPasswordSubmit: (passwords: any) => void;
    onLogout: () => void;
}

const ProfileForm: React.FC<ProfileFormProps> = ({ user, onSubmit, onPasswordSubmit }) => {
    const initialProfileValues = {
        email: user.email || "",
        firstname: user.firstname || "",
        lastname: user.lastname || "",
        username: user.username || "",
        phone: user.phone || "",
    };
    useEffect(() => {
        setDetailProfileValues(initialProfileValues);
    }, [user]);

    const [detailProfileValues, setDetailProfileValues] = useState<any>(initialProfileValues);
    const [resetPasswordValues, setResetPasswordValues] = useState<any>({});

    const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        if (detailProfileValues === initialProfileValues) return;
        onSubmit({ ...detailProfileValues });
    };

    const handlePasswordSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        if (Object.keys(resetPasswordValues).length !== 3) return;
        if (resetPasswordValues.newPassword !== resetPasswordValues.confirmPassword) {
            return;
        }
        if (resetPasswordValues.currentPassword === resetPasswordValues.newPassword) {
            return;
        }
        onPasswordSubmit(resetPasswordValues);
        setResetPasswordValues({});
    };

    const SaveIcon = () => {
        return (
            <View>
                <Svg width="15" height="12" viewBox="0 0 15 12" fill="none">
                    <Path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M7.50016 0C7.86835 0 8.16683 0.298477 8.16683 0.666667V5.72386L9.02876 4.86193C9.28911 4.60158 9.71122 4.60158 9.97157 4.86193C10.2319 5.12228 10.2319 5.54439 9.97157 5.80474L7.97204 7.80426C7.97044 7.80587 7.96882 7.80748 7.9672 7.80907C7.84735 7.92673 7.68325 7.99947 7.50216 8C7.5015 8 7.50083 8 7.50016 8C7.4995 8 7.49883 8 7.49816 8C7.4085 7.99973 7.323 7.98177 7.24497 7.94941C7.16813 7.91762 7.09603 7.87084 7.03313 7.80907C7.03151 7.80748 7.02989 7.80587 7.02828 7.80426L5.02876 5.80474C4.76841 5.54439 4.76841 5.12228 5.02876 4.86193C5.28911 4.60158 5.71122 4.60158 5.97157 4.86193L6.8335 5.72386V0.666667C6.8335 0.298477 7.13197 0 7.50016 0ZM1.41928 2.58579C1.79436 2.21071 2.30306 2 2.8335 2H4.8335C5.20169 2 5.50016 2.29848 5.50016 2.66667C5.50016 3.03486 5.20169 3.33333 4.8335 3.33333H2.8335C2.65668 3.33333 2.48712 3.40357 2.36209 3.5286C2.23707 3.65362 2.16683 3.82319 2.16683 4V10C2.16683 10.1768 2.23707 10.3464 2.36209 10.4714C2.48712 10.5964 2.65669 10.6667 2.8335 10.6667H12.1668C12.3436 10.6667 12.5132 10.5964 12.6382 10.4714C12.7633 10.3464 12.8335 10.1768 12.8335 10V4C12.8335 3.82319 12.7633 3.65362 12.6382 3.5286C12.5132 3.40357 12.3436 3.33333 12.1668 3.33333H10.1668C9.79864 3.33333 9.50016 3.03486 9.50016 2.66667C9.50016 2.29848 9.79864 2 10.1668 2H12.1668C12.6973 2 13.206 2.21071 13.581 2.58579C13.9561 2.96086 14.1668 3.46957 14.1668 4V10C14.1668 10.5304 13.9561 11.0391 13.581 11.4142C13.206 11.7893 12.6973 12 12.1668 12H2.8335C2.30306 12 1.79436 11.7893 1.41928 11.4142C1.04421 11.0391 0.833496 10.5304 0.833496 10V4C0.833496 3.46957 1.04421 2.96086 1.41928 2.58579Z"
                        fill={colors.white}
                    />
                </Svg>
            </View>
        );
    };

    return (
        <ScrollView contentContainerStyle={styles.container}>
            <View style={styles.profileContainer}>
                <Text style={styles.sectionTitle}>Profile Details</Text>
                <View style={{ gap: 5 }}>
                    <Input
                        label={"First Name"}
                        placeholder="Firstname"
                        inputProps={{
                            value: detailProfileValues.firstname,
                            defaultValue: user.firstname,
                        }}
                        onChange={(value) => {
                            const newValues = { ...detailProfileValues, firstname: value };
                            setDetailProfileValues(newValues);
                        }}
                        validationSchema={(value) => {
                            return value.length < 5 ? "First name must be at least 5 characters" : null;
                        }}
                    />
                </View>
                <View style={{ gap: 5 }}>
                    <Input
                        label={"Last Name"}
                        placeholder="Lastname"
                        inputProps={{
                            value: detailProfileValues.lastname,
                            defaultValue: user.lastname,
                        }}
                        onChange={(value) => {
                            const newValues = { ...detailProfileValues, lastname: value };
                            setDetailProfileValues(newValues);
                        }}
                        validationSchema={(value) => {
                            return value.length < 5 ? "Last name must be at least 5 characters" : null;
                        }}
                    />
                </View>
                <View style={{ gap: 5 }}>
                    <Input
                        label={"Phone"}
                        placeholder="Phone"
                        inputProps={{
                            value: detailProfileValues.phone,
                            defaultValue: user.phone,
                        }}
                        onChange={(value) => {
                            const newValues = { ...detailProfileValues, phone: value };
                            setDetailProfileValues(newValues);
                        }}
                        validationSchema={(value) => {
                            return !/^\+216[0-9]{8}$/.test(value)
                                ? "Phone number must start with +216 and contain 8 digits"
                                : null;
                        }}
                    />
                </View>
                <View style={{ gap: 5 }}>
                    <Input
                        label={"Email"}
                        isValid={true}
                        inputProps={{
                            value: detailProfileValues.email,
                            defaultValue: user.email,
                        }}
                        isEditable={false}
                        style={{
                            width: "100%",
                            height: 50,
                            padding: 10,
                            borderWidth: 1,
                            borderColor: colors.gray[400],
                            borderRadius: 5,
                            backgroundColor: colors.gray[100],
                        }}
                    />
                </View>
                <View style={{ gap: 10, paddingBottom: 10, paddingTop: 10 }}>
                    <TextButton
                        label="Save Changes"
                        onPress={handleSubmit}
                        leftIcon={<SaveIcon />}
                        color={colors.blue[500]}
                    />
                </View>
            </View>
            <View style={styles.profileContainer}>
                <Text style={styles.sectionTitle}>Reset Password</Text>
                <View style={{ gap: 5 }}>
                    <PasswordInput
                        label={"Current Password"}
                        inputProps={{
                            defaultValue: resetPasswordValues.currentPassword,
                        }}
                        onChange={(value) => {
                            setResetPasswordValues({ ...resetPasswordValues, currentPassword: value });
                        }}
                        validationSchema={(value) => {
                            return value.length < 8 ? "Password must be at least 8 characters" : null;
                        }}
                    />
                </View>
                <View style={{ gap: 5 }}>
                    <PasswordInput
                        label={"New Password"}
                        inputProps={{
                            defaultValue: resetPasswordValues.newPassword,
                        }}
                        onChange={(value) => {
                            setResetPasswordValues({ ...resetPasswordValues, newPassword: value });
                        }}
                        validationSchema={(value) => {
                            return value.length < 8 ? "Password must be at least 8 characters" : null;
                        }}
                    />
                </View>
                <View style={{ gap: 5 }}>
                    <PasswordInput
                        inputProps={{
                            defaultValue: resetPasswordValues.confirmPassword,
                        }}
                        label={"Confirm Password"}
                        onChange={(value) => {
                            setResetPasswordValues({ ...resetPasswordValues, confirmPassword: value });
                        }}
                        validationSchema={(value) => {
                            return value.length < 8 ? "Password must be at least 8 characters" : null;
                        }}
                    />
                </View>
                <View style={{ gap: 10, paddingBottom: 10, paddingTop: 10 }}>
                    <TextButton
                        label="Save Changes"
                        onPress={handlePasswordSubmit}
                        leftIcon={<SaveIcon />}
                        color={colors.blue[500]}
                    />
                </View>
            </View>
        </ScrollView>
    );
};
export default ProfileForm;

const styles = StyleSheet.create({
    label: {
        fontSize: 12,
        fontWeight: "bold",
        color: colors.gray[700],
    },
    container: {
        gap: 25,
        paddingVertical: 5,
        paddingHorizontal: 10,
    },
    profileContainer: {
        borderColor: colors.gray[200],
        backgroundColor: colors.white,
        borderRadius: 13.06,
        paddingTop: 20,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 1,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: "bold",
        textAlign: "center",
        color: colors.primary[500],
        paddingVertical: 10,
    },
    logoutButton: {
        width: "90%",
        alignSelf: "center",
    },
});
