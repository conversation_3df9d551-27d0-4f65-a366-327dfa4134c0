import { Defs, LinearGradient, Path, Stop, Svg } from "react-native-svg";

export const BreakEvenLeadCostIcon = () => {
    return (
        <Svg width="51" height="50" viewBox="0 0 51 50" fill="none">
            <Path
                d="M11.2837 40.216C10.7833 40.216 10.3608 40.0439 10.0163 39.6997C9.6721 39.3553 9.5 38.9328 9.5 38.4323C9.5 37.9318 9.6721 37.5093 10.0163 37.1649C10.3608 36.8207 10.7833 36.6486 11.2837 36.6486H23.7163V19.2843C23.0618 19.0365 22.4956 18.6722 22.0176 18.1913C21.5398 17.7102 21.1771 17.1424 20.9293 16.4879H16.3163L20.4691 26.2272C20.6185 26.5523 20.7079 26.887 20.7372 27.2312C20.7665 27.5756 20.7518 27.9234 20.6933 28.2744C20.4581 29.508 19.7831 30.4654 18.6685 31.1468C17.5538 31.8284 16.399 32.1692 15.2041 32.1692C14.0092 32.1692 12.8544 31.8284 11.7397 31.1468C10.625 30.4654 9.95005 29.508 9.71483 28.2744C9.65629 27.9234 9.64165 27.5756 9.67093 27.2312C9.7002 26.887 9.78958 26.5523 9.93908 26.2272L14.1013 16.4879H12.8519C12.3514 16.4879 11.9289 16.3158 11.5844 15.9716C11.2402 15.6272 11.0681 15.2047 11.0681 14.7042C11.0681 14.2037 11.2402 13.7812 11.5844 13.4367C11.9289 13.0925 12.3514 12.9204 12.8519 12.9204H20.9293C21.2743 11.9931 21.8613 11.2386 22.6903 10.6568C23.5191 10.0751 24.4556 9.78418 25.5 9.78418C26.5444 9.78418 27.4809 10.0751 28.3097 10.6568C29.1387 11.2386 29.7257 11.9931 30.0707 12.9204H38.1481C38.6486 12.9204 39.0711 13.0925 39.4156 13.4367C39.7598 13.7812 39.9319 14.2037 39.9319 14.7042C39.9319 15.2047 39.7598 15.6272 39.4156 15.9716C39.0711 16.3158 38.6486 16.4879 38.1481 16.4879H36.8987L41.0609 26.2272C41.2104 26.5523 41.2998 26.887 41.3291 27.2312C41.3583 27.5756 41.3437 27.9234 41.2852 28.2744C41.0499 29.508 40.375 30.4654 39.2603 31.1468C38.1456 31.8284 36.9908 32.1692 35.7959 32.1692C34.601 32.1692 33.4462 31.8284 32.3315 31.1468C31.2169 30.4654 30.5419 29.508 30.3067 28.2744C30.2482 27.9234 30.2335 27.5756 30.2628 27.2312C30.2921 26.887 30.3815 26.5523 30.5309 26.2272L34.6837 16.4879H30.0707C29.8229 17.1424 29.4602 17.7102 28.9824 18.1913C28.5044 18.6722 27.9382 19.0365 27.2837 19.2843V36.6486H39.7163C40.2167 36.6486 40.6392 36.8207 40.9837 37.1649C41.3279 37.5093 41.5 37.9318 41.5 38.4323C41.5 38.9328 41.3279 39.3553 40.9837 39.6997C40.6392 40.0439 40.2167 40.216 39.7163 40.216H11.2837ZM32.9964 27.1085H38.5954L35.7959 20.6152L32.9964 27.1085ZM12.414 27.1085H18.013L15.2041 20.6152L12.414 27.1085ZM25.5 16.2068C25.9255 16.2068 26.2807 16.0611 26.5655 15.7697C26.8507 15.4786 26.9933 15.1234 26.9933 14.7042C26.9933 14.2787 26.8507 13.9234 26.5655 13.6382C26.2807 13.3534 25.9255 13.2109 25.5 13.2109C25.0808 13.2109 24.727 13.3534 24.4388 13.6382C24.1508 13.9234 24.0067 14.2787 24.0067 14.7042C24.0067 15.1234 24.1508 15.4786 24.4388 15.7697C24.727 16.0611 25.0808 16.2068 25.5 16.2068Z"
                fill="url(#paint0_linear_9162_23545)"
            />
            <Defs>
                <LinearGradient
                    id="paint0_linear_9162_23545"
                    x1="25.5"
                    y1="9.78418"
                    x2="25.5"
                    y2="40.216"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
            </Defs>
        </Svg>
    );
};

export const TotalProfitIcon = () => {
    return (
        <Svg width="50" height="50" viewBox="0 0 50 50" fill="none">
            <Path
                d="M40.9997 29.4086C40.8964 29.9261 40.8256 30.4536 40.6831 30.9611C39.6222 34.7511 36.0714 37.2978 32.0939 37.1378C28.2506 36.9836 24.9539 34.1203 24.2072 30.2878C23.2714 25.4886 26.7514 20.8294 31.6281 20.3511C36.1831 19.9044 40.2122 23.0253 40.9172 27.5478C40.9431 27.7111 40.9722 27.8736 40.9989 28.0361V29.4086H40.9997ZM31.5622 24.2011C29.9039 24.8186 29.2331 26.6661 30.0022 28.1269C30.5206 29.1103 31.3622 29.6111 32.4731 29.6578C32.8997 29.6761 33.2097 29.8669 33.3689 30.2653C33.5147 30.6302 33.4331 30.9661 33.1647 31.2428C32.8639 31.5528 32.4806 31.6253 32.1047 31.4386C31.8231 31.2994 31.5622 31.0928 31.3356 30.8711C30.8789 30.4244 30.3322 30.3594 29.9247 30.7428C29.5156 31.1278 29.5314 31.6794 29.9622 32.1544C30.4031 32.6411 30.9289 33.0027 31.5614 33.2336C31.5614 33.5911 31.5572 33.9444 31.5622 34.2969C31.5706 34.8544 31.9647 35.2661 32.4881 35.2719C33.0231 35.2778 33.4256 34.8611 33.4356 34.2869C33.4397 34.0378 33.4514 33.7869 33.4322 33.5386C33.4156 33.3211 33.4964 33.2211 33.6956 33.1269C35.3531 32.3386 35.8314 30.2494 34.6814 28.8319C34.1247 28.1461 33.3881 27.8103 32.5056 27.7853C32.0922 27.7736 31.7906 27.5736 31.6322 27.1911C31.4831 26.8286 31.5564 26.4903 31.8231 26.2103C32.1097 25.9094 32.4731 25.8278 32.8531 25.9886C33.0872 26.0878 33.3047 26.2419 33.5056 26.4019C33.9497 26.7561 34.4964 26.7494 34.8531 26.3636C35.2164 25.9703 35.1956 25.3744 34.7472 25.0103C34.4047 24.7319 33.9956 24.5369 33.6264 24.2903C33.5422 24.2344 33.4531 24.1211 33.4456 24.0278C33.4222 23.7386 33.4406 23.4461 33.4364 23.1553C33.4272 22.5811 33.0222 22.1653 32.4864 22.1728C31.9631 22.1794 31.5714 22.5919 31.5631 23.1494C31.5581 23.4919 31.5622 23.8353 31.5622 24.2011Z"
                fill="url(#paint0_linear_7682_5867)"
            />
            <Path
                d="M20.3126 40.9502V16.5094C20.1959 16.5036 20.0959 16.4952 19.9959 16.4952C19.1834 16.4944 18.3717 16.4969 17.5592 16.4936C17.1084 16.4911 16.7734 16.2694 16.6276 15.8919C16.4759 15.4986 16.5817 15.1161 16.9442 14.8052C19.0934 12.9636 21.2426 11.1227 23.3951 9.28524C23.8351 8.9094 24.2826 8.90357 24.7201 9.27607C26.8817 11.1186 29.0392 12.9661 31.1951 14.8152C31.5434 15.1144 31.6426 15.4944 31.5009 15.8786C31.3559 16.2719 31.0217 16.4911 30.5476 16.4927C29.6526 16.4961 28.7576 16.4936 27.8126 16.4936C27.8126 16.6136 27.8126 16.7219 27.8126 16.8311C27.8126 17.6627 27.8067 18.4944 27.8167 19.3261C27.8192 19.5086 27.7517 19.5952 27.5976 19.6852C23.9642 21.8036 22.0634 24.9627 22.2201 29.1652C22.3617 32.9752 24.2617 35.8219 27.5567 37.7361C27.7417 37.8436 27.8251 37.9461 27.8176 38.1652C27.8001 38.6736 27.7867 39.1852 27.8184 39.6927C27.8551 40.2736 27.6642 40.7002 27.1242 40.9494H20.3117L20.3126 40.9502Z"
                fill="url(#paint1_linear_7682_5867)"
            />
            <Path
                d="M14.6875 40.9502C14.6875 35.0769 14.6875 29.2035 14.6875 23.3302C14.6875 22.5119 15.025 22.1719 15.8375 22.1719C16.69 22.1719 17.5425 22.1719 18.4375 22.1719V40.9502H14.6875Z"
                fill="url(#paint2_linear_7682_5867)"
            />
            <Path
                d="M9 30.3446C9.24917 29.8063 9.67583 29.6271 10.2575 29.6504C11.0758 29.6829 11.8958 29.6588 12.75 29.6588V40.9504H9.82333C9.72833 40.9504 9.63417 40.9271 9.55333 40.8779C9.42333 40.7988 9.30917 40.7029 9.2125 40.5904C9.07333 40.4288 9 40.2204 9 40.0063V30.3454V30.3446Z"
                fill="url(#paint3_linear_7682_5867)"
            />
            <Defs>
                <LinearGradient
                    id="paint0_linear_7682_5867"
                    x1="32.5272"
                    y1="20.3086"
                    x2="32.5272"
                    y2="37.1449"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint1_linear_7682_5867"
                    x1="24.0619"
                    y1="9"
                    x2="24.0619"
                    y2="40.9502"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint2_linear_7682_5867"
                    x1="16.5625"
                    y1="22.1719"
                    x2="16.5625"
                    y2="40.9502"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint3_linear_7682_5867"
                    x1="10.875"
                    y1="29.6484"
                    x2="10.875"
                    y2="40.9504"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
            </Defs>
        </Svg>
    );
};

export const ProfitPerUnitIcon = () => {
    return (
        <Svg width="50" height="50" viewBox="0 0 50 50" fill="none">
            <Path
                d="M17.9886 8C18.7198 8.16086 19.483 8.23763 20.1785 8.49536C23.7914 9.83431 25.9638 12.4181 26.3934 16.2585C26.9536 21.2652 23.5245 25.601 18.5388 26.3815C13.6921 27.1401 8.89659 23.6415 8.13435 18.7911C8.09231 18.5233 8.04478 18.2564 8 17.9886C8 17.4869 8 16.9851 8 16.4834C8.02102 16.3846 8.05118 16.2878 8.06215 16.1881C8.46795 12.5296 11.0691 9.4212 14.6171 8.39209C15.2257 8.21569 15.86 8.12887 16.4834 8C16.9851 8 17.4869 8 17.9886 8ZM16.2101 12.2819C16.0922 12.3376 15.9889 12.3843 15.8865 12.4345C14.1025 13.3037 13.5943 15.6005 14.8574 17.1295C15.4734 17.8744 16.2795 18.2363 17.2465 18.2647C17.8077 18.2811 18.2272 18.696 18.2601 19.228C18.2912 19.7398 17.9155 20.2086 17.3991 20.3019C16.901 20.3914 16.413 20.0725 16.2503 19.5506C16.2247 19.4674 16.2037 19.3833 16.1845 19.3157H14.1491C14.2332 20.5861 14.8099 21.5046 15.9322 22.0512C16.1552 22.1599 16.2293 22.2751 16.2146 22.5082C16.1964 22.7979 16.2101 23.0894 16.2101 23.3764H18.2628C18.2628 23.0885 18.2802 22.8162 18.2583 22.5475C18.2363 22.287 18.3204 22.1563 18.5681 22.0366C20.3732 21.1656 20.8822 18.9026 19.6255 17.3553C19.0068 16.5939 18.1888 16.2338 17.209 16.2055C16.6561 16.1891 16.2348 15.7549 16.2128 15.2257C16.1918 14.7249 16.5556 14.2725 17.0582 14.1729C17.5536 14.0751 18.0535 14.3913 18.2171 14.9068C18.2436 14.9918 18.2647 15.0786 18.2884 15.1645H20.3229C20.2379 13.7625 19.5378 12.8303 18.2555 12.2746V11.1038H16.2119V12.2837L16.2101 12.2819Z"
                fill="url(#paint0_linear_7682_5879)"
            />
            <Path
                d="M29.3776 8C30.1746 8 30.9715 8.08408 31.7448 8.27602C31.869 8.30709 31.9924 8.34091 32.1149 8.37929C35.4792 9.43491 37.7485 12.4409 37.8783 15.9779C37.8811 16.0437 37.8875 16.1095 37.8929 16.1991H39.464C40.1075 16.1991 40.4721 16.9367 40.0828 17.4485C38.8645 19.0488 37.6608 20.6291 36.4461 22.2239C36.1336 22.6343 35.5157 22.6325 35.2059 22.2193C34.0159 20.6336 32.8351 19.0589 31.6406 17.4668C31.2558 16.954 31.6214 16.222 32.2621 16.222H33.7756C33.7573 15.1755 33.4319 14.2899 32.7748 13.5349C31.9513 12.589 30.9003 12.1137 29.6454 12.1055C28.8941 12.1009 28.1428 12.1028 27.3715 12.1037C26.9419 12.1037 26.5928 11.7564 26.5928 11.3268V8.77686C26.5928 8.34822 26.941 8 27.3696 8H29.3776Z"
                fill="url(#paint1_linear_7682_5879)"
            />
            <Path
                d="M18.8653 41.0021C18.0684 41.0021 17.2714 40.918 16.4982 40.7261C16.3739 40.695 16.2505 40.6612 16.128 40.6228C12.7638 39.5672 10.4944 36.5612 10.3646 33.0242C10.3619 32.9584 10.3555 32.8926 10.35 32.803H8.77891C8.13549 32.803 7.77082 32.0654 8.16016 31.5536C9.37847 29.9533 10.5821 28.3731 11.7968 26.7782C12.1094 26.3678 12.7272 26.3697 13.037 26.7828C14.227 28.3685 15.4078 29.9432 16.6024 31.5354C16.9872 32.0481 16.6216 32.7802 15.9809 32.7802H14.4674C14.4857 33.8266 14.811 34.7123 15.4682 35.4672C16.2916 36.4131 17.3427 36.8884 18.5976 36.8966C19.3488 36.9012 20.1001 36.8994 20.8715 36.8985C21.301 36.8985 21.6502 37.2458 21.6502 37.6753V40.2253C21.6502 40.6548 21.302 41.0021 20.8733 41.0021H18.8653Z"
                fill="url(#paint2_linear_7682_5879)"
            />
            <Path
                d="M27.4614 26.6758H32.0285C32.458 26.6758 32.8053 27.024 32.8053 27.4526V32.7902H34.8608V27.4563C34.8608 27.0267 35.2091 26.6794 35.6377 26.6794H40.2212C40.6508 26.6794 40.9981 27.0277 40.9981 27.4563V40.2234C40.9981 40.6529 40.6498 41.0002 40.2212 41.0002H27.4614C27.0319 41.0002 26.6846 40.652 26.6846 40.2234V27.4536C26.6846 27.024 27.0328 26.6767 27.4614 26.6767V26.6758Z"
                fill="url(#paint3_linear_7682_5879)"
            />
            <Defs>
                <LinearGradient
                    id="paint0_linear_7682_5879"
                    x1="17.2266"
                    y1="8"
                    x2="17.2266"
                    y2="26.485"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint1_linear_7682_5879"
                    x1="33.4179"
                    y1="8"
                    x2="33.4179"
                    y2="22.5304"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint2_linear_7682_5879"
                    x1="14.8251"
                    y1="26.4717"
                    x2="14.8251"
                    y2="41.0021"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint3_linear_7682_5879"
                    x1="33.8413"
                    y1="26.6758"
                    x2="33.8413"
                    y2="41.0002"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
            </Defs>
        </Svg>
    );
};
export const ConfirmedLeadsIcon = () => {
    return (
        <Svg width="50" height="50" viewBox="0 0 50 50" fill="none">
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M25.0001 5C22.3479 5 19.8043 6.05357 17.929 7.92893C16.0536 9.8043 15 12.3478 15 15V17.5H12.5C11.8846 17.5 11.2908 17.7269 10.8322 18.1375C10.3737 18.548 10.0827 19.1133 10.015 19.725L7.51505 42.225C7.47638 42.5744 7.51183 42.928 7.61908 43.2628C7.72633 43.5975 7.90297 43.9059 8.13748 44.1678C8.37198 44.4296 8.65907 44.6391 8.98001 44.7825C9.30095 44.9259 9.64853 45 10 45H40.0001C40.3516 45 40.6991 44.9259 41.0201 44.7825C41.341 44.6391 41.6281 44.4296 41.8626 44.1678C42.0971 43.9059 42.2738 43.5975 42.381 43.2628C42.4883 42.928 42.5237 42.5744 42.4851 42.225L39.9851 19.725C39.9174 19.1133 39.6264 18.548 39.1679 18.1375C38.7093 17.7269 38.1155 17.5 37.5001 17.5H35.0001V15C35.0001 12.3478 33.9465 9.8043 32.0711 7.92893C30.1958 6.05357 27.6522 5 25.0001 5ZM30.0001 17.5V15C30.0001 13.6739 29.4733 12.4021 28.5356 11.4645C27.5979 10.5268 26.3261 10 25.0001 10C23.674 10 22.4022 10.5268 21.4645 11.4645C20.5268 12.4021 20.0001 13.6739 20.0001 15V17.5H30.0001ZM15 25C15 24.337 15.2634 23.7011 15.7323 23.2322C16.2011 22.7634 16.837 22.5 17.5001 22.5C18.1631 22.5 18.799 22.7634 19.2678 23.2322C19.7367 23.7011 20.0001 24.337 20.0001 25C20.0001 25.663 19.7367 26.2989 19.2678 26.7678C18.799 27.2366 18.1631 27.5 17.5001 27.5C16.837 27.5 16.2011 27.2366 15.7323 26.7678C15.2634 26.2989 15 25.663 15 25ZM32.5001 22.5C31.837 22.5 31.2011 22.7634 30.7323 23.2322C30.2634 23.7011 30.0001 24.337 30.0001 25C30.0001 25.663 30.2634 26.2989 30.7323 26.7678C31.2011 27.2366 31.837 27.5 32.5001 27.5C33.1631 27.5 33.799 27.2366 34.2678 26.7678C34.7367 26.2989 35.0001 25.663 35.0001 25C35.0001 24.337 34.7367 23.7011 34.2678 23.2322C33.799 22.7634 33.1631 22.5 32.5001 22.5Z"
                fill="url(#paint0_linear_7682_5891)"
            />
            <Defs>
                <LinearGradient
                    id="paint0_linear_7682_5891"
                    x1="25"
                    y1="5"
                    x2="25"
                    y2="45"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
            </Defs>
        </Svg>
    );
};

export const DeliveredLeadsIcon = () => {
    return (
        <Svg width="50" height="50" viewBox="0 0 50 50" fill="none">
            <Path
                d="M24.7411 42.8335H23.4096C22.8669 42.7035 22.3102 42.6184 21.7853 42.4379C18.0549 41.1539 14.4236 39.6352 10.9168 37.8219C8.42887 36.5352 7.08901 34.4881 7.04972 31.7144C6.98422 27.1771 6.98329 22.6369 7.04972 18.0995C7.09089 15.3268 8.43168 13.2806 10.9215 11.9939C14.4292 10.1815 18.0567 8.65254 21.7937 7.38727C22.5105 7.14507 23.2964 7.02724 24.0552 7.00292C26.0435 6.9384 27.7464 7.95772 29.6504 8.55248C29.4961 8.62168 29.4277 8.66002 29.3548 8.68433C28.6165 8.93776 27.8755 9.18371 27.1382 9.44088C22.6779 10.9998 18.3 12.7579 14.0531 14.8339C13.1436 15.2791 12.5018 15.9533 12.2379 16.9334C12.1331 17.3214 12.1125 17.7395 12.1013 18.1453C12.0339 20.5945 11.9769 23.0437 11.9263 25.4928C11.917 25.9576 12.1818 26.0979 12.5841 25.8641C12.9172 25.6705 13.2503 25.4779 13.5731 25.2684C13.8267 25.1029 14.0091 25.1309 14.1953 25.3797C14.6847 26.0334 15.1908 26.6749 15.6895 27.3211C15.8271 27.4988 15.9553 27.697 16.2294 27.5969C16.4979 27.4988 16.4764 27.2659 16.4783 27.0405C16.5008 24.4623 16.5139 21.8831 16.5541 19.3049C16.5756 17.9237 17.239 16.9352 18.5068 16.3723C19.4331 15.9608 20.3659 15.5615 21.2997 15.1678C25.3941 13.4424 29.5625 11.9125 33.7898 10.5453C33.9236 10.5023 34.107 10.4695 34.2202 10.5257C35.4955 11.1541 36.8194 11.7086 38.017 12.4633C40.0867 13.7678 41.0692 15.7541 41.1113 18.179C41.1665 21.3753 41.1721 24.5726 41.1983 27.7699C41.1983 27.8457 41.1908 27.9205 41.1842 28.0589C39.1473 26.283 36.8363 25.389 34.1884 25.4713C31.5367 25.5536 29.2659 26.558 27.4105 28.4489C25.7375 30.1537 24.8047 32.2204 24.6325 34.5985C24.4248 37.4666 25.3436 39.9635 27.2795 42.1087C27.1766 42.1508 27.1045 42.1891 27.0278 42.2106C26.2662 42.4192 25.5036 42.6249 24.7411 42.8325V42.8335Z"
                fill="url(#paint0_linear_7682_5898)"
            />
            <Path
                d="M33.7793 42.8335C33.0626 42.6446 32.3197 42.5211 31.6329 42.2537C28.6379 41.0875 26.6758 37.8547 27.008 34.7098C27.3701 31.2787 29.8093 28.5705 33.0645 27.9841C37.274 27.2266 41.113 29.8853 41.884 34.0916C42.6372 38.2016 39.6187 42.2696 35.4495 42.7671C35.3587 42.7783 35.2708 42.811 35.1809 42.8335H33.7793ZM33.3545 35.9376C32.8596 35.4766 32.3899 35.0567 31.9398 34.6162C31.3363 34.0243 30.5242 33.9738 29.9768 34.5124C29.422 35.0576 29.451 35.8535 30.0507 36.4725C30.7731 37.2188 31.4973 37.9632 32.2215 38.7076C32.9213 39.4257 33.6811 39.4257 34.3763 38.6982C35.8041 37.2057 37.2281 35.7094 38.6513 34.2132C39.2398 33.5941 39.2473 32.7871 38.6747 32.2531C38.1189 31.7341 37.3161 31.7874 36.7425 32.385C36.2176 32.932 35.6965 33.4838 35.1725 34.0327C34.554 34.6817 33.9337 35.3298 33.3536 35.9376H33.3545Z"
                fill="url(#paint1_linear_7682_5898)"
            />
            <Defs>
                <LinearGradient
                    id="paint0_linear_7682_5898"
                    x1="24.0993"
                    y1="7"
                    x2="24.0993"
                    y2="42.8335"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint1_linear_7682_5898"
                    x1="34.4855"
                    y1="27.8555"
                    x2="34.4855"
                    y2="42.8335"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
            </Defs>
        </Svg>
    );
};
export const CostPerDeliveredIcon = () => {
    return (
        <Svg width="50" height="50" viewBox="0 0 50 50" fill="none">
            <Path
                d="M4 25.6506C4.32669 24.8615 5.03652 25.0135 5.73006 24.9939C5.73006 24.8203 5.73006 24.664 5.73006 24.5088C5.73006 21.2592 5.72681 18.0096 5.73223 14.7601C5.7344 13.3263 6.57881 12.2822 7.91815 12.0391C8.11677 12.0033 8.32299 12.0011 8.52595 12.0011C15.0631 12 21.6013 12 28.1395 12C29.8196 12 30.9202 13.0897 30.9223 14.7698C30.9278 19.5356 30.9245 24.3015 30.9234 29.0673C30.9234 29.8932 30.6184 30.1993 29.8001 30.1993C21.5796 30.1993 13.3601 30.1971 5.13963 30.2069C4.60455 30.2069 4.21816 30.0495 4 29.5503V29.0629C4.26591 28.6082 4.66098 28.4399 5.1863 28.4671C5.75394 28.4975 6.32375 28.4801 6.89357 28.4714C7.51656 28.4627 7.90404 28.1219 7.90512 27.5999C7.90512 27.0908 7.5068 26.7403 6.88706 26.7294C6.31833 26.7186 5.74743 26.7023 5.17979 26.7338C4.65447 26.7631 4.26049 26.5992 4.00109 26.1379V25.6506H4ZM19.2015 16.6128C19.2015 16.527 19.2059 16.3664 19.2005 16.2069C19.1939 16.0451 19.1961 15.8802 19.1614 15.7239C19.0702 15.3082 18.7262 15.0433 18.3137 15.0499C17.9165 15.0564 17.5822 15.3125 17.4964 15.7098C17.4541 15.9051 17.4422 16.1135 17.4574 16.3132C17.4823 16.6215 17.3933 16.7712 17.0732 16.8971C16.1104 17.2759 15.4082 17.9304 15.3105 19.0234C15.2074 20.1771 15.85 20.9097 16.8181 21.3916C17.3499 21.6564 17.9154 21.8496 18.4602 22.0884C18.7685 22.2241 19.0865 22.3554 19.3622 22.5443C19.7095 22.7809 19.7182 23.1076 19.4164 23.4017C19.0181 23.7903 18.5069 23.9357 17.9914 23.8109C17.516 23.6958 17.0677 23.4538 16.6227 23.2378C16.1929 23.0294 15.7262 23.1119 15.4647 23.4831C15.1998 23.8586 15.2519 24.3514 15.6318 24.6423C15.9086 24.8539 16.2103 25.0764 16.5359 25.173C17.1708 25.3608 17.58 25.6071 17.491 26.3919C17.4411 26.8379 17.8872 27.1538 18.3289 27.1527C18.7555 27.1527 19.195 26.8553 19.157 26.4277C19.0876 25.6462 19.5011 25.3749 20.1089 25.0265C21.8411 24.0334 21.7728 21.8855 20.0318 20.9075C19.7616 20.7556 19.4729 20.6362 19.1863 20.5146C18.6632 20.2921 18.1314 20.0924 17.6137 19.858C17.3304 19.7288 16.9679 19.5367 17.058 19.2003C17.1285 18.9354 17.4433 18.7281 17.669 18.5154C17.7396 18.4492 17.8687 18.4416 17.974 18.4166C18.6882 18.2495 19.3047 18.485 19.8907 18.8682C20.4117 19.21 20.924 19.1427 21.2127 18.7086C21.5025 18.2723 21.3831 17.7502 20.8491 17.4333C20.3423 17.1327 19.792 16.9037 19.2026 16.6139L19.2015 16.6128Z"
                fill="url(#paint0_linear_7682_5907)"
            />
            <Path
                d="M45.6784 32.3928C45.6263 32.5296 45.572 32.6652 45.5232 32.802C45.1694 33.7929 44.2696 34.4789 43.2222 34.5136C42.4527 34.5386 41.6821 34.5179 40.9158 34.5179C40.4101 30.488 35.6942 28.7601 32.6877 31.6396C32.6747 31.5137 32.6552 31.416 32.6541 31.3172C32.653 26.9584 32.6519 22.6007 32.6541 18.2419C32.6541 17.5201 32.9743 17.2 33.6949 17.1989C35.3762 17.1967 37.0585 17.1837 38.7397 17.2021C40.218 17.2184 41.3196 17.8946 41.9448 19.235C43.0876 21.6847 44.198 24.1506 45.3083 26.6154C45.4722 26.979 45.5568 27.3774 45.6784 27.7605V32.3917V32.3928ZM38.4977 25.872C39.4061 25.872 40.3146 25.8828 41.223 25.8687C42.0197 25.8568 42.3963 25.2598 42.0717 24.5316C41.6637 23.6177 41.2393 22.7125 40.8279 21.7997C40.486 21.04 39.9097 20.6623 39.0696 20.6698C38.0114 20.6785 36.9543 20.6698 35.8961 20.6731C35.1233 20.6753 34.8346 20.9586 34.8313 21.7205C34.8281 22.7494 34.8281 23.7783 34.8313 24.8072C34.8335 25.5616 35.1374 25.8665 35.8928 25.8709C36.7611 25.8763 37.6283 25.872 38.4966 25.8709L38.4977 25.872Z"
                fill="url(#paint1_linear_7682_5907)"
            />
            <Path
                d="M18.4512 34.5278C18.0866 33.6259 17.7501 32.7956 17.4082 31.9512C17.4136 31.9512 17.5102 31.9295 17.6068 31.9295C21.6878 31.9284 25.7687 31.9262 29.8497 31.9295C30.591 31.9295 30.9133 32.2583 30.9242 32.9931C30.9437 34.3715 30.7885 34.5278 29.3971 34.5278C25.8718 34.5278 22.3466 34.5278 18.8224 34.5278H18.4512Z"
                fill="url(#paint2_linear_7682_5907)"
            />
            <Path
                d="M13.5458 38C11.883 38 10.5187 36.6422 10.5122 34.9838C10.5057 33.308 11.883 31.9253 13.5545 31.9307C15.2151 31.9361 16.5794 33.2917 16.5924 34.9512C16.6065 36.6173 15.2238 38.0011 13.5458 38Z"
                fill="url(#paint3_linear_7682_5907)"
            />
            <Path
                d="M39.1639 34.9938C39.1509 36.6685 37.7638 38.0198 36.0782 37.9981C34.423 37.9774 33.0609 36.5838 33.0848 34.9373C33.1087 33.2735 34.4773 31.9233 36.1346 31.9287C37.8126 31.9342 39.1769 33.3147 39.165 34.9927L39.1639 34.9938Z"
                fill="url(#paint4_linear_7682_5907)"
            />
            <Path
                d="M5.77205 31.9258C5.93051 31.9258 6.06293 31.9258 6.19643 31.9258C7.38598 31.9258 8.57553 31.9258 9.70539 31.9258C9.36133 32.7724 9.02162 33.6103 8.65911 34.5013C7.18085 34.7412 5.72104 33.5278 5.77205 31.9258Z"
                fill="url(#paint5_linear_7682_5907)"
            />
            <Defs>
                <LinearGradient
                    id="paint0_linear_7682_5907"
                    x1="17.4627"
                    y1="12"
                    x2="17.4627"
                    y2="30.2069"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint1_linear_7682_5907"
                    x1="39.1656"
                    y1="17.1924"
                    x2="39.1656"
                    y2="34.5259"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint2_linear_7682_5907"
                    x1="24.1669"
                    y1="31.9277"
                    x2="24.1669"
                    y2="34.5278"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint3_linear_7682_5907"
                    x1="13.5524"
                    y1="31.9307"
                    x2="13.5524"
                    y2="38"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint4_linear_7682_5907"
                    x1="36.1248"
                    y1="31.9287"
                    x2="36.1248"
                    y2="37.9983"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
                <LinearGradient
                    id="paint5_linear_7682_5907"
                    x1="7.73807"
                    y1="31.9258"
                    x2="7.73807"
                    y2="34.5317"
                    gradientUnits="userSpaceOnUse"
                >
                    <Stop stopColor="#8658C0" />
                    <Stop offset="1" stopColor="#2A358D" />
                </LinearGradient>
            </Defs>
        </Svg>
    );
};
