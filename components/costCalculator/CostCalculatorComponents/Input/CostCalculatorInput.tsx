import Input from "@components/inputs/textInputs/Input";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React, { useState } from "react";
import { StyleSheet, Text, View } from "react-native";
import { z } from "zod";

const CostCalculatorInput = ({
    label,
    unit,
    value,
    onChange,
}: {
    label: string;
    unit: string;
    value: string;
    onChange?: (value: any) => any;
}) => {
    const [inputError, setInputError] = useState<string | null>(null);

    const inputSchema = z.string().nonempty("Input cannot be empty");

    return (
        <>
            <Input
                isValid={inputError === null}
                inputProps={{ value, inputMode: "decimal" }}
                onChange={(value) => {
                    const result = inputSchema.safeParse(value);
                    if (!result.success) {
                        setInputError(result.error.errors[0].message);
                        onChange && onChange(value);
                    } else {
                        setInputError(null);
                        onChange && onChange(value);
                    }
                }}
                selectTextOnFocus
                placeholder={`Enter ${label}`}
                label={label}
                icon={<Text style={styles.currency}>{unit}</Text>}
            />
            <View style={{ opacity: inputError ? 1 : 0, paddingVertical: 2 }}>
                <Text style={{ color: colors.red[500], fontSize: typography.xs.fontSize }}>{inputError}</Text>
            </View>
        </>
    );
};

export default CostCalculatorInput;

const styles = StyleSheet.create({
    currency: { color: colors.gray[600], fontSize: typography.sm.fontSize },
});
