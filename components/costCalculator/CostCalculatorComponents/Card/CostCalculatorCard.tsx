import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { StyleSheet, Text, View } from "react-native";

const CalculatorCard = ({ title, children }: { title: string; children?: React.ReactNode }) => {
    return (
        <View style={styles.card}>
            <Text style={styles.title}>{title}</Text>
            {children}
        </View>
    );
};
export default CalculatorCard;

const styles = StyleSheet.create({
    card: {
        padding: 20,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "stretch",
        borderColor: colors.gray[300],
        backgroundColor: "white",
        borderWidth: 1,
        borderRadius: 10,
    },
    title: {
        color: colors["black"][600],
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.md.fontSize,
        lineHeight: 24,
        textTransform: "capitalize",
    },
});
