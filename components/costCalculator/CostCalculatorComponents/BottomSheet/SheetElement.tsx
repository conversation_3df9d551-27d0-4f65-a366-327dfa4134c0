import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

const SheetElement = ({
    icon,
    label,
    value,
    unit,
}: {
    icon: React.ReactElement;
    label: string;
    value: number;
    unit: string;
}) => {
    return (
        <View
            style={{
                height: 60,
                flexDirection: "row",
                alignItems: "center",
                alignSelf: "stretch",
                justifyContent: "space-between",
            }}
        >
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 10,
                }}
            >
                {icon}
                <Text
                    style={{
                        fontFamily: typography.fontSemibold.fontFamily,
                        fontSize: typography.md.fontSize,
                        color: colors.gray[600],
                    }}
                >
                    {label}:
                </Text>
            </View>
            <View style={{ flexDirection: "row", alignItems: "baseline" }}>
                <Text
                    style={{
                        fontFamily: typography.fontSemibold.fontFamily,
                        fontWeight: "800",
                        fontSize: 26,
                        lineHeight: 36,
                        color: colors.blue[700],
                    }}
                >
                    {/* {(() => {
                        const decimals: number = value - Math.floor(value);
                        const fixed = decimals.toFixed(2);
                        if (Number(fixed) !== 0) {
                            return value.toFixed(2);
                        }
                        return value;
                    })()} */}
                    {value.toFixed(2)}
                </Text>
                <Text
                    style={{
                        fontFamily: typography.fontNormal.fontFamily,
                        fontSize: 12,
                        color: colors.gray[700],
                        width: 26,
                    }}
                >
                    {unit}
                </Text>
            </View>
        </View>
    );
};

export default SheetElement;

const styles = StyleSheet.create({});
