import React from "react";
import { StyleSheet, View } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import CalculatorCard from "../Card/CostCalculatorCard";
import CostCalculatorInput from "../Input/CostCalculatorInput";
import { CardContentProps } from "@components/costCalculator/Props";

const CostCalculatorContent = ({ pageContent }: { pageContent: CardContentProps[] }) => {
    return (
        <ScrollView style={styles.scroll}>
            <View style={styles.content}>
                {pageContent?.map((card, index) => {
                    return (
                        <CalculatorCard title={`${index + 1}. ${card.cardTitle}`} key={index}>
                            {card.inputs.map((input, index) => {
                                return (
                                    <CostCalculatorInput
                                        label={input?.label}
                                        unit={input.unit}
                                        value={input.value.toString()}
                                        onChange={input.action}
                                        key={index}
                                    />
                                );
                            })}
                        </CalculatorCard>
                    );
                })}
            </View>
        </ScrollView>
    );
};

export default CostCalculatorContent;

const styles = StyleSheet.create({
    scroll: {
        height: "100%",
        paddingHorizontal: 10,
    },
    content: {
        gap: 10,
        paddingBottom: "50%",
    },
});
