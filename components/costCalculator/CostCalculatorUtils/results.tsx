import {
    BreakEvenLeadCostIcon,
    ConfirmedLeadsIcon,
    CostPerDeliveredIcon,
    DeliveredLeadsIcon,
    ProfitPerUnitIcon,
    TotalProfitIcon,
} from "../CostCalculatorIcons/CostCalculatorIcons";

const results = (
    result: {
        breakEvenLeadCost: number;
        deliveredLeads: number;
        totalProfit: number;
        profitPerUnit: number;
        confirmedLeads: number;
        costPerDelivered: number;
    },
    currency: string
) => {
    return [
        {
            label: "Total Profit",
            value: result?.totalProfit,
            unit: currency,
            icon: <TotalProfitIcon />,
        },
        {
            label: "Break-even Lead Cost",
            value: result?.breakEvenLeadCost,
            unit: currency,
            icon: <BreakEvenLeadCostIcon />,
        },
        {
            label: "Profit Per Unit",
            value: result?.profitPerUnit,
            unit: currency,
            icon: <ProfitPerUnitIcon />,
        },
        {
            label: "Confirmed Leads",
            value: result?.confirmedLeads,
            unit: "Unit",
            icon: <ConfirmedLeadsIcon />,
        },
        {
            label: "Delivered Leads",
            value: result?.deliveredLeads,
            unit: "Unit",
            icon: <DeliveredLeadsIcon />,
        },
        {
            label: "Cost Per Delivered",
            value: result?.costPerDelivered,
            unit: currency,
            icon: <CostPerDeliveredIcon />,
        },
    ];
};
export default results;
