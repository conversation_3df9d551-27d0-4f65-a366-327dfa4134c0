import { FormValuesProps } from "../Props";

const content = (currency: string, formValues: FormValuesProps, setFormValues: React.Dispatch<any>) => {
    return [
        {
            cardTitle: "production",
            inputs: [
                {
                    label: "Product Cost",
                    unit: currency,
                    value: formValues.productCost,
                    action: (value: any) => {
                        setFormValues({ ...formValues, productCost: value });
                    },
                },
                {
                    label: "Selling Price",
                    unit: currency,
                    value: formValues.sellingPrice,
                    action: (value: any) => {
                        setFormValues({ ...formValues, sellingPrice: value });
                    },
                },
            ],
        },
        {
            cardTitle: "leads",
            inputs: [
                {
                    label: "Lead Cost",
                    unit: currency,
                    value: formValues.leadCost,
                    action: (value: any) => {
                        setFormValues({ ...formValues, leadCost: value });
                    },
                },
                {
                    label: "Leads Recieved",
                    unit: currency,
                    value: formValues.leadReceived,
                    action: (value: any) => {
                        setFormValues({ ...formValues, leadReceived: value });
                    },
                },
            ],
        },
        {
            cardTitle: "Confiramtion",
            inputs: [
                {
                    label: "fullfillment Cost",
                    unit: currency,
                    value: formValues.fulfillmentCost,
                    action: (value: any) => {
                        setFormValues({
                            ...formValues,
                            fulfillmentCost: value,
                        });
                    },
                },
                {
                    label: "Confirmation Rate",
                    unit: "%",
                    value: formValues.confirmationRate,
                    action: (value: any) => {
                        setFormValues({
                            ...formValues,
                            confirmationRate: value,
                        });
                    },
                },
            ],
        },
        {
            cardTitle: "Delivery",
            inputs: [
                {
                    label: "Delivery Cost",
                    unit: currency,
                    value: formValues.deliveryCost,
                    action: (value: any) => {
                        setFormValues({ ...formValues, deliveryCost: value });
                    },
                },
                {
                    label: "Delivery Rate",
                    unit: "%",
                    value: formValues.deliveryRate,
                    action: (value: any) => {
                        setFormValues({ ...formValues, deliveryRate: value });
                    },
                },
            ],
        },
        {
            cardTitle: "Return",
            inputs: [
                {
                    label: "Return Cost",
                    unit: currency,
                    value: formValues.returnCost,
                    action: (value: any) => {
                        setFormValues({ ...formValues, returnCost: value });
                    },
                },
                // { label: 'Return Rate', unit: '%' ,value:formValues.deliveryRate},
            ],
        },
    ];
};

export default content;
