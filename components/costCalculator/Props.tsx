export type FormValuesProps = {
    productCost: number;
    sellingPrice: number;
    leadCost: number;
    leadReceived: number;
    fulfillmentCost: number;
    confirmationRate: number;
    deliveryCost: number;
    deliveryRate: number;
    returnCost: number;
};

export type ResultProps = {
    deliveredLeads: number;
    totalProfit: number;
    profitPerUnit: number;
    confirmedLeads: number;
    costPerDelivered: number;
};

export type CardContentProps = {
    cardTitle: string;
    inputs: {
        label: string;
        unit: string;
        value: number;
        action: (value: any) => void;
    }[];
};

export type SheetElementProps = {
    label: string;
    value: number;
    unit: string;
    icon: React.JSX.Element;
};
