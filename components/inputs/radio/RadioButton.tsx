import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface RadioButtonProps {
    label?: string;
    value: string;
    index: number;
    color?: string;
    noLabel?: boolean;
    selectedValue: string | null;
    onValueChange: (itemValue: string, itemIndex: number) => void;
}

const RadioButton: React.FC<RadioButtonProps> = ({ label, value, index, color, selectedValue, onValueChange }) => {
    return (
        <TouchableOpacity style={styles.radioButton} onPress={() => onValueChange(value, index)}>
            <View
                style={{
                    padding: 2.5,
                    borderWidth: 0.5,
                    borderRadius: 15,
                    borderColor: selectedValue === value ? "#000" : "#fff",
                    alignItems: "center",
                }}
            >
                <View
                    style={[
                        styles.radioButtonDot,
                        {
                            borderColor: selectedValue === value ? "#000" : "#fff",
                            backgroundColor: color,
                        },
                    ]}
                />
            </View>
            {label && <Text style={styles.radioButtonText}>{label}</Text>}
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    radioButton: {
        flexDirection: "row",
        alignItems: "center",
    },
    radioButtonDot: {
        height: 15,
        width: 15,
        borderRadius: 10,
        justifyContent: "center",
    },
    radioButtonText: {
        marginLeft: 10,
    },
});

export default RadioButton;
