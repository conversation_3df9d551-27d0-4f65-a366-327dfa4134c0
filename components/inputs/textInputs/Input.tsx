import React, { useState } from "react";
import {
    StyleSheet,
    Text,
    TextInput,
    TextInputProps,
    TextInputAndroidProps,
    TouchableOpacity,
    View,
    ViewProps,
    TextStyle,
    StyleProp,
} from "react-native";
import { typography } from "../../../styles/typography";
import colors from "../../../styles/colors";
import { ViewStyle } from "react-native";
import { TextInputIOSProps } from "react-native";
import { inputStyles } from "@app/createOrder";

interface Action {
    text: string;
    callback: () => void;
}

interface InputProps {
    label?: string;
    placeholder?: string;
    action?: Action;
    onChange?: (text: string) => void;
    inputProps?: TextInputProps & TextInputAndroidProps & TextInputIOSProps & ViewProps;
    onIconPress?: () => void;
    icon?: React.ReactNode;
    isValid?: boolean;
    isEditable?: boolean;
    validationSchema?: (value: string) => string | null;
    noBottomPadding?: boolean;
    onSubmitEditing?: () => void;
    selectTextOnFocus?: boolean;
    noIconContainer?: boolean;
    textContentType?: string;
    autoComplete?: string;
    style?: ViewStyle;
    containerStyle?: ViewStyle;
    placeholderStyle?: TextStyle;
    inputStyle?: StyleProp<TextStyle>;
    labelStyle?: StyleProp<TextStyle>;
}

const styles = StyleSheet.create({
    inputContainer: {
        flexShrink: 0,
        width: "100%",
        alignItems: "center",
        justifyContent: "center",
    },
    textLabel: {
        flexDirection: "row",
        justifyContent: "space-between",
        width: "100%",
        paddingBottom: 5,
    },
    labelText: {
        color: colors.black,
        fontWeight: "bold",
    },
    actionText: {
        color: colors.primary[500],
    },
    inputElement: {
        flexDirection: "row",
        alignItems: "center",
        width: "100%",
        borderWidth: 0.5,
        //borderColor: "rgba(203, 213, 224, 1)",

        borderRadius: 6,
    },
    input: {
        minHeight: 50,

        fontFamily: typography.fontMedium.fontFamily,
        flex: 1,
        marginRight: 10,
        alignSelf: "stretch",
        paddingLeft: 10,
    },
    iconContainer: {
        width: 20,
        height: 20,
        justifyContent: "center",
        alignItems: "center",
    },
    vectorStroke: {
        position: "absolute",
        top: 7,
        right: 7,
        bottom: 7,
        left: 7,
    },
    _vectorStroke: {
        position: "absolute",
        top: 3,
        right: 1,
        bottom: 3,
        left: 1,
    },
});

export default function Input({
    label,
    action,
    onChange,
    inputProps,
    onIconPress,
    icon,
    placeholder,
    style,
    isValid,
    isEditable = true,
    validationSchema,
    noBottomPadding,
    containerStyle,
    onSubmitEditing,
    placeholderStyle,
    selectTextOnFocus,
    noIconContainer,
    inputStyle,
    labelStyle,
    autoComplete,
}: InputProps) {
    const [isFocused, setIsFocused] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const borderColor = error ? colors.red[500] : isFocused ? colors.primary[500] : colors.gray[400];

    return (
        <View style={[styles.inputContainer, containerStyle]}>
            {label && (
                <View style={styles.textLabel}>
                    <Text
                        style={[
                            styles?.labelText,
                            typography.baseText,
                            typography.xs,
                            typography.fontSemibold,
                            typography.lineHeight4,
                            !isEditable && { color: colors.gray[500] },
                            labelStyle,
                        ]}
                    >
                        {label}
                    </Text>
                    {action && (
                        <Text
                            style={[
                                styles.actionText,
                                typography.baseText,
                                typography.xs,
                                typography.fontSemibold,
                                typography.lineHeight4,
                            ]}
                            onPress={action.callback}
                        >
                            {action.text}
                        </Text>
                    )}
                </View>
            )}
            <View
                style={[
                    styles.inputElement,
                    { borderColor },
                    !isEditable && { backgroundColor: colors.gray[100] },
                    style,
                ]}
            >
                <TextInput
                    onSubmitEditing={onSubmitEditing ? onSubmitEditing : () => {}}
                    returnKeyType="done"
                    style={[styles.input, !isEditable && { color: colors.gray[300] }, inputStyle]}
                    onChangeText={(value) => {
                        if (onChange) {
                            onChange(value);
                        }
                        if (validationSchema) {
                            setError(validationSchema(value));
                        }
                    }}
                    value={inputProps?.value}
                    placeholderTextColor={colors.gray[400]}
                    placeholder={placeholder}
                    textContentType={inputProps?.textContentType}
                    importantForAutofill="yes" // Android equivalent
                    selectTextOnFocus={selectTextOnFocus}
                    onFocus={() => setIsFocused(true)}
                    onBlur={() => setIsFocused(false)}
                    editable={isEditable}
                    {...inputProps}
                    autoComplete={inputProps?.autoComplete}
                />
                <View style={{ paddingVertical: 5, paddingRight: 5 }}>
                    {icon && (
                        <TouchableOpacity
                            style={[
                                {
                                    justifyContent: "center",
                                    alignItems: "center",
                                    padding: 5,
                                },
                                !noIconContainer && {
                                    borderColor: colors.gray[200],
                                    borderWidth: 1,
                                    backgroundColor: colors.gray[100],
                                    borderRadius: 20,
                                },
                            ]}
                            onPress={onIconPress}
                        >
                            {icon}
                        </TouchableOpacity>
                    )}
                </View>
            </View>
            {!noBottomPadding && (
                <View style={{ opacity: error ? 1 : 0, paddingVertical: 2 }}>
                    <Text style={{ color: colors.red[500], fontSize: typography.xs.fontSize }}>{error}</Text>
                </View>
            )}
        </View>
    );
}
