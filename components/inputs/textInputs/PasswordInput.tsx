import React, { useState } from "react";
import { StyleSheet, Text, TextInput, TextInputProps, TouchableOpacity, View } from "react-native";
import Svg, { G, Path } from "react-native-svg";
import { typography } from "../../../styles/typography";
import colors from "../../../styles/colors";

interface Action {
    text: string;
    callback: () => void;
}

interface PasswordInputProps {
    label?: string;
    action?: Action;
    onChange?: (text: string) => void;
    inputProps?: TextInputProps | any;
    validationSchema?: (value: string) => string | null;
}

export default function PasswordInput({ label, action, onChange, inputProps, validationSchema }: PasswordInputProps) {
    const [isVisible, setIsVisible] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const renderEyeIcon = () => {
        if (isVisible) {
            return (
                <TouchableOpacity onPress={() => setIsVisible(!isVisible)}>
                    {/* Eye Open SVG */}
                    <Svg width={20} height={21} viewBox="0 0 20 21" fill="none">
                        <G fillRule="evenodd" clipRule="evenodd" fill="#718096">
                            <Path d="M10 8.699a1.667 1.667 0 100 3.333A1.667 1.667 0 0010 8.7zm-2.357-.69a3.333 3.333 0 114.714 4.713 3.333 3.333 0 01-4.714-4.714z" />
                            <Path d="M2.048 10.366l-.795.25a9.17 9.17 0 0017.494 0 .832.832 0 000-.5 9.17 9.17 0 00-17.494 0l.795.25zm.879 0a7.504 7.504 0 0014.146 0 7.503 7.503 0 00-14.146 0z" />
                        </G>
                    </Svg>
                </TouchableOpacity>
            );
        } else {
            return (
                <TouchableOpacity onPress={() => setIsVisible(!isVisible)}>
                    {/* Eye Closed SVG */}
                    <Svg width={20} height={21} viewBox="0 0 24 24" fill="none">
                        <G fillRule="evenodd" clipRule="evenodd" fill="#718096">
                            <Path d="M2.293 2.293a1 1 0 011.414 0l3.041 3.04A10.953 10.953 0 0112.001 4c4.927 0 9.094 3.239 10.496 7.7a1 1 0 010 .601 11.025 11.025 0 01-3.552 5.23l2.762 2.762a1 1 0 11-1.414 1.414l-3.574-3.574-.029-.029-3.249-3.249a.846.846 0 01-.053-.053l-4.19-4.19a.887.887 0 01-.053-.053L5.898 7.312l-.029-.029-3.576-3.576a1 1 0 010-1.414zM7.148 7.42a1 1 0 01-1.265-.123l.015.015m1.25.108c.35-.225.714-.424 1.09-.597l1.73 1.731a3.998 3.998 0 00-.797.617.997.997 0 00-.293.708m2.604.19l2.45 2.449a1.998 1.998 0 00-2.45-2.45zm-.897-.898l-.617-.617a4 4 0 015.478 5.478l-.617-.617v-.001m2.693 2.694a9.026 9.026 0 002.967-4.109A9.006 9.006 0 0012 6h-.001a8.953 8.953 0 00-3.762.823l-.94-.94m10.225 10.225l-2.076-2.076 2.076 2.076zm-6.935-6.935l-.002-.002.002.002zM12.001 4L12 5V4zM4.624 8.174a1 1 0 01.193 1.4A8.971 8.971 0 003.511 12 9.006 9.006 0 0012 18c.567 0 1.133-.052 1.69-.158a1 1 0 11.37 1.966c-.679.128-1.369.192-2.06.192v-1 1c-4.928 0-9.095-3.239-10.497-7.7a1 1 0 010-.6 10.97 10.97 0 011.72-3.333 1 1 0 011.4-.193z" />
                        </G>
                    </Svg>
                </TouchableOpacity>
            );
        }
    };

    return (
        <View style={styles.inputContainer}>
            {label && (
                <View style={styles.textLabel}>
                    <Text
                        style={[
                            styles?.labelText,
                            typography.baseText,
                            typography.xs,
                            typography.fontSemibold,
                            typography.lineHeight4,
                        ]}
                    >
                        {label}
                    </Text>
                    {action && (
                        <Text
                            style={[
                                styles.actionText,
                                typography.baseText,
                                typography.xs,
                                typography.fontSemibold,
                                typography.lineHeight4,
                            ]}
                            onPress={action.callback}
                        >
                            {action.text}
                        </Text>
                    )}
                </View>
            )}
            <View style={styles.inputElement}>
                <TextInput
                    style={styles.input}
                    secureTextEntry={!isVisible}
                    onChangeText={(text) => {
                        if (onChange) {
                            onChange(text);
                        }
                        if (validationSchema) {
                            setError(validationSchema(text));
                        }
                    }}
                    textContentType="password"
                    autoComplete="password"
                    keyboardType="default"
                    placeholder="Password"
                    placeholderTextColor={colors.gray[400]}
                    autoCapitalize="none"
                    {...inputProps}
                />
                {renderEyeIcon()}
            </View>
            <View style={{ opacity: error ? 1 : 0, paddingVertical: 2 }}>
                <Text style={{ color: colors.red[500], fontSize: typography.xs.fontSize }}>{error}</Text>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    inputContainer: {
        flexShrink: 0,
        width: "100%",
        alignItems: "center",
        justifyContent: "center",
    },
    textLabel: {
        flexDirection: "row",
        justifyContent: "space-between",
        width: "100%",
    },
    labelText: {
        color: colors.black,
    },
    actionText: {
        color: colors.primary[500],
    },
    inputElement: {
        flexDirection: "row",
        alignItems: "center",
        width: "100%",
        paddingHorizontal: 10,
        borderWidth: 0.5,
        borderRadius: 6,
        borderColor: colors.gray[400],
        height: 50,
    },
    input: {
        flex: 1,
        marginRight: 10,
    },
    iconContainer: {
        width: 20,
        height: 20,
        justifyContent: "center",
        alignItems: "center",
    },
    vectorStroke: {
        position: "absolute",
        top: 7,
        right: 7,
        bottom: 7,
        left: 7,
    },
    _vectorStroke: {
        position: "absolute",
        top: 3,
        right: 1,
        bottom: 3,
        left: 1,
    },
});
