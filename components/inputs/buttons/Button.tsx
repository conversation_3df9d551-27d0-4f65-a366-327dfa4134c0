import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { StyleProp, StyleSheet, Text, TextStyle, TouchableOpacity, ViewStyle } from "react-native";

const Button = ({
    rounded,
    label,
    type,
    variant,
    action,
    style,
    labelStyle,
    disabled,
}: {
    rounded?: boolean;
    label: string;
    type: "outlined" | "filled" | "text";
    variant: "primary" | "secondary" | "blue" | "green" | "red" | "orange" | "teal" | "cyan" | "pink" | "gray";
    action: () => void;
    style?: StyleProp<ViewStyle>;
    labelStyle?: StyleProp<TextStyle>;
    disabled?: boolean;
}) => {
    return (
        <TouchableOpacity
            disabled={disabled}
            onPress={action}
            style={[
                buttonStyles.container,
                type === "filled" && {
                    backgroundColor: colors[variant][600],
                },
                type === "outlined" && {
                    borderColor: colors[variant][600],
                    borderWidth: 1,
                },
                rounded && { borderRadius: rounded ? 100 : 6 },
                ,
                style,
            ]}
        >
            <Text
                style={[
                    buttonStyles.text,
                    type === "filled" && {
                        color: colors.white,
                    },
                    (type === "outlined" || type === "text") && {
                        color: colors[variant][600],
                    },
                    labelStyle,
                ]}
            >
                {label}
            </Text>
        </TouchableOpacity>
    );
};

export default Button;

const buttonStyles = StyleSheet.create({
    container: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 6,
    },
    text: {
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.sm.fontSize,
        textTransform: "capitalize",
        textAlign: "center",
    },
});
