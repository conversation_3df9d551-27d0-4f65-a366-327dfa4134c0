import colors from "@styles/colors";
import React from "react";
import { TouchableOpacity } from "react-native";
import { Circle, Svg } from "react-native-svg";
type Props = {
    children: React.ReactNode;
    notif?: boolean;
    disabled?: boolean;
    action?: () => void;
    backrgound?: string;
};
const IconButton = ({ disabled, notif, children, action = () => {}, backrgound }: Props) => {
    return (
        <TouchableOpacity
            disabled={disabled}
            onPress={action}
            style={[
                {
                    display: "flex",
                    width: 36,
                    height: 36,
                    justifyContent: "center",
                    alignItems: "center",
                    borderWidth: 1,
                    borderRadius: 100,
                    borderColor: colors.gray[200],
                },
                disabled ? { opacity: 0.3 } : backrgound && { backgroundColor: backrgound },
            ]}
        >
            {children}
            {notif && (
                <Svg
                    style={{ position: "absolute", top: 8, left: 20 }}
                    width="8"
                    height="8"
                    viewBox="0 0 8 8"
                    fill="none"
                >
                    <Circle cx="4" cy="4" r="4" fill={colors.red[500]} />
                </Svg>
            )}
        </TouchableOpacity>
    );
};

export default IconButton;
