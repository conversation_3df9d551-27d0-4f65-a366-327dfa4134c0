import { ViewStyle } from "react-native";

/**
 * Interface for TextButton properties
 */
export default interface TextButtonProps {
    /**
     * Button label
     * @default "Button"
     * @type {string}
     */
    label: string;
    /**
     * Button size
     * @default "medium"
     * @type {"xs" | "sm" | "md" | "lg"}
     */
    size?: string;
    /**
     * Button color
     * @default "var(--primary-500)"
     */
    color?: string;
    /**
     * Button variant
     * @default "contained"
     * @type {"contained" | "outlined" | "text"}
     */
    variant?: "contained" | "outlined" | "text";
    /**
     * Button disabled state
     * @default false
     * @type {boolean}
     */
    disabled?: boolean;

    /**
     * @default false
     * @type {boolean}
     */
    loading?: boolean;

    /**
     * Optional click handler
     * @type {() => void}
     * @default undefined
     */
    onPress?: (event: any) => void;
    /**
     * Optional left icon
     * @type {JSX.Element}
     * @default undefined
     */
    leftIcon?: JSX.Element;
    /**
     * Optional right icon
     * @type {JSX.Element}
     * @default undefined
     */
    rightIcon?: JSX.Element;
    style?: ViewStyle;
}
