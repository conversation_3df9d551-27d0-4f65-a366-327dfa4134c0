import React from "react";
import { StyleSheet, Text, TouchableOpacity } from "react-native";

import TextButtonProps from "./TextButtonProps";
import colors from "../../../../styles/colors";
import { typography } from "../../../../styles/typography";

const TextButton = ({
    label,
    size = "medium",
    color = colors.primary[500],
    variant = "contained",
    disabled = false,
    onPress,
    leftIcon,
    rightIcon,
    style,
}: TextButtonProps) => {
    return (
        <TouchableOpacity
            onPress={onPress}
            disabled={disabled}
            style={[
                styles.container,
                variant === "contained"
                    ? {
                          backgroundColor: color,
                      }
                    : variant === "outlined"
                    ? {
                          borderColor: color,
                          borderWidth: 1,
                      }
                    : {
                          backgroundColor: "transparent",
                      },
                size === "medium" ? styles.mediumButton : size === "small" ? styles.smallButton : styles.largeButton,
                style,
            ]}
        >
            {leftIcon}
            <Text
                style={[
                    size === "medium"
                        ? styles.mediumButtonText
                        : size === "small"
                        ? styles.smallButtonText
                        : styles.largeButtonText,
                    variant === "outlined" ? { color } : variant === "contained" ? { color: "white" } : { color },
                ]}
            >
                {label}
            </Text>
            {rightIcon}
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        width: "100%",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 6,
        gap: 10,
    },
    smallButton: {
        paddingVertical: 0,
        paddingHorizontal: 8,
    },
    mediumButton: {
        paddingVertical: 10,
        paddingHorizontal: 20,
    },
    largeButton: {
        paddingVertical: 20,
        paddingHorizontal: 40,
    },
    smallButtonText: {
        ...typography.baseText,
        ...typography.xs,
        ...typography.fontNormal,
        ...typography.lineHeight5,
    },
    mediumButtonText: {
        ...typography.baseText,
        ...typography.sm,
        ...typography.fontBold,
        ...typography.lineHeight5,
    },
    largeButtonText: {
        ...typography.baseText,
        ...typography.lg,
        ...typography.fontBold,
        ...typography.lineHeight8,
    },
});

export default TextButton;
