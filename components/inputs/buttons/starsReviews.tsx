import React, { useState } from "react";
import { StyleSheet, Text, View, SafeAreaView, TouchableOpacity } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import colors from "@styles/colors";

export default function Stars() {
    const [starRating, setStarRating] = useState(null);
    const size = 24;

    return (
        <View>
            <View style={styles.container}>
                <View style={styles.stars}>
                    <TouchableOpacity onPress={() => setStarRating(1)}>
                        <MaterialIcons
                            name={starRating >= 1 ? "star" : "star-border"}
                            size={size}
                            style={starRating >= 1 ? styles.starSelected : styles.starUnselected}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => setStarRating(2)}>
                        <MaterialIcons
                            name={starRating >= 2 ? "star" : "star-border"}
                            size={size}
                            style={starRating >= 2 ? styles.starSelected : styles.starUnselected}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => setStarRating(3)}>
                        <MaterialIcons
                            name={starRating >= 3 ? "star" : "star-border"}
                            size={size}
                            style={starRating >= 3 ? styles.starSelected : styles.starUnselected}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => setStarRating(4)}>
                        <MaterialIcons
                            name={starRating >= 4 ? "star" : "star-border"}
                            size={size}
                            style={starRating >= 4 ? styles.starSelected : styles.starUnselected}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => setStarRating(5)}>
                        <MaterialIcons
                            name={starRating >= 5 ? "star" : "star-border"}
                            size={size}
                            style={starRating >= 5 ? styles.starSelected : styles.starUnselected}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.gray[50],
        alignItems: "center",
        justifyContent: "center",
    },
    heading: {
        fontSize: 24,
        fontWeight: "bold",
    },
    stars: {
        display: "flex",
        flexDirection: "row",
    },
    starUnselected: {
        color: colors.primary[500],
    },
    starSelected: {
        color: colors.primary[500],
    },
});
