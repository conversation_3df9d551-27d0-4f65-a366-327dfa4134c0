import React from "react";
import { TouchableOpacity, StyleSheet, Dimensions, Text } from "react-native";
import { useRouter, ExpoRoot } from "expo-router";
import { AddIcon, TabsContentRowAdd } from "@components/icons/BudgetManagerIcons";
import colors from "@styles/colors";
import { Color } from "@components/charts/HomeChart/HomeChartV2";

const { height: screenHeight } = Dimensions.get("window");

interface FABProps {
    size?: "sm" | "md" | "lg";
    color?: Color;
    elevation?: number;
    height?: number;
    Icon?: React.FC<{ size: string; color: string }>;
    action?: () => void;
}

const FAB: React.FC<FABProps> = ({
    size = "md",
    color = "primary",
    elevation = 5,
    height,
    Icon = AddIcon,
    action = () => {},
}) => {
    const navigation = useRouter();
    const sizes = {
        sm: { fab: 45, icon: 25 },
        md: { fab: 60, icon: 25 },
        lg: { fab: 100, icon: 40 },
    };

    return (
        <TouchableOpacity
            style={[
                styles.fab,
                {
                    width: sizes[size].fab,
                    height: sizes[size].fab,
                    backgroundColor: colors[color][200],
                    elevation: elevation,
                    bottom: height * sizes[size].fab,
                    shadowColor: colors.gray[900], // IOS
                    shadowOffset: { height: 1, width: 0 }, // IOS
                    shadowOpacity: 0.3, // IOS
                    shadowRadius: 2, //IOS
                },
            ]}
            onPress={action}
            activeOpacity={0.8}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
            <Icon color={colors[color][800]} size={"30"} />
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    fab: {
        position: "absolute",
        right: 16,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 10000,
        zIndex: 1000,
        elevation: 10,
    },
});

export default FAB;
