import React from "react";
import { TouchableOpacity, StyleSheet, Dimensions, Text } from "react-native";
import { useRouter, ExpoRoot } from "expo-router";
import { AddIcon, TabsContentRowAdd } from "@components/icons/BudgetManagerIcons";
import colors from "@styles/colors";
import { Color } from "@components/charts/HomeChart/HomeChartV2";

const { height: screenHeight } = Dimensions.get("window");

interface FABProps {
    size?: "sm" | "md" | "lg";
    color?: Color;
    elevation?: number;
    Icon?: React.FC<{ size: string; color: string }>;
    action?: () => void;
}

const FAB: React.FC<FABProps> = ({
    size = "md",
    color = "primary",
    elevation = 5,
    Icon = AddIcon,
    action = () => {},
}) => {
    const navigation = useRouter();
    const sizes = {
        sm: { fab: 45, icon: 25 },
        md: { fab: 60, icon: 25 },
        lg: { fab: 100, icon: 40 },
    };

    return (
        <TouchableOpacity
            style={[
                styles.fab,
                {
                    width: sizes[size].fab,
                    height: sizes[size].fab,
                    backgroundColor: colors.primary[500],
                    opacity: 0.8,
                    elevation: elevation,
                    top: screenHeight * 0.64,
                    shadowColor: colors.gray[900], // IOS
                    shadowOffset: { height: 1, width: 0 }, // IOS
                    shadowOpacity: 0.3, // IOS
                    shadowRadius: 2, //IOS
                },
            ]}
            onPress={action}
        >
            <Icon color={colors.white} size={"30"} />
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    fab: {
        position: "absolute",
        right: 16,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 10000,
    },
});

export default FAB;
