import { Picker } from "@react-native-picker/picker";
import React from "react";
import { Text, View } from "react-native";

import { DeliveryCompanyPickerProps } from "./DeliveryCompanyPickerProps";
import { styles } from "./DeliveryCompanyPickerStyles";
import Button from "../buttons/Button";

const DeliveryCompanyPicker: React.FC<DeliveryCompanyPickerProps> = ({
    deliveryOptions,
    formValues,
    setFormValues,
    style,
    action,
    label = "Delivery Company",
}) => (
    <View style={[{ gap: 5 }, style]}>
        <Text style={styles?.label}>{label}</Text>
        <View style={{ flexDirection: "row", alignItems: "center", gap: 10 }}>
            <View style={styles.selectContainer}>
                <Picker
                    selectedValue={formValues.deliveryCompany}
                    placeholder="Delivery Company"
                    mode="dropdown"
                    itemStyle={styles.selectItem}
                    onValueChange={(itemValue: any) => setFormValues(itemValue)}
                >
                    {deliveryOptions.map((option, index) => {
                        return (
                            <Picker.Item
                                style={{ textTransform: "capitalize" }}
                                key={index}
                                label={option.name}
                                value={option.value}
                            />
                        );
                    })}
                </Picker>
            </View>
            {action && (
                <Button
                    label="filter"
                    action={action}
                    type="filled"
                    variant="primary"
                    style={{ flex: 1, alignSelf: "stretch", marginVertical: 8 }}
                />
            )}
        </View>
    </View>
);

export default DeliveryCompanyPicker;
