import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    label: {
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: 12,
        color: colors.gray[700],
        paddingLeft: 5,
    },
    selectContainer: {
        backgroundColor: colors.white,
        borderWidth: 1,
        borderColor: colors.gray[300],
        borderRadius: 6,
        flex: 6,
    },
    selectItem: {
        color: colors.gray[700],
    },
});
