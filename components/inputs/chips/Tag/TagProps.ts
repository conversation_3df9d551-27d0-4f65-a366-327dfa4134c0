export default interface TagProps {
    /**
     * Tag Label
     * @type {string}
     */
    label?: string;

    attempt?: number;

    /**
     * Tag Label
     * @type {string}
     */
    status?: string;

    /**
     * Tag Label
     * @type {string}
     */
    delivery?: string;

    /**
     * On click handler
     * @type {() => void}
     * @default undefined
     * @optional
     */
    onClick?: () => void;

    /**
     * color
     * @type {string}
     */
    color?: string;

    /**
     * disabled
     * @type {boolean}
     * @default true
     */
    disabled?: boolean;
}
