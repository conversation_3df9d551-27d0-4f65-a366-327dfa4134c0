import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    chip: {
        gap: 5,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        paddingVertical: 3, // Had to be changed to 2 to match the figma design
        paddingHorizontal: 12,
        borderRadius: 6,
    },
    label: {
        fontSize: 14,
        fontFamily: typography.altTextMedium.fontFamily,
    },
    delivery: {
        overflow: "hidden",
        borderColor: colors.gray[300],
        borderWidth: 0.75,
        borderRadius: 4,
        height: 22,
        width: 70,
    },
    value: {
        fontFamily: typography.fontBold.fontFamily,
        fontSize: 16,
        color: colors.primary["50"],
    },
});
