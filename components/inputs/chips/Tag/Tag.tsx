import React from "react";
import { Text, View, Image } from "react-native";

import chroma from "chroma-js";

import TagProps from "./TagProps";
import { styles } from "./TagStyle";
import colors from "../../../../styles/colors";

export const Tag: React.FC<TagProps> = ({
    attempt,
    label = "pending",
    color = "black",
    status,
    delivery,
    ...props
}) => {
    const handleColor = (color: string): any => {
        try {
            if (validColors.includes(color)) {
                const { color100, color700 } = getColor(color);
                return { color: color100, background: color700 };
            }

            const textColor = chroma(color).darken(3).hex();
            const backgroundChroma = chroma(color);
            const whiteChroma = chroma("white");
            const contrastRatio = chroma.contrast(backgroundChroma, whiteChroma);

            if (contrastRatio > 3.5) {
                return { color: "white", background: color };
            }

            return { color: textColor, background: color };
        } catch {
            return { color: "black", background: "#EAF6FF" };
        }
    };

    return (
        <>
            {status ? (
                <View
                    style={{
                        ...styles.chip,
                        backgroundColor: handleStatus(status).color,
                    }}
                >
                    <Text
                        style={{
                            ...styles?.label,
                            color: chroma(handleStatus(status).color).darken(3).hex(),
                        }}
                        {...props}
                    >
                        {handleStatus(status).name.charAt(0).toUpperCase() + handleStatus(status).name.slice(1)}
                    </Text>
                    {attempt && status === "attempt" && (
                        <Text
                            style={{
                                ...styles?.label,
                                color: chroma(handleStatus(status).color).darken(3).hex(),
                            }}
                            {...props}
                        >
                            {attempt}
                        </Text>
                    )}
                </View>
            ) : delivery ? (
                <View>{handleDelivery(delivery)}</View>
            ) : (
                <View
                    style={{
                        ...styles.chip,
                        backgroundColor: handleColor(color).background,
                    }}
                >
                    <Text
                        style={{
                            ...styles?.label,
                            color: handleColor(color).color,
                        }}
                        {...props}
                    >
                        {label.charAt(0).toUpperCase() + label.slice(1)}
                    </Text>
                </View>
            )}
        </>
    );
};

const validColors = ["primary", "secondary", "blue", "green", "red", "orange", "teal", "cyan", "pink", "gray"];

export const handleStatus = (status: string): any => {
    switch (status) {
        case "abandoned":
            return { color: "#8895ff", name: "Abandoned" };
        case "pending":
            return { color: "#FFF2AC", name: "Pending" };
        case "confirmed":
            return { color: "#c6ed9a", name: "Confirmed" };
        case "uploaded":
            return { color: "#B1E5FF", name: "Uploaded" };
        case "packed":
            return { color: "#B072FF", name: "Packed" };
        case "exchange":
            return { color: "#f5e3f2", name: "Exchange" };
        case "rejected":
            return { color: "#F89E9F", name: "Rejected" };
        case "deposit":
            return { color: "#FFDDB8", name: "Deposit" };
        case "in transit":
            return { color: "#B6E9B6", name: "In transit" };
        case "to be returned":
            return { color: "#FFB28C", name: "To be returned" };
        case "cancelled":
            return { color: "#FF9376", name: "Cancelled" };
        case "delivered":
            return { color: "#AEE6C8", name: "Delivered" };
        case "returned":
            return { color: "#f5e3f2", name: "Returned" };
        case "received":
            return { color: "#F5C3C5", name: "Received" };
        case "deleted":
            return { color: "#EB6A6A", name: "Deleted" };
        case "restored":
            return { color: "#CEFDE7", name: "Restored" };
        case "duplicated":
            return { color: "#efefef", name: "Duplicated" };
        case "test":
            return { color: "#FF7BDA", name: "Test" };
        case "Shown":
            return { color: colors.green[200], name: "Shown" };
        case "Hidden":
            return { color: colors.red[200], name: "Hidden" };
        default:
            return { color: "#FFBC70", name: status };
    }
};

const getColor = (color: string): { color100: string; color700: string } => {
    switch (color) {
        case "primary":
            return {
                color100: colors.primary[100],
                color700: colors.primary[700],
            };
        case "secondary":
            return {
                color100: colors.secondary[100],
                color700: colors.secondary[700],
            };
        case "blue":
            return { color100: colors.blue[100], color700: colors.blue[700] };
        case "green":
            return { color100: colors.green[100], color700: colors.green[700] };
        case "red":
            return { color100: colors.red[100], color700: colors.red[700] };
        case "orange":
            return {
                color100: colors.orange[100],
                color700: colors.orange[700],
            };
        case "teal":
            return { color100: colors.teal[100], color700: colors.teal[700] };
        case "cyan":
            return { color100: colors.cyan[100], color700: colors.cyan[700] };
        case "pink":
            return { color100: colors.pink[100], color700: colors.pink[700] };
        case "gray":
            return { color100: colors.gray[100], color700: colors.gray[700] };
        default:
            return {
                color100: colors.primary[100],
                color700: colors.primary[700],
            };
    }
};

const CDN = "https://cdn.converty.shop/";

const handleDelivery = (delivery: string): any => {
    switch (delivery) {
        case "zajella":
            return (
                <Image
                    source={{ uri: CDN + "assets/integrations/zajella-tag.webp" }}
                    style={styles.delivery}
                    resizeMode="cover"
                />
            );

        case "none":
            return <Text style={styles.value}>-</Text>;

        default:
            return (
                <Image
                    source={{ uri: CDN + `assets/integrations/${delivery}.webp` }}
                    style={styles.delivery}
                    resizeMode="cover"
                />
            );
    }
};
