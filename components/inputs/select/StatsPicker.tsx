import React from "react";
import { StyleSheet, Text, View } from "react-native";
import SelectCustom from "@components/Navigation/ModalView/SelectCustom";
import { typography } from "@styles/typography";
import colors from "@styles/colors";
import { Item } from "react-native-picker-select";

interface StatsPickerProps {
    label: string;
    items: Item[];
    value: string;
    onValueChange: (value: string) => void;
    placeholder?: Item;
}

const StatsPicker: React.FC<StatsPickerProps> = ({
    label,
    items,
    value,
    onValueChange,
    placeholder = { label: "all", value: "store" },
}) => {
    // Add "all" option to the beginning of the options array
    const allOptions = [{ label: "All", value: "store" }, ...items];

    return (
        <View style={styles.container}>
            <Text style={styles.label}>{label}</Text>
            <SelectCustom
                value={value || "store"} // Set "store" as default if value is empty
                options={allOptions}
                action={onValueChange}
                placeholder="Select option"
                title={label}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        gap: 5,
    },
    label: {
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.xs.fontSize,
        color: colors.gray[700],
    },
});

export default StatsPicker;
