import { Platform, StyleSheet, Text, View } from "react-native";
import React from "react";

import RNPickerSelect, { Item } from "react-native-picker-select";
import { Path, Svg } from "react-native-svg";
import { typography } from "@styles/typography";
import colors from "@styles/colors";

const SelectModal = ({
    action,
    activeItem,
    items,
    placeholder,
}: {
    action: (value: any) => void;
    activeItem: Item;
    items: Item[];
    placeholder?: Item;
}) => {
    return Platform.OS === "ios" ? (
        <RNPickerSelect
            placeholder={placeholder}
            onValueChange={action}
            items={items}
            value={activeItem}
            useNativeAndroidPickerStyle={false}
            Icon={() => (
                <Svg height="24" viewBox="0 -960 960 960" width="24">
                    <Path d="M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z" fill={colors.gray[600]} />
                </Svg>
            )}
            style={{
                iconContainer: {
                    height: "100%",
                    justifyContent: "center",
                    paddingRight: 10,
                },
                inputAndroid: {
                    fontFamily: typography.fontMedium.fontFamily,
                    color: colors.gray[900],
                    borderWidth: 1,
                    borderColor: colors.gray[300],
                    borderRadius: 10,
                    padding: 10,
                    alignItems: "center",
                },
                inputIOS: {
                    fontFamily: typography.fontMedium.fontFamily,
                    color: colors.gray[900],
                    borderWidth: 1,
                    borderColor: colors.gray[300],
                    borderRadius: 10,
                    padding: 10,
                    alignItems: "center",
                },
            }}
        />
    ) : (
        <RNPickerSelect
            placeholder={placeholder}
            onValueChange={action}
            items={items}
            value={activeItem}
            useNativeAndroidPickerStyle={false}
            Icon={() => (
                <Svg height="24" viewBox="0 -960 960 960" width="24">
                    <Path d="M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z" fill={colors.gray[600]} />
                </Svg>
            )}
            style={{
                iconContainer: {
                    height: "100%",
                    justifyContent: "center",
                    paddingRight: 10,
                },
                inputAndroid: {
                    fontFamily: typography.fontMedium.fontFamily,
                    color: colors.gray[900],
                    borderWidth: 1,
                    borderColor: colors.gray[300],
                    borderRadius: 6,
                    padding: 10,
                    alignItems: "center",
                },
            }}
        />
    );
};

export default SelectModal;

const styles = StyleSheet.create({});
