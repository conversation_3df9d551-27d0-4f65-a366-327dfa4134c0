import React from "react";
import { View, Text, StyleSheet } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import ModalBase from "@components/Navigation/ModalView/ModalBase";
import { Order } from "@components/orders/types";
import { BlockCustomerState } from "../../hooks/useBlockCustomer";

interface ActionModalProps {
    visible: boolean;
    state: BlockCustomerState;
    customerOrders: Order[];
    swipeableClose: (() => void) | undefined;
    onClose: () => void;
    onDelete: (deleteId: string) => void;
    onRestore: (restoreId: string) => void;
    onSwipeableClose: () => void;
}

export const ActionModal: React.FC<ActionModalProps> = ({
    visible,
    state,
    customerOrders,
    swipeableClose,
    onClose,
    onDelete,
    onRestore,
    onSwipeableClose,
}) => {
    const currentOrder = customerOrders.find((o) => o._id === state.deleteId);
    const orderReference = currentOrder?.reference;

    const handleCancel = () => {
        if (swipeableClose) {
            swipeableClose();
        }
        onSwipeableClose();
        onClose();
    };

    const handleDelete = () => {
        if (state.deleteId) {
            onDelete(state.deleteId);
        }
        onClose();
    };

    const handleRestore = () => {
        if (state.deleteId) {
            onRestore(state.deleteId);
        }
        onClose();
    };

    return (
        <ModalBase
            visible={visible}
            setVisible={onClose}
            onDismiss={() => {
                if (swipeableClose) {
                    swipeableClose();
                }
                onSwipeableClose();
                onClose();
            }}
        >
            <View style={styles.container}>
                {state.modalMode === "delete" && (
                    <>
                        <Text style={styles.title}>
                            Delete Order{" "}
                            {orderReference && (
                                <Text style={styles.orderReference}>#{orderReference}</Text>
                            )}
                        </Text>
                        <Text style={styles.description}>
                            Are you sure you want to delete Order{" "}
                            <Text style={styles.orderReference}>#{orderReference}</Text>?
                        </Text>
                        <View style={styles.buttonContainer}>
                            <TextButton
                                style={styles.button}
                                label="Cancel"
                                variant="outlined"
                                color={colors.red[500]}
                                onPress={handleCancel}
                            />
                            <TextButton
                                style={styles.button}
                                label="Delete"
                                variant="contained"
                                color={colors.red[500]}
                                onPress={handleDelete}
                            />
                        </View>
                    </>
                )}

                {state.modalMode === "restore" && (
                    <>
                        <Text style={styles.title}>
                            Restore Order{" "}
                            {orderReference && (
                                <Text style={styles.orderReference}>#{orderReference}</Text>
                            )}
                        </Text>
                        <Text style={styles.description}>
                            Are you sure you want to restore Order{" "}
                            <Text style={styles.orderReference}>#{orderReference}</Text>?
                        </Text>
                        <View style={styles.buttonContainer}>
                            <TextButton
                                style={styles.button}
                                label="Cancel"
                                variant="outlined"
                                color={colors.green[500]}
                                onPress={handleCancel}
                            />
                            <TextButton
                                style={styles.button}
                                label="Restore"
                                variant="contained"
                                color={colors.green[500]}
                                onPress={handleRestore}
                            />
                        </View>
                    </>
                )}
            </View>
        </ModalBase>
    );
};

const styles = StyleSheet.create({
    container: {
        gap: 10,
    },
    title: {
        ...typography.fontMedium,
        ...typography.lg,
        color: colors.gray[800],
        textAlign: "center",
    },
    description: {
        ...typography.fontNormal,
        ...typography.md,
        color: colors.gray[800],
    },
    orderReference: {
        ...typography.altTextMedium,
        color: colors.primary[700],
    },
    buttonContainer: {
        flexDirection: "row",
        gap: 10,
    },
    button: {
        flex: 1,
    },
});
