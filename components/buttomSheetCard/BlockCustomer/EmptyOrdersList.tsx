import React from "react";
import { View, Text, StyleSheet } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";

export const EmptyOrdersList: React.FC = () => (
    <View style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>No Orders Found</Text>
        <Text style={styles.emptyText}>There are no orders associated with this customer.</Text>
    </View>
);

const styles = StyleSheet.create({
    emptyContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        paddingHorizontal: 20,
        gap: 8,
    },
    emptyTitle: {
        ...typography.lg,
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.gray[900],
    },
    emptyText: {
        ...typography.sm,
        color: colors.gray[600],
        textAlign: "center",
    },
});
