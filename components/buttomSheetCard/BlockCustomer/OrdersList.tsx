import React from "react";
import { View, ActivityIndicator, StyleSheet, Platform } from "react-native";
import { FlatList } from "react-native-gesture-handler";
import colors from "@styles/colors";
import { Order } from "@components/orders/types";
import OrderCard from "@components/cards/orders/OrderCard";
import { EmptyOrdersList } from "./EmptyOrdersList";
import { getAndroidOptimizations } from "../../utils/blockCustomerUtils";
import { BlockCustomerState } from "../../hooks/useBlockCustomer";

interface OrdersListProps {
    orders: Order[];
    state: BlockCustomerState;
    setState: React.Dispatch<React.SetStateAction<BlockCustomerState>>;
    handleSetSwipeableClose: (callback: () => void) => void;
    bottomSheetRef: React.RefObject<any>;
}

export const OrdersList: React.FC<OrdersListProps> = ({
    orders,
    state,
    setState,
    handleSetSwipeableClose,
    bottomSheetRef,
}) => {
    const androidOptimizations = getAndroidOptimizations(orders.length);

    if (state.loading || !state.dataReady) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary[500]} />
            </View>
        );
    }

    if (orders.length === 0) {
        return <EmptyOrdersList />;
    }

    return (
        <FlatList
            data={orders && Array.isArray(orders) ? [...orders] : []}
            renderItem={({ item }) => (
                <OrderCard
                    key={item._id}
                    order={item}
                    selectionMode={false}
                    selectedOrdersId={state.selectedOrdersId}
                    setSelectedOrdersId={(ids) =>
                        setState((prev) => ({ ...prev, selectedOrdersId: ids }))
                    }
                    setDeleteId={(id) =>
                        setState((prev) => ({
                            ...prev,
                            deleteId: id,
                            modalVisible: true,
                            modalMode: item.status === "deleted" ? "restore" : "delete",
                        }))
                    }
                    setSwipeableClose={handleSetSwipeableClose}
                    setModalVisible={(visible) =>
                        setState((prev) => ({ ...prev, modalVisible: visible }))
                    }
                    setModalMode={(mode) => setState((prev) => ({ ...prev, modalMode: mode }))}
                    disableInteractions={true}
                    isBlockCustomerView={true}
                />
            )}
            keyExtractor={(item: Order, index) => `${item._id}-${index}`}
            contentContainerStyle={[
                styles.ordersList,
                { width: "100%", paddingBottom: 15, paddingTop: 5 },
            ]}
            showsVerticalScrollIndicator={true}
            scrollEnabled={orders && Array.isArray(orders) && orders.length >= 3}
            {...(Platform.OS === "android" && androidOptimizations)}
            extraData={state.dataReady}
            waitFor={bottomSheetRef}
            simultaneousHandlers={bottomSheetRef}
        />
    );
};

const styles = StyleSheet.create({
    loadingContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
    ordersList: {
        gap: 12,
        paddingBottom: 16,
    },
});
