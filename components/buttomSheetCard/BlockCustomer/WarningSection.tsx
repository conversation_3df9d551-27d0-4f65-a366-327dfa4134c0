import React from "react";
import { View, Text, StyleSheet } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { WarningIcon } from "@components/icons/BlockCustomerIcons";

interface WarningSectionProps {
    shouldDeleteOrders: boolean;
}

export const WarningSection: React.FC<WarningSectionProps> = ({ shouldDeleteOrders }) => (
    <View style={[styles.warningSection, { borderWidth: 1, borderColor: colors.secondary[600] }]}>
        <View style={styles.warningHeader}>
            <WarningIcon size={24} color={colors.secondary[600]} />
            <Text style={styles.warningTitle}>Suspicious Activity Detected</Text>
        </View>
        <Text style={styles.warningText}>
            This customer has placed multiple orders in a short time span.
            {shouldDeleteOrders ? "\nWarning: All orders from this customer will be deleted." : ""}
        </Text>
    </View>
);

const styles = StyleSheet.create({
    warningSection: {
        backgroundColor: colors.secondary[50],
        padding: 16,
        borderRadius: 12,
        gap: 8,
    },
    warningHeader: {
        flexDirection: "row",
        alignItems: "center",
        gap: 8,
    },
    warningTitle: {
        ...typography.lg,
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.secondary[600],
    },
    warningText: {
        ...typography.sm,
        color: colors.secondary[600],
    },
});
