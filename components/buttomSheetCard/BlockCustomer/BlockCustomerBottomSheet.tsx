// Re-export the refactored component for backward compatibility
import React, { useRef, useCallback } from "react";
import {
    View,
    Text,
    Switch,
    TouchableOpacity,
    ActivityIndicator,
} from "react-native";
import BottomSheet, { BottomSheetBackdrop } from "@gorhom/bottom-sheet";
import colors from "@styles/colors";

import { blockCustomerStyles } from "../../../styles/blockCustomerStyles";
import { useBlockCustomer, BlockCustomerBottomSheetProps } from "../../../hooks/useBlockCustomer";
import { WarningSection } from "./WarningSection";
import { OrdersList } from "./OrdersList";
import { ActionModal } from "./ActionModal";

export const BlockCustomerBottomSheet: React.FC<BlockCustomerBottomSheetProps> = (props) => {
    const { visible, onClose } = props;
    const bottomSheetRef = useRef<BottomSheet>(null);

    const {
        state,
        customerOrders,
        showWarning,
        snapPoints,
        swipeableClose,
        actions,
    } = useBlockCustomer(props);

    const renderBackdrop = useCallback(
        (backdropProps: any) => (
            <BottomSheetBackdrop
                {...backdropProps}
                disappearsOnIndex={-1}
                appearsOnIndex={0}
                opacity={0.5}
            />
        ),
        []
    );

    React.useEffect(() => {
        if (visible) {
            bottomSheetRef.current?.expand();
        } else {
            bottomSheetRef.current?.close();
        }
    }, [visible]);

    return (
        <>
            <BottomSheet
                ref={bottomSheetRef}
                index={visible ? 0 : -1}
                snapPoints={snapPoints}
                enablePanDownToClose={true}
                onChange={actions.handleSheetChanges}
                backdropComponent={renderBackdrop}
                backgroundStyle={blockCustomerStyles.sheetBackground}
                handleIndicatorStyle={blockCustomerStyles.handleIndicator}
                animateOnMount={true}
            >
                <View style={blockCustomerStyles.container}>
                    <View style={blockCustomerStyles.header}>
                        <View style={blockCustomerStyles.totalOrdersContainer}>
                            <Text style={blockCustomerStyles.totalOrders}>
                                Total Orders:{" "}
                                {state.loading ? (
                                    <ActivityIndicator size="small" color={colors.primary[500]} />
                                ) : (
                                    <Text style={blockCustomerStyles.totalOrdersNumber}>
                                        {customerOrders.length}
                                    </Text>
                                )}
                            </Text>
                        </View>

                        {showWarning && (
                            <WarningSection shouldDeleteOrders={state.shouldDeleteOrders} />
                        )}
                    </View>

                    <View style={blockCustomerStyles.ordersSection}>
                        <OrdersList
                            orders={customerOrders}
                            state={state}
                            setState={actions.setState}
                            handleSetSwipeableClose={actions.handleSetSwipeableClose}
                            bottomSheetRef={bottomSheetRef}
                        />
                    </View>

                    <View style={blockCustomerStyles.footer}>
                        <View style={blockCustomerStyles.switchContainer}>
                            <Text style={blockCustomerStyles.switchText}>
                                Delete all orders from this customer
                            </Text>
                            <Switch
                                value={state.shouldDeleteOrders}
                                onValueChange={(value) =>
                                    actions.setState((prev) => ({ ...prev, shouldDeleteOrders: value }))
                                }
                                trackColor={{ false: colors.gray[300], true: colors.red[200] }}
                                thumbColor={state.shouldDeleteOrders ? colors.red[500] : colors.gray[500]}
                            />
                        </View>

                        <View style={blockCustomerStyles.buttonContainer}>
                            <TouchableOpacity
                                style={blockCustomerStyles.cancelButton}
                                onPress={onClose}
                                disabled={state.loading}
                            >
                                <Text style={blockCustomerStyles.cancelButtonText}>Cancel</Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={blockCustomerStyles.blockButton}
                                onPress={actions.handleBlockCustomer}
                                disabled={state.loading}
                            >
                                {state.loading ? (
                                    <ActivityIndicator size="small" color={colors.white} />
                                ) : (
                                    <Text style={blockCustomerStyles.blockButtonText}>Block Customer</Text>
                                )}
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </BottomSheet>

            <ActionModal
                visible={state.modalVisible}
                state={state}
                customerOrders={customerOrders}
                swipeableClose={swipeableClose}
                onClose={() => actions.setState((prev) => ({ ...prev, modalVisible: false }))}
                onDelete={actions.handleDeleteOrder}
                onRestore={actions.handleRestoreOrder}
                onSwipeableClose={() => actions.setSwipeableClose(undefined)}
            />
        </>
    );
};
