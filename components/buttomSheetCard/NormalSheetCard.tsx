import { StyleSheet, Text, View, ViewStyle } from "react-native";
import React from "react";
import { IconProp } from "@components/stats/Props";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import Svg, { G, Mask, Path, Rect } from "react-native-svg";
import { Elipses } from "@app/budgetManager";

const NormalSheetCard = ({
    icon: Icon,
    value,
    valueUnit = "TND",
    label,
    variant,
    style,
}: {
    icon: React.FC<IconProp>;
    value?: number;
    valueUnit?: string;
    label?: string;
    variant: keyof typeof colors;
    style?: ViewStyle;
}) => {
    const backgroundColor = colors[variant][500];
    const textColor = colors.gray[600];

    return (
        <View
            style={[
                {
                    paddingHorizontal: 10,
                    paddingVertical: 5,
                    borderRadius: 10,
                    backgroundColor: backgroundColor,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                    elevation: 2,
                    shadowColor: colors.black,
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.2,
                    shadowRadius: 2,
                },
                style,
            ]}
        >
            <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Icon color={textColor} />
                {label && <Text style={[styles?.labelText, { color: textColor, paddingLeft: 10 }]}>{label}</Text>}
            </View>

            <View style={styles.elipsesContainer}>{/* <Elipses color={variant} /> */}</View>
            <View>
                <Text style={[styles.valueText, { color: textColor }]}>
                    {value?.toLocaleString("en-US")}
                    <Text style={styles.unitText}>{"" + valueUnit}</Text>
                </Text>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    unitText: {
        fontSize: typography.xs.fontSize,
        fontFamily: typography.fontBold.fontFamily,
        // textTransform: "uppercase",
    },
    labelText: {
        fontSize: typography.md.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    valueText: {
        fontSize: typography.xl.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    elipsesContainer: {
        position: "absolute",
        right: 10,
        top: 20,
        bottom: 0,
        left: 50,
        justifyContent: "center",
        alignItems: "center",
        opacity: 0.2,
        zIndex: -1,
    },
});

export default NormalSheetCard;

export const ShopIcon = ({ color }: IconProp) => {
    return (
        <Svg width="16" height="20" viewBox="0 0 16 20" fill={color}>
            <Path d="M14 4H12C12 1.79 10.21 0 8 0C5.79 0 4 1.79 4 4H2C0.9 4 0 4.9 0 6V18C0 19.1 0.9 20 2 20H14C15.1 20 16 19.1 16 18V6C16 4.9 15.1 4 14 4ZM8 2C9.1 2 10 2.9 10 4H6C6 2.9 6.9 2 8 2ZM14 18H2V6H4V8C4 8.55 4.45 9 5 9C5.55 9 6 8.55 6 8V6H10V8C10 8.55 10.45 9 11 9C11.55 9 12 8.55 12 8V6H14V18Z" />
        </Svg>
    );
};
export const TruckIcon = ({ color }: IconProp) => {
    return (
        <Svg width="20" height="16" viewBox="0 0 20 16" fill={color}>
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M0.585786 0.585787C0.960859 0.210714 1.46957 0 2 0H10C10.5304 0 11.0391 0.210714 11.4142 0.585787C11.7893 0.960859 12 1.46957 12 2H14.586C15.1163 2.00011 15.6251 2.21086 16.0001 2.58589C16.0001 2.58586 16.0001 2.58593 16.0001 2.58589L19.414 5.99979C19.7891 6.37477 19.9999 6.8834 20 7.41379V12C20 12.5304 19.7893 13.0391 19.4142 13.4142C19.0391 13.7893 18.5304 14 18 14H17.8284C17.6807 14.4179 17.4407 14.8019 17.1213 15.1213C16.5587 15.6839 15.7957 16 15 16C14.2043 16 13.4413 15.6839 12.8787 15.1213C12.5593 14.8019 12.3193 14.4179 12.1716 14H12C11.6459 14 11.3015 13.9061 11 13.7321C10.6985 13.9061 10.3541 14 10 14H7.82843C7.68067 14.4179 7.44072 14.8019 7.12132 15.1213C6.55871 15.6839 5.79565 16 5 16C4.20435 16 3.44129 15.6839 2.87868 15.1213C2.55927 14.8019 2.31933 14.4179 2.17157 14H2C1.46957 14 0.960861 13.7893 0.585786 13.4142C0.210713 13.0391 0 12.5304 0 12V2C0 1.46957 0.210714 0.960859 0.585786 0.585787ZM4 13C4 13.2652 4.10536 13.5196 4.29289 13.7071C4.48043 13.8946 4.73478 14 5 14C5.26522 14 5.51957 13.8946 5.70711 13.7071C5.89464 13.5196 6 13.2652 6 13C6 12.7348 5.89464 12.4804 5.70711 12.2929C5.51957 12.1054 5.26522 12 5 12C4.73478 12 4.48043 12.1054 4.29289 12.2929C4.10536 12.4804 4 12.7348 4 13ZM7.82843 12H10V2L2 2V12H2.17157C2.31933 11.5821 2.55927 11.1981 2.87868 10.8787C3.44129 10.3161 4.20435 10 5 10C5.79565 10 6.55871 10.3161 7.12132 10.8787C7.44072 11.1981 7.68067 11.5821 7.82843 12ZM12 12H12.1716C12.3193 11.5821 12.5593 11.1981 12.8787 10.8787C13.4413 10.3161 14.2043 10 15 10C15.7957 10 16.5587 10.3161 17.1213 10.8787C17.4407 11.1981 17.6807 11.5821 17.8284 12H18V7.41421L14.5859 4.00011L12 4V12ZM15 12C14.7348 12 14.4804 12.1054 14.2929 12.2929C14.1054 12.4804 14 12.7348 14 13C14 13.2652 14.1054 13.5196 14.2929 13.7071C14.4804 13.8946 14.7348 14 15 14C15.2652 14 15.5196 13.8946 15.7071 13.7071C15.8946 13.5196 16 13.2652 16 13C16 12.7348 15.8946 12.4804 15.7071 12.2929C15.5196 12.1054 15.2652 12 15 12Z"
            />
        </Svg>
    );
};
export const BrokenIcon = ({ color }: IconProp) => {
    return (
        <Svg width="24" height="24" viewBox="0 0 24 24" fill={color}>
            <Mask id="mask0_10952_14744" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                <Rect width="24" height="24" fill="#D9D9D9" />
            </Mask>
            <G mask="url(#mask0_10952_14744)">
                <Path
                    d="M16.65 12.9992L11 7.34922L16.65 1.69922L22.3 7.34922L16.65 12.9992ZM3 10.9992V2.99922H11V10.9992H3ZM13 20.9992V12.9992H21V20.9992H13ZM3 20.9992V12.9992H11V20.9992H3ZM5 8.99922H9V4.99922H5V8.99922ZM16.675 10.1992L19.5 7.37422L16.675 4.54922L13.85 7.37422L16.675 10.1992ZM15 18.9992H19V14.9992H15V18.9992ZM5 18.9992H9V14.9992H5V18.9992Z"
                    // fill="white"
                />
            </G>
        </Svg>
    );
};

export const CompareIcon = ({ color }: IconProp) => {
    return (
        <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <Path d="M9.01 14H2V16H9.01V19L13 15L9.01 11V14ZM14.99 13V10H22V8H14.99V5L11 9L14.99 13Z" fill={color} />
        </Svg>
    );
};
