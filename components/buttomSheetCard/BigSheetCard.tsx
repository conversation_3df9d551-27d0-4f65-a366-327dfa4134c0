import { StyleSheet, Text, View, ViewStyle } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { IconProp } from "@components/stats/Props";
import { Elipses } from "@app/budgetManager";

const BigSheetCard = ({
    variant,
    MainIcon,
    mainValue,
    mainValueLabel,
    mainValueUnit,
    style,
}: {
    variant: keyof typeof colors;
    MainIcon: React.FC<IconProp>;
    mainValue?: number;
    mainValueLabel?: string;
    mainValueUnit?: string;
    style?: ViewStyle;
}) => {
    const backgroundColor = colors[variant][500];
    const textColor = colors.white;

    return (
        <View
            style={[
                {
                    padding: 20,
                    borderRadius: 10,
                    backgroundColor: backgroundColor,
                    flexDirection: "column",
                    alignItems: "flex-start",
                    justifyContent: "center",
                    elevation: 2,
                    position: "relative",
                    overflow: "hidden",
                },
                style,
            ]}
        >
            <View style={cardStyles.elipsesContainer}>
                <Elipses color={variant} />
            </View>

            <View style={{ flexDirection: "row", alignItems: "center" }}>
                <MainIcon color={textColor} />
                <Text style={[cardStyles.mainLabel, { color: textColor, marginLeft: 8, fontSize: 25 }]}>
                    {mainValueLabel}
                </Text>
            </View>

            <View style={{ flexDirection: "row", alignItems: "baseline" }}>
                <Text style={[cardStyles.mainValue, { color: textColor, fontSize: 30 }]}>
                    {mainValue?.toLocaleString("en-US", { minimumFractionDigits: 2 })}
                </Text>
                {mainValueUnit && (
                    <Text style={[cardStyles.mainUnit, { color: textColor, fontSize: 18, marginLeft: 4 }]}>
                        {mainValueUnit}
                    </Text>
                )}
            </View>
        </View>
    );
};

export default BigSheetCard;

const cardStyles = StyleSheet.create({
    elipsesContainer: {
        position: "absolute",
        right: 50,
        top: 10,
        bottom: 0,
        left: 5,
        justifyContent: "center",
        alignItems: "center",
        opacity: 0.2,
    },
    mainValue: {
        fontSize: typography.xl.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    mainLabel: {
        fontSize: typography.sm.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    mainUnit: {
        fontSize: typography.xxs.fontSize,
        fontFamily: typography.fontNormal.fontFamily,
        textTransform: "uppercase",
    },
});
