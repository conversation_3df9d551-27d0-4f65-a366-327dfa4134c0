import { StyleSheet, Text, View, ViewStyle } from "react-native";
import { Color } from "@components/charts/HomeChart/HomeChartV2";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { IconProp } from "@components/stats/Props";
import { ColorsProp, Elipses } from "@app/budgetManager";
import { ViewProps } from "react-native-svg/lib/typescript/fabric/utils";

const BottomSheetCard = ({
    variant,
    MainIcon,
    SecondaryIcon,
    TertiaryIcon,
    mainValue,
    mainValueUnit = "unit",
    mainValueLabel,
    secondaryValue,
    secondaryValueUnit = "tnd",
    secondaryValueLabel,
    tertiaryValue,
    tertiaryValueUnit = "tnd",
    tertiaryValueLabel,
    color,
    simple,
    soft,
    style,
}: {
    color?: string;
    variant: Color;
    MainIcon: React.FC<IconProp>;
    mainValue?: number;
    mainValueUnit?: string;
    mainValueLabel?: string;
    SecondaryIcon?: React.FC<IconProp>;
    secondaryValue?: number;
    secondaryValueUnit?: string;
    secondaryValueLabel?: string;
    TertiaryIcon?: React.FC<IconProp>;
    tertiaryValue?: number;
    tertiaryValueUnit?: string;
    tertiaryValueLabel?: string;
    simple?: boolean;
    soft?: boolean;
    style?: ViewStyle;
}) => {
    const backgroundColor = colors[variant][500];
    const textColor = colors.white;
    return (
        <View
            style={[
                {
                    paddingHorizontal: 10,
                    paddingVertical: 10,
                    borderRadius: 10,
                    backgroundColor: backgroundColor,
                    flexDirection: "row",
                    alignItems: "flex-end",
                    justifyContent: "space-between",
                    elevation: 2,
                    gap: 5,
                    overflow: "hidden",
                },
                style,
            ]}
        >
            <View
                style={{
                    position: "absolute",
                    right: 50,
                    height: "100%",
                    width: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: "pink",
                    overflow: "visible",
                }}
            >
                <Elipses color={variant} key={Math.random()} />
            </View>
            <View
                style={{
                    alignSelf: "stretch",
                    justifyContent: "space-between",
                    overflow: "visible",
                }}
            >
                <MainIcon color={textColor} />
                <View style={{ gap: -5, overflow: "visible" }}>
                    <View
                        style={{
                            flexDirection: "row",
                            alignItems: "baseline",
                            flexWrap: "wrap",

                            columnGap: 2,
                            rowGap: -10,
                        }}
                    >
                        <Text style={[cardStyles.mainValue, { color: textColor }]}>
                            {mainValue?.toLocaleString("en-US")}
                        </Text>
                        <Text style={[cardStyles.mainUnit, { color: textColor }]}>{mainValueUnit}</Text>
                    </View>
                    <Text style={[cardStyles.mainLabel, { color: textColor }]}>{mainValueLabel}</Text>
                </View>
            </View>

            <View style={{ gap: 5 }}>
                <View style={[{ alignItems: "flex-end" }, simple ? { opacity: 0 } : {}]}>
                    <Text style={[cardStyles.secondaryValue, { color: textColor }]}>
                        {/* <SecondaryIcon color={textColor} /> */}

                        {secondaryValue?.toLocaleString()}
                        <Text style={cardStyles.secondaryUnit}>{" " + secondaryValueUnit}</Text>
                    </Text>

                    <View style={cardStyles.secondaryContainer}>
                        <Text style={[cardStyles.secondaryLabel, { color: textColor }]}>{secondaryValueLabel}</Text>
                    </View>
                </View>

                {soft ? null : (
                    <View style={[{ alignItems: "flex-end" }, simple ? { opacity: 0 } : {}]}>
                        <Text style={[cardStyles.secondaryValue, { color: textColor }]}>
                            {tertiaryValue?.toLocaleString()}
                            <Text style={cardStyles.secondaryUnit}>{" " + tertiaryValueUnit}</Text>
                        </Text>
                        <View style={cardStyles.secondaryContainer}>
                            {TertiaryIcon && <TertiaryIcon color={textColor} />}
                            <Text style={[cardStyles.secondaryLabel, { color: textColor }]}>{tertiaryValueLabel}</Text>
                        </View>
                    </View>
                )}
            </View>
        </View>
    );
};

export default BottomSheetCard;

const cardStyles = StyleSheet.create({
    mainValue: {
        fontSize: typography.xl.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    mainLabel: {
        fontSize: typography.sm.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    mainUnit: {
        fontSize: typography.xs.fontSize,
        fontFamily: typography.fontNormal.fontFamily,
    },

    secondaryContainer: {
        flexDirection: "row",
        alignItems: "center",
        gap: 2,
    },

    secondaryValue: {
        fontSize: typography.lg.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    secondaryLabel: {
        fontSize: typography.sm.fontSize,
        fontFamily: typography.fontBold.fontFamily,
    },
    secondaryUnit: {
        fontSize: typography.xxs.fontSize,
        fontFamily: typography.fontNormal.fontFamily,
        textTransform: "uppercase",
    },
});
