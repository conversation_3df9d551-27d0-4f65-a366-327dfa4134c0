import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Href, useRouter } from "expo-router";
import React from "react";
import { Text, TouchableOpacity, View } from "react-native";

const AccountListItem = ({
    icon,
    label,
    navigation,
    route,
    action,
}: {
    icon: React.ReactNode;
    label: string;
    navigation?: any;
    route?: Href<string>;
    action?: (props: any) => any;
}) => {
    const router = useRouter();
    return (
        <TouchableOpacity
            onPress={
                route
                    ? () => {
                          router.navigate(route);
                      }
                    : action
                    ? action
                    : () => {}
            }
        >
            <View
                style={{
                    flexDirection: "row",
                    paddingVertical: 5,
                    paddingHorizontal: 15,
                    alignItems: "center",
                    gap: 10,
                }}
            >
                {icon}
                <Text
                    style={{
                        color: colors.primary[700],
                        fontFamily: typography.fontNormal.fontFamily,
                        fontSize: typography.md.fontSize,
                        textTransform: "capitalize",
                    }}
                >
                    {label}
                </Text>
            </View>
        </TouchableOpacity>
    );
};

export default AccountListItem;
