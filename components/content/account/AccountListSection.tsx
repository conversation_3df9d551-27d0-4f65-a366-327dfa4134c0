import colors from "@styles/colors";
import { typography } from "@styles/typography";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

const SectionHeader = ({ label }: { label: string }) => {
    return (
        <View style={styles.header}>
            <Text style={styles?.label}>{label}</Text>
        </View>
    );
};

const AccountListSection = ({ label, children }: { label: string; children?: React.ReactNode }) => {
    return (
        <View style={styles.section}>
            {label && <SectionHeader label={label} />}
            {children}
        </View>
    );
};
const styles = StyleSheet.create({
    section: {
        gap: 5,
        alignSelf: "stretch",
    },
    header: { paddingHorizontal: 15, paddingVertical: 5, flexDirection: "row" },
    label: {
        color: colors.primary[800],
        /* text-md/lineHeight-6/font-medium */
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: 16,
        textTransform: "capitalize",
    },
});

export default AccountListSection;
