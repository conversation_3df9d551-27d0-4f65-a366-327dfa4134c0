import AccountListDivider from "@components/dividers/AccountListDivider";
import AccountPages from "@components/pages/account/AccountPages";
import { View } from "react-native";
import { useStoreStore } from "../../../store/storeStore";
import AccountListItem from "./AccountListItem";
import AccountListSection from "./AccountListSection";
import Button from "@components/inputs/buttons/Button";
import { router } from "expo-router";
import { useEffect, useState } from "react";

const AccountListContent = ({ navigation }: { navigation: any }) => {
    const { user } = useStoreStore();
    console.log(user);

    const [_user, setUser] = useState(user);

    return (
        <View
            style={{
                gap: 10,
                alignItems: "stretch",
                alignSelf: "stretch",
                flexShrink: 0,
            }}
        >
            {AccountPages?.filter((section) =>
                section?.items?.some((item) => {
                    if (item)
                        return (
                            _user?.permissions === "all" ||
                            !item?.permission ||
                            _user?.permissions[item?.permission]?.read
                        );
                })
            ).map((section, key) => {
                return [
                    <AccountListSection label={section?.sectionLabel} key={2 * key}>
                        {section.items
                            .filter(
                                (item) =>
                                    _user?.permissions === "all" ||
                                    !item.permission ||
                                    _user?.permissions[item.permission]?.read
                            )
                            .map((item, key) => {
                                return (
                                    <AccountListItem
                                        navigation={navigation}
                                        icon={item.icon}
                                        label={item?.label}
                                        key={key}
                                        route={item.route}
                                        action={item.action}
                                    />
                                );
                            })}
                        {/* <Button
                            action={() => {
                                router.navigate("/(modals)/notificationSound");
                            }}
                            label="notification sound"
                        /> */}
                    </AccountListSection>,
                    <AccountListDivider key={2 * key + 1} />,
                ];
            })}
        </View>
    );
};

export default AccountListContent;
