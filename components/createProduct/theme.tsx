import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import colors from "@styles/colors";
import Input from "@components/inputs/textInputs/Input";
import { TabsContentCellDeleteBundles } from "@components/icons/createOrderIcons";

// Spacer Component
const Spacer = ({ height = 15 }) => <View style={{ height }} />;

const Theme = () => {
    const [formValues, setFormValues] = useState<any>({
        customer: {},
        cart: [],
        status: "pending",
        total: {
            deliveryPrice: 0,
            deliveryCost: 0,
            totalPrice: 0,
        },
        deliveryCompany: "",
        note: "",
    });

    return (
        <ScrollView
            style={{
                paddingHorizontal: 10,
                paddingTop: 10,
                backgroundColor: colors.gray[50],
            }}
        >
            <View
                style={{
                    borderWidth: 0.5,
                    borderColor: colors.gray[500],
                    borderRadius: 10,
                    paddingHorizontal: 10,
                }}
            >
                <Text style={{ ...styles?.label, paddingTop: 20 }}>Title badge</Text>
                <Input
                    placeholder="Title badge"
                    inputProps={{
                        defaultValue: "",
                    }}
                    onChange={(value) => {
                        setFormValues({ ...formValues, note: value });
                    }}
                />
            </View>

            <Spacer height={15} />

            <View
                style={{
                    borderWidth: 0.5,
                    borderColor: colors.gray[500],
                    borderRadius: 10,
                    paddingHorizontal: 10,
                    paddingVertical: 15,
                }}
            >
                <Text style={styles?.label}>Name</Text>
                <View style={{ flexDirection: "row", justifyContent: "space-between", gap: 10 }}>
                    <Input
                        containerStyle={{ flex: 1 }}
                        placeholder="Name"
                        inputProps={{
                            defaultValue: "",
                        }}
                        onChange={(value) => {
                            setFormValues({ ...formValues, note: value });
                        }}
                        noBottomPadding
                    />
                    <TouchableOpacity
                        style={{
                            backgroundColor: colors.red[100],
                            padding: 12,
                            borderRadius: 10,
                        }}
                    >
                        <TabsContentCellDeleteBundles />
                    </TouchableOpacity>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    label: {
        fontSize: 14,
        fontWeight: "bold",
        color: colors.gray[800],
    },
});

export default Theme;
