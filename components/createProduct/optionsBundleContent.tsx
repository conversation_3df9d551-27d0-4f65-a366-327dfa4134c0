import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import {
    View,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    Text,
    ScrollView,
    Modal,
    Pressable,
    ViewStyle,
} from "react-native";
import { Icon } from "react-native-elements";
import RNPickerSelect, { Item } from "react-native-picker-select";
import colors from "@styles/colors";
import { TabsContentCellDeleteBundles } from "@components/icons/createOrderIcons";
import Input from "@components/inputs/textInputs/Input";
import Svg, { Path } from "react-native-svg";
import { typography } from "@styles/typography";
import SelectModal from "@components/inputs/select/SelectModal";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import { AddIcon } from "@components/icons/BudgetManagerIcons";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { Tag } from "@components/inputs/chips/Tag/Tag";
import FilterChip from "@components/stats/FilterChip";
import ColorPicker, { Panel1, Swatches, Preview, OpacitySlider, HueSlider } from "reanimated-color-picker";
import { Button } from "react-native";

type ContentProps = {
    index: number;
    onDelete: (index: number) => void;
};

type OptionProps = {
    name: string;
    type: "text" | "image" | "color";
    values: string[];
};

const Option: React.FC<ContentProps> = ({ index, onDelete }) => {
    const [formValues, setFormValues] = useState<OptionProps>({ name: "", type: "text", values: [] });

    const [colorPickerVisible, setColorPickerVisible] = useState(false);
    useEffect(() => {
        console.log({ colorPickerVisible });
    }, [colorPickerVisible]);

    const items = [
        { label: "text", value: "text" },
        { label: "color", value: "color" },
        { label: "image", value: "image" },
    ];
    const handleTypeChange = (selectedType: "text" | "image" | "color") => {
        setFormValues((prevValues) => ({
            ...prevValues,
            type: selectedType,
            values: selectedType === "color" ? [] : prevValues.values,
        }));
    };

    const handleSelectColor = () => {
        if (selectedColor) {
            setFormValues((prevValues) => ({
                ...prevValues,
                values: [...prevValues.values, selectedColor],
            }));
            setSelectedColor(undefined);
        }
    };

    const [selectedColor, setSelectedColor] = useState<string>();

    return (
        <View
            style={{
                borderWidth: 0.5,
                borderColor: colors.gray[500],
                borderRadius: 10,
                paddingHorizontal: 10,
                paddingVertical: 10,
            }}
        >
            <View style={{ ...styles.row }}>
                <View style={styles.column}>
                    <Text style={styles?.label}>Option name</Text>
                    <Input
                        placeholder="Option name"
                        inputProps={{ defaultValue: formValues.name }}
                        onChange={(value) => setFormValues({ ...formValues, name: value })}
                    />
                </View>
                <View style={styles.column}>
                    <Text style={styles?.label}>Type</Text>
                    <SelectModal
                        items={items}
                        action={(e) => {
                            console.log("Item selected:", e);
                            handleTypeChange(e as "text" | "image" | "color");
                        }}
                        activeItem={{ label: formValues.type, value: formValues.type }}
                    />
                </View>
            </View>

            {/* <View style={{ position: "absolute", width: 300 }}>
                <ColorPicker onComplete={onSelectColor}>
                    <Preview />
                    <Panel1 />
                    <HueSlider />
                </ColorPicker>
            </View> */}

            <View style={{ ...styles.column }}>
                <Text style={styles?.label}>Values</Text>

                <View style={{ flexDirection: "row", justifyContent: "space-between", gap: 10, alignItems: "center" }}>
                    {formValues.type === "text" && (
                        <Input
                            containerStyle={{ flex: 1 }}
                            placeholder="Values"
                            inputProps={{
                                defaultValue: "",
                            }}
                            onChange={(value) => {
                                setFormValues({ ...formValues, values: [value] });
                            }}
                            noBottomPadding
                            onSubmitEditing={() => {
                                console.log("done");
                            }}
                        />
                    )}

                    {formValues.type === "color" && (
                        <>
                            <View style={{ width: 280 }}>
                                <TextButton
                                    label="Add Color"
                                    onPress={() => setColorPickerVisible(true)}
                                    leftIcon={<AddIcon color="white" size="18" />}
                                />
                            </View>
                        </>
                    )}
                    <Modal visible={colorPickerVisible} transparent={true} animationType="fade">
                        <View
                            // onPress={() => {
                            //     setColorPickerVisible(false);
                            // }}
                            style={{
                                height: "100%",
                                backgroundColor: "rgba(0, 0, 0, 0.5)",
                            }}
                        >
                            <Padding
                                action={() => {
                                    setColorPickerVisible(false);
                                }}
                                style={{ flexGrow: 1 }}
                            />
                            <View style={{ flexDirection: "row", justifyContent: "center" }}>
                                <Padding
                                    action={() => {
                                        setColorPickerVisible(false);
                                    }}
                                    style={{ flexGrow: 1 }}
                                />

                                <View
                                    style={{
                                        backgroundColor: "white",
                                        width: 300,
                                        alignItems: "stretch",
                                        borderRadius: 15,
                                        gap: 10,
                                        padding: 15,
                                    }}
                                >
                                    <Text style={[typography.fontMedium, typography.md, { alignSelf: "center" }]}>
                                        Select Color
                                    </Text>
                                    <ColorPicker
                                        value={selectedColor}
                                        onComplete={(e) => {
                                            setSelectedColor(e.hex);
                                        }}
                                        style={{ width: "100%", backgroundColor: "white", gap: 15 }}
                                    >
                                        <Preview />

                                        <HueSlider />

                                        <Panel1 />
                                    </ColorPicker>
                                    <TextButton
                                        label="Confirm"
                                        onPress={() => {
                                            handleSelectColor();
                                            setColorPickerVisible(false);
                                        }}
                                    />
                                </View>
                                <Padding
                                    action={() => {
                                        setColorPickerVisible(false);
                                    }}
                                    style={{ flexGrow: 1 }}
                                />
                            </View>
                            <Padding
                                action={() => {
                                    setColorPickerVisible(false);
                                }}
                                style={{ flexGrow: 1 }}
                            />
                        </View>
                    </Modal>

                    <TouchableOpacity
                        style={{
                            backgroundColor: colors.red[100],
                            padding: 15,
                            borderRadius: 10,
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                        onPress={() => onDelete(index)}
                    >
                        <TabsContentCellDeleteBundles />
                    </TouchableOpacity>
                </View>
                <View style={{ flexDirection: "row", flexWrap: "wrap", gap: 5, marginTop: 10 }}>
                    {formValues.values.map((color, idx) => (
                        <View
                            key={idx}
                            style={{
                                width: 30,
                                height: 30,
                                backgroundColor: color,
                                borderRadius: 5,
                                borderWidth: 1,
                                borderColor: colors.gray[300],
                            }}
                        />
                    ))}
                </View>
            </View>
        </View>
    );
};

const Padding = ({ action, style }: { action?: () => void; style?: ViewStyle }) => {
    return <View onTouchStart={action ? action : () => {}} style={[{}, style]} />;
};

const OptionsBundlesContent = () => {
    const [contentComponents, setContentComponents] = useState<number[]>([0]);

    const handleAddContent = () => {
        setContentComponents([...contentComponents, contentComponents.length]);
    };
    const handleDeleteContent = (index: number) => {
        const updatedComponents = contentComponents.filter((_, i) => i !== index);
        setContentComponents(updatedComponents);
    };
    const bottomSheetRef = useRef<BottomSheet>(null);
    const handleSheetChanges = useCallback((index: number) => {}, []);
    return (
        <GestureHandlerRootView>
            <ScrollView
                style={{ backgroundColor: colors.gray[50] }}
                contentContainerStyle={{ gap: 10, paddingHorizontal: 10, paddingTop: 10, paddingBottom: 500 }}
            >
                {contentComponents.map((_, index) => (
                    <Option key={index} index={index} onDelete={handleDeleteContent} />
                ))}
                <TextButton
                    label="Add Option"
                    onPress={handleAddContent}
                    leftIcon={<AddIcon color="white" size="18" />}
                />
            </ScrollView>
            <BottomSheet
                style={[
                    {
                        borderColor: colors.gray[100],
                        borderRadius: 15,
                        backgroundColor: "white",
                        borderWidth: 1,
                        elevation: 1,
                        // paddingHorizontal: 5,
                    },
                    // isKeyboardVisible && { display: 'none' },
                ]}
                snapPoints={[150, 260]}
                ref={bottomSheetRef}
                onChange={handleSheetChanges}
                enableOverDrag={false}
            >
                <BottomSheetView style={{ flexDirection: "row", gap: 5, width: "100%" }}>
                    <View></View>
                </BottomSheetView>
            </BottomSheet>
        </GestureHandlerRootView>
    );
};
const styles = StyleSheet.create({
    inputSelect: {
        padding: 10,
        borderWidth: 1,
        borderColor: colors.gray[500],
        borderRadius: 5,
    },

    label: {
        fontSize: 16,
        paddingBottom: 5,
        fontWeight: "bold",
    },
    input: {
        borderWidth: 0.5,
        borderColor: colors.gray[500],
        borderRadius: 5,
        flex: 1,
        paddingBottom: 2,
    },
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        paddingTop: 10,
        gap: 10,
    },
    column: {
        flex: 1,
    },
    iconContainer: {
        height: "100%",
        justifyContent: "center",
        paddingRight: 10,
    },
    inputAndroid: {
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.black,
        borderWidth: 1,
        borderColor: colors.gray[300],

        borderRadius: 5,
        padding: 10,
        // backgroundColor: colors.white,
    },
});

export default OptionsBundlesContent;
