import { useState } from "react";
import { View, TextInput, TouchableOpacity, Text, StyleSheet, ScrollView } from "react-native";
import colors from "@styles/colors";
import { TabsContentCellDeleteBundles } from "@components/icons/createOrderIcons";
import Input from "@components/inputs/textInputs/Input";
import Stars from "@components/inputs/buttons/starsReviews";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import { AddIcon } from "@components/icons/BudgetManagerIcons";
type ContentProps = {
    index: number;
    onDelete: (index: number) => void;
};
const Content: React.FC<ContentProps> = ({ index, onDelete }) => {
    const [formValues, setFormValues] = useState<any>({
        customer: {},
        cart: [],
        status: "pending",
        total: {
            deliveryPrice: 0,
            deliveryCost: 0,
            totalPrice: 0,
        },
        deliveryCompany: "",
        note: "",
    });
    return (
        <View
            style={{
                borderWidth: 0.5,
                borderColor: colors.gray[500],
                borderRadius: 10,
                paddingHorizontal: 10,
                paddingBottom: 10,
            }}
        >
            <View style={{ ...styles.column }}>
                <Text style={styles?.label}>Name</Text>
                <View style={{ flexDirection: "row", justifyContent: "space-between", gap: 10 }}>
                    <Input
                        containerStyle={{ flex: 1 }}
                        placeholder="Name"
                        inputProps={{
                            defaultValue: "",
                        }}
                        onChange={(value) => {
                            setFormValues({ ...formValues, note: value });
                        }}
                        noBottomPadding
                    />
                    <TouchableOpacity
                        style={{
                            backgroundColor: colors.red[100],
                            padding: 12,
                            borderRadius: 10,
                        }}
                        onPress={() => onDelete(index)}
                    >
                        <TabsContentCellDeleteBundles />
                    </TouchableOpacity>
                </View>
                <Text style={{ ...styles?.label, paddingTop: 10 }}>Comment</Text>
                <View style={{ paddingVertical: 10 }}>
                    <TextInput style={styles.largeInput} multiline={true} placeholder="Write your Comment here... " />
                </View>
                <View
                    style={{
                        flexDirection: "row",
                        alignItems: "center",
                        paddingTop: 10,
                        justifyContent: "space-between",
                    }}
                >
                    <Text style={{ ...styles?.label, paddingTop: 5 }}>Select Rating:</Text>
                    <Stars />
                </View>
            </View>
        </View>
    );
};

const Reviews = ({}) => {
    const [contentComponents, setContentComponents] = useState<number[]>([0]);
    const handleAddContent = () => {
        setContentComponents([...contentComponents, contentComponents.length]);
    };
    const handleDeleteContent = (index: number) => {
        const updatedComponents = contentComponents.filter((_, i) => i !== index);
        setContentComponents(updatedComponents);
    };

    return (
        <ScrollView
            style={{ backgroundColor: colors.gray[50] }}
            contentContainerStyle={{ paddingHorizontal: 10, paddingTop: 10, gap: 10, paddingBottom: 500 }}
        >
            {contentComponents.map((_, index) => (
                <Content key={index} index={index} onDelete={handleDeleteContent} />
            ))}
            <TextButton label="Add Review" onPress={handleAddContent} leftIcon={<AddIcon color="white" size="18" />} />
        </ScrollView>
    );
};
const styles = StyleSheet.create({
    label: {
        fontSize: 16,
        paddingBottom: 5,
        fontWeight: "bold",
    },
    input: {
        borderWidth: 0.5,
        borderColor: colors.gray[500],
        borderRadius: 5,
        flex: 1,
        paddingBottom: 2,
    },
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        gap: 10,
        paddingVertical: 10,
    },
    column: {
        flex: 1,
        paddingTop: 10,
        paddingBottom: 10,
    },
    largeInput: {
        borderWidth: 0.5,
        borderColor: colors.gray[500],
        borderRadius: 5,
        padding: 10,
        height: 150,
        textAlignVertical: "top",
    },
});

export default Reviews;
