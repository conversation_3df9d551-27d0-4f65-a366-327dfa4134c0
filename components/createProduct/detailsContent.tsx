// import { styles } from "@components/order/orderDetailsStyles";
import { launchImageLibraryAsync, MediaTypeOptions } from "expo-image-picker";
import { useRef, useState } from "react";
import { View, TouchableOpacity, ScrollView, TextInput, Switch, Text, Image, StyleSheet, Button } from "react-native";
import colors from "@styles/colors";
import Svg, { Path } from "react-native-svg";
import { TabsContentCellDelete } from "@components/icons/createOrderIcons";
import { inputStyles } from "@app/createOrder";
import Input from "@components/inputs/textInputs/Input";
import Animated, { useSharedValue, withTiming, useAnimatedStyle } from "react-native-reanimated";
import Divider from "@components/dividers/Divider";
import { typography } from "@styles/typography";
import { Product } from "../../types/Product";

const DetailsContent = ({}) => {
    const [images, setImages] = useState<string[]>([]);
    const [isTrackStock, setIsTrackStock] = useState(false);
    const [formValues, setFormValues] = useState<Partial<Product>>({});

    const handleDeleteImage = (uri: string) => {
        setImages((prevImages) => prevImages.filter((image) => image !== uri));
    };

    const handleImagePick = async () => {
        let result = await launchImageLibraryAsync({
            mediaTypes: MediaTypeOptions.All,
            allowsEditing: true,
            aspect: [4, 3],
            quality: 1,
        });

        if (!result.canceled && result.assets) {
            setImages((prevImages) => [...prevImages, ...result.assets.map((asset) => asset.uri || "")]);
        }
    };
    const fadeAnim = useSharedValue(1);

    const animatedStyle = useAnimatedStyle(() => {
        return {
            opacity: fadeAnim.value,
        };
    });
    const handleToggleSwitch = (value: boolean) => {
        setIsTrackStock(value);
        fadeAnim.value = withTiming(value ? 1 : 0, { duration: 300 });
    };

    return (
        <ScrollView
            style={{ backgroundColor: colors.gray[50] }}
            contentContainerStyle={{ paddingHorizontal: 10, gap: 20 }}
        >
            <View style={{ ...styles.imageSection }}>
                <TouchableOpacity style={styles.imagePlaceholder} onPress={handleImagePick}>
                    <Text style={styles.plusSign}>+</Text>
                </TouchableOpacity>
            </View>
            <ScrollView horizontal style={styles.imageList} showsHorizontalScrollIndicator={false}>
                {images.map((uri, index) => (
                    <View key={index} style={{ paddingRight: 10 }}>
                        <Image source={{ uri }} style={styles.image} />

                        <TouchableOpacity onPress={() => handleDeleteImage(uri)} style={styles.deleteButton}>
                            <TabsContentCellDelete />
                        </TouchableOpacity>
                    </View>
                ))}
            </ScrollView>
            <Text style={{ ...styles?.label }}>Product Name</Text>
            <Input
                noBottomPadding
                placeholder="Product name"
                inputProps={{
                    defaultValue: "",
                }}
                onChange={(value) => {
                    setFormValues({ ...formValues, name: value });
                }}
            />
            <View style={styles.column}>
                <Text style={styles?.label}>SKU</Text>
                <Input
                    noBottomPadding
                    placeholder="SKU"
                    inputProps={{
                        defaultValue: "",
                    }}
                    onChange={(value) => {
                        setFormValues({ ...formValues, sku: value });
                    }}
                />
            </View>

            {/* <View style={styles.column}>
                <Text style={styles?.label}>Categories</Text>
                <Input
                    placeholder="Categories"
                    inputProps={{
                        defaultValue: "",
                    }}
                    onChange={(value) => {
                        setFormValues({ ...formValues, note: value });
                    }}
                    noBottomPadding
                    style={inputStyles}
                />
            </View> */}
            <Divider color={colors.gray[300]} />
            <Text style={[styles.underlineLabel, typography.fontBold]}>Pricing</Text>
            <View style={styles.column}>
                <Text style={styles?.label}>Price</Text>
                <Input
                    noBottomPadding
                    placeholder="Price"
                    inputProps={{
                        defaultValue: "",
                    }}
                    onChange={(value) => {
                        const numericValue = parseInt(value);
                        setFormValues({ ...formValues, price: numericValue });
                    }}
                    style={inputStyles}
                />
            </View>
            <View style={styles.column}>
                <Text style={styles?.label}>Pre-Discount</Text>
                <Input
                    noBottomPadding
                    placeholder="Pre-Discount"
                    inputProps={{
                        defaultValue: "",
                    }}
                    onChange={(value) => {
                        setFormValues({ ...formValues, comparePrice: parseInt(value) });
                    }}
                />
            </View>
            <View style={styles.column}>
                <Text style={styles?.label}>Cost</Text>
                <Input
                    noBottomPadding
                    placeholder="Cost"
                    inputProps={{
                        defaultValue: "",
                    }}
                    onChange={(value) => {
                        setFormValues({ ...formValues, cost: parseInt(value) });
                    }}
                />
            </View>
            <Divider color={colors.gray[300]} />
            <Text style={[styles.underlineLabel, typography.fontBold]}>Delivery</Text>
            <View style={styles.column}>
                <Text style={styles?.label}>Delivery Price</Text>
                <Input
                    noBottomPadding
                    placeholder="Delivery Price"
                    inputProps={{
                        defaultValue: "",
                    }}
                    onChange={(value) => {
                        setFormValues({ ...formValues, deliveryPrice: parseInt(value) });
                    }}
                />
            </View>
            <View style={styles.column}>
                <Text style={styles?.label}>Delivery Cost</Text>
                <Input
                    noBottomPadding
                    placeholder="Delivery Cost"
                    inputProps={{
                        defaultValue: "",
                    }}
                    onChange={(value) => {
                        setFormValues({ ...formValues, deliveryCost: parseInt(value) });
                    }}
                />
            </View>
            <Divider color={colors.gray[300]} />
            <Text style={[styles.underlineLabel, typography.fontBold]}>Stock</Text>
            <View style={styles.checkboxContainer}>
                <Text style={styles?.label}>Track Stock</Text>

                <Switch
                    value={isTrackStock}
                    onValueChange={handleToggleSwitch}
                    trackColor={{ false: colors.gray[300], true: colors.primary[500] }}
                    thumbColor={isTrackStock ? "white" : "white"}
                />
            </View>
            <Animated.View style={[animatedStyle]}>
                {isTrackStock && (
                    <>
                        <View style={styles.column}>
                            <Text style={styles?.label}>Stock</Text>
                            <Input
                                placeholder="Stock"
                                inputProps={{
                                    defaultValue: "",
                                }}
                                onChange={(value) => {
                                    setFormValues({ ...formValues, stock: parseInt(value) });
                                }}
                            />
                        </View>
                        <View style={styles.column}>
                            <Text style={styles?.label}>Damaged Stock</Text>
                            <Input
                                placeholder="Damage stock"
                                inputProps={{
                                    defaultValue: "",
                                }}
                                onChange={(value) => {
                                    setFormValues({ ...formValues, damagedStock: parseInt(value) });
                                }}
                            />
                        </View>
                    </>
                )}
            </Animated.View>
            <Divider color={colors.gray[300]} />
            <Text style={[styles.underlineLabel, typography.fontBold]}>Description</Text>
            <View style={{ paddingVertical: 10 }}>
                <TextInput
                    style={styles.largeInput}
                    multiline={true}
                    placeholder="Write your description here... "
                    placeholderTextColor={colors.gray[400]}
                    onChangeText={(value) => {
                        setFormValues({ ...formValues, description: value });
                    }}
                />
            </View>
        </ScrollView>
    );
};
const styles = StyleSheet.create({
    imagePlaceholder: {
        width: 100,
        height: 100,
        borderWidth: 2,
        borderColor: colors.primary[600],
        borderStyle: "dashed",
        justifyContent: "center",
        alignItems: "center",
    },
    imageList: {
        flexDirection: "row",
        paddingTop: 10,
    },

    image: {
        width: 100,
        height: 100,
        borderRadius: 8,
    },
    plusSign: {
        fontSize: 40,
        color: colors.primary[600],
    },
    label: {
        fontSize: 14,
        fontWeight: "bold",
        color: colors.gray[600],
        // paddingLeft: 5,
    },
    input: {
        borderWidth: 0.5,
        borderColor: colors.gray[500],
        borderRadius: 5,
        flex: 1,
        paddingBottom: 2,
    },
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        gap: 10,
        paddingVertical: 10,
    },
    column: {
        flex: 1,
    },
    checkboxContainer: {
        flexDirection: "row",
        alignItems: "center",
        // paddingTop: 10,
    },

    imageSection: {
        flexDirection: "row",
        alignItems: "center",
        paddingBottom: 20,
        paddingTop: 10,
        justifyContent: "center",
    },
    deleteButton: {
        position: "absolute",
        top: 0,
        backgroundColor: "rgba(0,0,0,0.5)",
        borderRadius: 50,
        padding: 5,
    },
    underlineLabel: {
        fontSize: 16,
        color: colors.gray[500],
        width: "100%",
    },
    largeInput: {
        borderWidth: 1,
        backgroundColor: colors.gray[50],
        borderRadius: 5,
        borderColor: colors.gray[200],
        padding: 10,
        height: 150,
        textAlignVertical: "top",
    },
    bold: typography.fontBold,
});
export default DetailsContent;
