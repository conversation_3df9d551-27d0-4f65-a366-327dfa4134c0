import { TabsContentCellDeleteBundles } from "@components/icons/createOrderIcons";
import { View, TextInput, TouchableOpacity, Text, StyleSheet, ScrollView } from "react-native";
import colors from "@styles/colors";
import Input from "@components/inputs/textInputs/Input";
import { useState } from "react";
import Svg, { G, Circle, Path } from "react-native-svg";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import { AddIcon } from "@components/icons/BudgetManagerIcons";
type ContentProps = {
    index: number;
    onDelete: (index: number) => void;
};
const Content: React.FC<ContentProps> = ({ index, onDelete }) => {
    const [formValues, setFormValues] = useState<any>({
        customer: {},
        cart: [],
        status: "pending",
        total: {
            deliveryPrice: 0,
            deliveryCost: 0,
            totalPrice: 0,
        },
        deliveryCompany: "",
        note: "",
    });
    return (
        <View
            style={{
                borderWidth: 0.5,
                borderColor: colors.gray[500],
                borderRadius: 10,
                paddingHorizontal: 10,
                paddingBottom: 10,
            }}
        >
            <View style={{ ...styles.column }}>
                <Text style={styles?.label}>Related Product</Text>
                <View
                    style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        gap: 10,
                    }}
                >
                    <Input
                        containerStyle={{ flex: 1 }}
                        placeholder="Related Product"
                        inputProps={{
                            defaultValue: "",
                        }}
                        onChange={(value) => {
                            setFormValues({ ...formValues, note: value });
                        }}
                        noBottomPadding
                        icon={
                            <Svg width="17" height="17" viewBox="0 0 20 20" fill="none">
                                <G stroke={colors.gray[900]} strokeWidth="2">
                                    <Circle cx="8.5" cy="8.5" r="6.5" />
                                    <Path d="M13 13l5 5" />
                                </G>
                            </Svg>
                        }
                    />
                    <TouchableOpacity
                        style={{
                            backgroundColor: colors.red[100],
                            padding: 10,
                            borderRadius: 10,
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                        onPress={() => onDelete(index)}
                    >
                        <TabsContentCellDeleteBundles />
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};

export const RelatedProducts = ({}) => {
    const [contentComponents, setContentComponents] = useState<number[]>([0]);

    const handleAddContent = () => {
        setContentComponents([...contentComponents, contentComponents.length]);
    };
    const handleDeleteContent = (index: number) => {
        const updatedComponents = contentComponents.filter((_, i) => i !== index);
        setContentComponents(updatedComponents);
    };
    return (
        <ScrollView
            style={{ backgroundColor: colors.gray[50] }}
            contentContainerStyle={{ paddingHorizontal: 10, paddingTop: 10, gap: 10, paddingBottom: 500 }}
        >
            {contentComponents.map((_, index) => (
                <Content key={index} index={index} onDelete={handleDeleteContent} />
            ))}
            <TextButton label="Add Product" onPress={handleAddContent} leftIcon={<AddIcon color="white" size="18" />} />
        </ScrollView>
    );
};
const styles = StyleSheet.create({
    label: {
        fontSize: 16,
        paddingBottom: 5,
        fontWeight: "bold",
    },
    input: {
        borderWidth: 0.5,
        borderColor: colors.gray[500],
        borderRadius: 5,
        flex: 1,
        paddingBottom: 2,
    },

    column: {
        flex: 1,
        paddingTop: 10,
        paddingBottom: 10,
    },
});

export default RelatedProducts;
