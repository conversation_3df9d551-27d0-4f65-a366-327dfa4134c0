import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { View, Text, StyleSheet } from "react-native";

function formatTime(seconds: number): string {
    const days = Math.floor(seconds / (24 * 3600));
    const hours = Math.floor((seconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    // const secs = seconds % 60;

    const parts = [];
    if (days > 0) parts.push(`${days} days`);
    if (hours > 0) parts.push(`${hours} hours`);
    if (minutes > 0) parts.push(`${minutes} minutes`);
    // if (secs > 0) parts.push(`${secs} seconds`);

    return parts.join(", ");
}

const BlockedIPCard = ({ IP, blockedAttempts, ttl }) => {
    return (
        <View style={styles.firstView} key={IP}>
            <View style={styles.column}>
                <View>
                    <View style={styles.row}>
                        <Text style={styles.budgetDate}>{"Blocked IP"}</Text>
                        <Text style={styles.budgetName}>{IP}</Text>
                    </View>
                    <View style={styles.row}>
                        <Text style={styles.budgetDate}>{"Blocked Attempts"}</Text>
                        <Text style={styles.budgetName}>{blockedAttempts}</Text>
                    </View>
                    <View style={styles.row}>
                        <Text style={styles.budgetDate}>{"Time before unblock"}</Text>
                        <Text style={styles.budgetDate}>{formatTime(ttl)}</Text>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default BlockedIPCard;

const styles = StyleSheet.create({
    row: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
        height: 30,
    },
    column: {
        flexDirection: "column",
        justifyContent: "space-between",
        alignItems: "flex-start",
    },
    firstView: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderColor: colors.gray[400],
        borderWidth: 1,
        borderRadius: 10,
    },
    budgetName: {
        fontFamily: typography.fontSemibold.fontFamily,
        fontSize: typography.lg.fontSize,
        color: colors.gray[600],
    },
    budgetDate: {
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: typography.sm.fontSize,
        color: colors.gray[600],
    },
    buttonLabel: {
        textTransform: "capitalize",
        fontFamily: typography.fontMedium.fontFamily,
        fontSize: typography.sm.fontSize,
        color: colors.gray[600],
    },
});
