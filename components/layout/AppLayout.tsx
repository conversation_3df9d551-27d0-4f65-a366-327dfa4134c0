import React, { useState, useEffect } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Dimensions, KeyboardAvoidingView, Platform, Vibration, View } from "react-native";
import { StatusBar } from "expo-status-bar";
import * as NavigationBar from "expo-navigation-bar";
import Toast from "react-native-toast-message";

import colors from "@styles/colors";
import { useAuthStore } from "../../store/authStore";
import { useLoaderStore } from "../../store/loaderStore";
import { useStoreStore } from "../../store/storeStore";

import Loading from "../pages/Loading";
import Login from "../pages/login/Login";
import LoaderModal from "../Navigation/LoaderModal";
import MultiStoreSelector from "../MultiStoreSelector";
import NeedsUpdate from "../needsUpdate/main";

import { useSocketEvents } from "../../hooks/useSocketEvents";
import { useDeepLinks } from "../../hooks/useDeepLinks";
import { useVersionCheck } from "../../hooks/useVersionCheck";
import MainAppContent from "./MainAppContent";

interface AppLayoutProps {
    fontLoaded: boolean;
}

const AppLayout: React.FC<AppLayoutProps> = ({ fontLoaded }) => {
    const auth = useAuthStore((state) => state.auth);
    const setAuth = useAuthStore((state) => state.setAuth);
    const checkSession = useAuthStore((state) => state.checkSession);
    const isLoading = useLoaderStore((state) => state.loading);
    const { showStoreSelector, setShowStoreSelector } = useStoreStore();

    const [needsUpdate, setNeedsUpdate] = useState(false);

    // Custom hooks for separated concerns
    useSocketEvents(); // Only handles order and store listeners
    useDeepLinks();
    const { checkForUpdates } = useVersionCheck();

    useEffect(() => {
        console.log("AppLayout: Initializing app...");

        // Check session on app startup
        checkSession();

        // Check for app updates
        checkForUpdates().then((result) => {
            if (result.forceUpdate) {
                setNeedsUpdate(true);
            }
        }).catch((error) => {
            console.error("Error checking for updates:", error);
        });
    }, [checkSession, checkForUpdates]);

    // Configure Android navigation bar
    useEffect(() => {
        if (Platform.OS === "android") {
            NavigationBar.setVisibilityAsync("visible");
            NavigationBar.setBackgroundColorAsync(colors.white);
        }
    }, []);

    const isLoadingState = !fontLoaded || auth === "pending";

    // Debug auth state changes
    useEffect(() => {
        console.log("AppLayout: Auth state changed to:", auth);
    }, [auth]);

    const renderContent = () => {
        if (isLoadingState) {
            return (
                <View
                    style={{
                        height: Platform.OS === "android" ? Dimensions.get("window").height : "100%",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <Loading />
                </View>
            );
        }

        if (needsUpdate) {
            return <NeedsUpdate />;
        }

        if (auth === "auth") {
            return (
                <View
                    style={{ height: Platform.OS === "android" ? Dimensions.get("window").height : "100%" }}
                >
                    <MainAppContent />
                </View>
            );
        }

        return <Login />;
    };

    return (
        <>
            <SafeAreaView
                style={{ backgroundColor: colors.white, height: Dimensions.get("screen").height }}
                edges={{ top: "maximum", bottom: Platform.OS === "android" ? "additive" : "off" }}
            >
                <KeyboardAvoidingView
                    style={[
                        { backgroundColor: colors.white, height: "100%" },
                        auth === "unauth" && { justifyContent: "center" },
                    ]}
                    behavior={Platform.OS === "ios" ? "height" : "padding"}
                >
                    {renderContent()}
                    <Toast bottomOffset={80} position="bottom" onShow={() => Vibration.vibrate(100)} />
                </KeyboardAvoidingView>
                {Platform.OS === "android" && (
                    <StatusBar backgroundColor={auth === "auth" ? colors.gray[50] : colors.white} />
                )}
            </SafeAreaView>

            <MultiStoreSelector
                isVisible={showStoreSelector}
                onClose={() => {
                    setShowStoreSelector(false);
                    if (auth !== "auth") {
                        setAuth("unauth");
                    }
                }}
            />
            <LoaderModal visible={isLoading} />
        </>
    );
};

export default AppLayout;
