import React, { useEffect, useState } from "react";
import { AppState } from "react-native";
import { Stack } from "expo-router";

import colors from "@styles/colors";
import { useAuthStore } from "../../store/authStore";
import { useStoreStore } from "../../store/storeStore";
import Header from "../Navigation/Header";

const MainAppContent: React.FC = () => {
    const { getStore, user, fetchStores } = useStoreStore();
    const { auth } = useAuthStore();
    const [appState, setAppState] = useState(AppState.currentState);

    useEffect(() => {
        if (auth === "auth") {
            getStore().then(() => {
                if (user?.stores && user.stores.length > 0) {
                    fetchStores();
                }
            });
        }
    }, [auth]); //, getStore, user?.stores, fetchStores

    useEffect(() => {
        const subscription = AppState.addEventListener("change", (nextAppState) => {
            if (appState.match(/inactive|background/) && nextAppState === "active") {
                if (auth === "auth") {
                    getStore().then(() => {
                        if (user?.stores && user.stores.length > 0) {
                            fetchStores();
                        }
                    });
                }
            }
            setAppState(nextAppState);
        });

        return () => {
            subscription.remove();
        };
    }, [appState, auth]); //, getStore, user?.stores, fetchStores

    return (
        <Stack
            screenOptions={{
                header: ({ navigation, options, route, back }) => (
                    <Header navigation={navigation} options={options} route={route} back={back} />
                ),
                contentStyle: {
                    backgroundColor: colors.gray[50],
                },
            }}
        >
            <Stack.Screen name="(mainNav)" options={{ headerShown: false }} />
            <Stack.Screen name="(modals)" options={{ headerShown: false }} />
            <Stack.Screen
                name="network-error"
                options={{
                    headerShown: false,
                    animation: "fade",
                    presentation: "transparentModal",
                    contentStyle: {
                        backgroundColor: "rgba(0,0,0,0.25)",
                        flex: 1,
                        width: "100%",
                        height: "100%",
                        margin: 0,
                        padding: 0,
                    },
                }}
            />
        </Stack>
    );
};

export default MainAppContent;
