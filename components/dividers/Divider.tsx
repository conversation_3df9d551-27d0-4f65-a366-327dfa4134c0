import { StyleSheet, Text, View } from "react-native";
import React from "react";
import colors from "@styles/colors";

const Divider = ({ color }: { color?: string }) => {
    return (
        <View
            style={{
                height: 1,
                gap: 10,
                alignItems: "center",
                alignSelf: "stretch",
                backgroundColor: color ? color : colors.gray[200],
            }}
        ></View>
    );
};

export default Divider;

const styles = StyleSheet.create({});
