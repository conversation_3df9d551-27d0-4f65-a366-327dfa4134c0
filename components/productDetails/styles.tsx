import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    scrollView: {
        backgroundColor: colors.gray[50],
    },
    container: {
        backgroundColor: colors.gray[50],
        gap: 10,
    },
    header: {
        backgroundColor: colors.gray[50],
        paddingLeft: 10,
        paddingRight: 0,
    },
    headerText: {
        justifyContent: "space-between",
        paddingHorizontal: 10,
    },
    refText: {
        fontSize: 18,
        fontWeight: "500",
        color: colors.gray[500],
    },
    nameText: {
        fontSize: 16,
        fontWeight: "700",
        color: colors.gray[600],
    },
    carouselImage: {
        width: "100%",
        height: 300,
        borderRadius: 10,
        borderColor: colors.gray[300],
        borderWidth: 0.75,
    },
    imageScrollContainer: {
        paddingHorizontal: 10,
        flexDirection: "row",
        alignItems: "center",
        gap: 10,
    },
    priceCards: {
        flexDirection: "row",
        justifyContent: "space-between",
        paddingVertical: 20,
        gap: 10,
    },
    priceCard: {
        padding: 10,
        backgroundColor: "transparent",
        shadowColor: colors.gray[500],
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        flex: 1,
        paddingHorizontal: 5,
    },
    variantTextContainer: {
        backgroundColor: "tranparent",
        padding: 20,
    },
    variantColorContainer: {
        backgroundColor: colors.white,
        padding: 20,
        paddingVertical: 10,
        shadowColor: colors.gray[500],
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    colorContainer: {
        flexDirection: "row",
        justifyContent: "space-evenly",
        paddingVertical: 10,
    },
    colorBox: {
        width: 50,
        height: 50,
    },
    upsellContainer: {
        backgroundColor: "tranparent",
        padding: 20,
        paddingVertical: 10,
        shadowColor: colors.gray[500],
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    upsellText: {
        fontSize: 16,
        color: colors.gray[500],
    },
    loadingText: {
        fontSize: 18,
        textAlign: "center",
        paddingVertical: 20,
    },
    descriptionContainer: {
        backgroundColor: "white",
        padding: 20,
        borderWidth: 0.3,
        borderRadius: 14,
        borderColor: colors.gray[400],
        height: "100%",
    },
    descriptionText: {
        fontSize: typography.lg.fontSize,
        fontFamily: typography.fontBold.fontFamily,
        paddingBottom: 10,
    },
    dorpShodow: {
        shadowRadius: 2,
    },
    elevation: {
        elevation: 3,
    },
});
