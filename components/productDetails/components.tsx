import React, { useState } from "react";
import { Text, View, Image as Img, TouchableOpacity, useWindowDimensions, ImageSourcePropType } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { ScrollView } from "react-native-gesture-handler";
import RenderHtml, { HTMLSource } from "react-native-render-html";
import { Combination, Variant } from "../../types/Product";
import { isColorHex, isImageLink } from "./utils";
import { useProductStore } from "../../store/productStore";
import { styles } from "./styles";
export type Combinations = (Combination & { keys: string[] })[];

export const OptionSection = ({ variants }: { variants: Variant[] }) => {
    const [selectedColorIndex, setSelectedColorIndex] = useState<number>(0);
    return (
        <View
            style={{
                paddingHorizontal: 15,
                paddingVertical: 15,
                borderRadius: 15,
                backgroundColor: colors.white,
                gap: 5,
                elevation: 2,
                shadowColor: colors.black,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.2,
                shadowRadius: 2,
            }}
        >
            <Text style={[typography.fontBold, typography.lg]}>Options</Text>
            <View style={{ gap: 5 }}>
                {variants.map((variant, index) => {
                    return (
                        <View
                            style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}
                            key={index}
                        >
                            <Text style={[typography.fontMedium, typography.sm, { color: colors.gray[800], flex: 1 }]}>
                                {variant.name}
                            </Text>
                            <View
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "flex-end",
                                }}
                            >
                                <ScrollView
                                    horizontal
                                    style={{
                                        flexGrow: 0,
                                    }}
                                    contentContainerStyle={{
                                        padding: 5,
                                        flexDirection: "row",
                                        gap: 5,
                                        justifyContent: "flex-end",
                                        alignSelf: "stretch",
                                    }}
                                >
                                    {variant.values.map((value, index) => {
                                        if (variant.type === "color")
                                            return (
                                                <ColorTag
                                                    selected={index + 1 === selectedColorIndex}
                                                    setTextVisible={() => {
                                                        if (index + 1 === selectedColorIndex) {
                                                            setSelectedColorIndex(0);
                                                            return;
                                                        }
                                                        setSelectedColorIndex(index + 1);
                                                    }}
                                                    color={value}
                                                    key={index}
                                                />
                                            );
                                        if (variant.type === "image" || isImageLink(value)) {
                                            return <SmallImage source={{ uri: value }} key={index} />;
                                        }
                                        return (
                                            <View
                                                style={{
                                                    backgroundColor: "rgba(115,0,200,0.05)",
                                                    padding: 5,
                                                    paddingHorizontal: 10,
                                                    borderRadius: 5,
                                                }}
                                            >
                                                <Text
                                                    numberOfLines={1}
                                                    style={[
                                                        typography.fontMedium,
                                                        typography.sm,
                                                        {
                                                            color: typography.grey[600],
                                                        },
                                                    ]}
                                                    key={index}
                                                >
                                                    {value}
                                                </Text>
                                            </View>
                                        );
                                    })}
                                </ScrollView>
                            </View>
                        </View>
                    );
                })}
            </View>
        </View>
    );
};

export const ColorTag = ({
    color,
    selected,
    setTextVisible,
}: {
    color: string;
    selected?: boolean;
    setTextVisible?: () => void;
}) => {
    return (
        <TouchableOpacity
            onPress={setTextVisible ? setTextVisible : () => {}}
            style={{ flexDirection: "row", alignItems: "center", gap: 5, minHeight: 20 }}
        >
            <View
                style={{
                    backgroundColor: color,
                    elevation: 1,
                    paddingHorizontal: 5,
                    borderRadius: 15,
                    height: 15,
                    width: 15,
                }}
            ></View>
            {selected && (
                <Text style={[typography.fontMedium, typography.sm, { color: typography.grey[600] }]}>{color}</Text>
            )}
        </TouchableOpacity>
    );
};

export const SmallImage = ({ source }: { source: ImageSourcePropType }) => {
    return (
        <View
            style={{
                height: 30,
                width: 30,
                elevation: 1,
                shadowColor: colors.black,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.2,
                shadowRadius: 2,
                borderWidth: 0.5,
                borderColor: colors.gray[200],
                borderRadius: 5,
                backgroundColor: "white",
            }}
        >
            <Img
                style={{
                    height: 30,
                    width: 30,
                    borderRadius: 5,
                    borderWidth: 0.5,
                    borderColor: colors.gray[200],
                }}
                height={30}
                width={30}
                source={source}
            />
        </View>
    );
};

export const CombinationCard = ({
    combination: { available, comparePrice, deliveryPrice, image, price, quantity, sku, stock, keys },
}: {
    combination: Combination & { keys: string[] };
}) => {
    return (
        <View
            style={{
                borderRadius: 15,
                backgroundColor: colors.white,
                elevation: 2,
                shadowColor: colors.black,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.2,
                shadowRadius: 2,
                paddingVertical: 15,
                width: 200,
                gap: 10,
            }}
        >
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 5,
                    width: 200,
                    justifyContent: "center",
                }}
            >
                <ScrollView
                    horizontal
                    style={{ flexGrow: 0 }}
                    centerContent={true}
                    contentContainerStyle={{
                        flexDirection: "row",
                        gap: 10,
                        paddingVertical: 5,
                        paddingHorizontal: 15,
                    }}
                >
                    {keys.map((key, index) => {
                        if (isImageLink(key)) {
                            return <SmallImage source={{ uri: key }} key={index} />;
                        }
                        return <CombinationTag value={key} key={index} />;
                    })}
                </ScrollView>
            </View>
            <View style={{ paddingHorizontal: 15 }}>
                <CombinationRow label={"Price"} value={price} />
                <CombinationRow label={"Compare Price"} value={comparePrice} />
                <CombinationRow label={"Delivery Price"} value={deliveryPrice} />
                <CombinationRow label={"Bundle Quantity"} value={quantity} />
                <CombinationRow label={"Stock"} value={stock} />
                <CombinationRow label={"Sku"} value={sku !== "" && sku ? sku : "-"} />
                {image !== "" && <CombinationRow label="Image" value={image} />}
            </View>
        </View>
    );
};

export const CombinationRow = ({ label, value }: { label: string; value: string | number }) => {
    const _value = typeof value === "number" ? value.toString() : value;
    return (
        <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
            <Text style={[typography.fontMedium, typography.sm, { color: colors.gray[800] }]}>{label}</Text>
            {isImageLink(_value) ? (
                <SmallImage source={{ uri: _value }} />
            ) : (
                <Text style={[typography.fontMedium, typography.sm, { color: colors.gray[800] }]}>{value}</Text>
            )}
        </View>
    );
};

export const CombinationTag = ({ value }: { value: string }) => {
    const [visible, setVisible] = useState(false);
    return (
        <View
            style={{
                backgroundColor: "rgba(115,0,200,0.05)",
                padding: 5,
                borderRadius: 5,
                flexDirection: "row",
                alignItems: "center",
                paddingHorizontal: 10,
            }}
        >
            {isColorHex(value) ? (
                <ColorTag
                    color={value}
                    selected={visible}
                    setTextVisible={() => {
                        setVisible(!visible);
                    }}
                />
            ) : (
                <Text style={[typography.fontMedium, typography.sm, { color: typography.grey[600] }]}>{value}</Text>
            )}
        </View>
    );
};

export const CombinationsSection = ({ combinations }: { combinations: Combinations }) => {
    return (
        <View>
            <ScrollView
                showsHorizontalScrollIndicator={false}
                horizontal
                contentContainerStyle={{ paddingVertical: 5, gap: 10, paddingHorizontal: 10 }}
            >
                {combinations.map((combination, index) => {
                    return <CombinationCard combination={combination} key={index} />;
                })}
            </ScrollView>
        </View>
    );
};

export const DescriptionSection = () => {
    const { selectedProduct } = useProductStore();
    const { width } = useWindowDimensions();

    const source: HTMLSource = { html: selectedProduct.description ? selectedProduct.description : "" };
    return (
        <View
            style={{
                height: "100%",
                borderWidth: 0.5,
                borderColor: colors.gray[200],
                borderRadius: 15,
                backgroundColor: "white",
                elevation: 3,
                padding: 15,
                paddingBottom: 100,
                overflow: "hidden",
            }}
        >
            <Text style={styles.descriptionText}>Description</Text>
            <RenderHtml source={source} contentWidth={width} />
        </View>
    );
};
