import { Combination } from "../../types/Product";

export function flattenCombination(item: any, currentPath: string[], result: (Combination & { keys: string[] })[]) {
    if (item === undefined) return;
    Object.keys(item).forEach((key) => {
        if (item[key] === undefined) return;
        if (item[key].price !== undefined && item[key].comparePrice !== undefined) {
            result.push({
                keys: [...currentPath, key],
                ...item[key],
            });
        } else {
            flattenCombination(item[key], [...currentPath, key], result);
        }
    });
}

export function isImageLink(url: string): boolean {
    const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|svg|webp|tiff|tif)$/i;
    return imageExtensions.test(url);
}

export const isColorHex = (hex: string) => {
    const hexRegex = /^#?([a-fA-F0-9]{3}|[a-fA-F0-9]{6})$/;
    return hexRegex.test(hex);
};
