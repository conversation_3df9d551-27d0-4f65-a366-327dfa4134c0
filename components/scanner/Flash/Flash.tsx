import React from "react";
import { TouchableOpacity } from "react-native";

import colors from "@styles/colors";
import Svg, { Path } from "react-native-svg";

export type FlashProps = {
    on: boolean;
    onPress: () => void;
};

const Flash = ({ on, onPress, ...props }: FlashProps) => {
    return (
        <TouchableOpacity
            {...props}
            onPress={onPress}
            style={{
                position: "absolute",
                right: 15,
                top: 15,
                backgroundColor: on ? colors.white : colors.primary[500],
                width: 50,
                height: 50,
                padding: 10,
                borderRadius: 50,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
            }}
        >
            <HiOutlineLightBulb color={on ? colors.primary[500] : colors.white} />
        </TouchableOpacity>
    );
};

export default Flash;

const HiOutlineLightBulb = ({ color }: { color: string }) => {
    return (
        <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
            <Path
                d="M13.2 3.6a1.2 1.2 0 10-2.4 0v1.2a1.2 1.2 0 002.4 0V3.6zm5.588 3.308a1.2 1.2 0 00-1.696-1.696l-.849.848a1.2 1.2 0 001.697 1.697l.848-.849zM21.6 12a1.2 1.2 0 01-1.2 1.2h-1.2a1.2 1.2 0 110-2.4h1.2a1.2 1.2 0 011.2 1.2zM6.06 7.757A1.2 1.2 0 107.757 6.06l-.849-.848a1.2 1.2 0 00-1.696 1.696l.848.849zM6 12a1.2 1.2 0 01-1.2 1.2H3.6a1.2 1.2 0 110-2.4h1.2A1.2 1.2 0 016 12zm3.6 7.2V18h4.8v1.2a2.4 2.4 0 11-4.8 0zm4.8-2.4c.018-.408.25-.775.572-1.03a4.8 4.8 0 10-5.944 0c.324.255.554.622.57 1.03h4.803-.001z"
                fill={color}
            />
        </Svg>
    );
};
