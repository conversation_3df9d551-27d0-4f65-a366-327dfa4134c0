import { Vibration } from "react-native";
import api from "@api/api";
import colors from "@styles/colors";
import Toast from "react-native-toast-message";

export type ScanRecord = Record<string, ScanResult>;
type SetState<T> = React.Dispatch<React.SetStateAction<T>>;

export type QrCodeBox = {
    x: number;
    y: number;
    width: number;
    height: number;
};

enum CodeStatus {
    picked = "picked",
    returned = "returned",
    error = "error",
    pending = "pending",
    other = "other",
}

const statusColors = {
    picked: colors.green[200],
    returned: colors.secondary[200],
    other: colors.primary[200],
    error: colors.red[200],
    pending: colors.black[200],
};

type Order = any; // to be defined later

export interface ScanResult {
    status: CodeStatus;
    order?: Order;
    error?: string;
}

export type QrMessage = Order | string | null;

/**
 * Fetches the QR Code from the server
 * @param code the QR Code to fetch
 */

let lastCode = "";
export const getBarCodeStatus = async (
    code: string,
    scanRecord: ScanRecord,
    setScanRecord: SetState<ScanRecord>,
    setQrColor: SetState<string>,
    setQrMessage: SetState<QrMessage>
): Promise<void> => {
    if (!code.match(/^[0-9a-fA-F]{24}$/)) {
        onQrError("Invalid QR Code", code, scanRecord, setScanRecord, setQrColor, setQrMessage);
        return;
    }

    const scanResult = scanRecord[code];

    // console.log("scanResult => ", scanResult);
    if (scanResult) {
        if (lastCode && lastCode !== code) {
            Vibration.vibrate(100);
            setTimeout(() => {
                setScanRecord({});
            }, 2000);
        }
        lastCode = code;
        // is it pending?
        if (scanResult.status === CodeStatus.pending) {
            changeQrPending(setQrColor, setQrMessage);
            return;
        }
        // is it picked?
        if (scanResult.status === CodeStatus.picked && scanResult.order) {
            changeQrSuccess(CodeStatus.picked, scanResult.order, setQrColor, setQrMessage);
            return;
        }
        // is it returned?
        if (scanResult.status === CodeStatus.returned && scanResult.order) {
            changeQrSuccess(CodeStatus.returned, scanResult.order, setQrColor, setQrMessage);
            return;
        }
        // is it other?
        if (scanResult.status === CodeStatus.other && scanResult.order) {
            changeQrSuccess(CodeStatus.other, scanResult.order, setQrColor, setQrMessage);
            return;
        }
        // is it error?
        if (scanResult.status === CodeStatus.error) {
            // in case of unknown error
            if (!scanResult.error) {
                changeQrError("Unknown error", setQrColor, setQrMessage);
                return;
            }
            // in case of known error
            changeQrError(scanResult.error, setQrColor, setQrMessage);
            return;
        }
        // everything else
        changeQrError("Unknown error", setQrColor, setQrMessage);
        return;
    }

    onQrPending(code, scanRecord, setScanRecord, setQrColor, setQrMessage);
    try {
        const data = await api.patch("/order/scan/" + code);

        if (!data.success) {
            onQrError("Not found", code, scanRecord, setScanRecord, setQrColor, setQrMessage);
            return;
        }

        if (data.message === "Updated") {
            Vibration.vibrate(300);
            Toast.show({
                type: "success",
                text1: "Order Successfully Scanned",
                text2: ` Order #${data.data?.reference} is now ${data.data?.status}`,
            });
        } else if (data.message && data.message !== "Updated") {
            Vibration.vibrate(100);
            Toast.show({
                type: "error",
                text1: "Error",
                text2: data.message,
            });
        } else Vibration.vibrate(100);

        onQrSuccess(data.data, code, scanRecord, setScanRecord, setQrColor, setQrMessage);
    } catch (error) {
        console.error(error);
        onQrError("Network error", code, scanRecord, setScanRecord, setQrColor, setQrMessage);
    }
};

const onQrError = (
    message: string,
    code: string,
    scanRecord: ScanRecord,
    setScanRecord: SetState<ScanRecord>,
    setQrColor: SetState<string>,
    setQrMessage: SetState<QrMessage>
) => {
    const newScanRecord = { ...scanRecord };
    newScanRecord[code] = { status: CodeStatus.error, error: message };
    setScanRecord(newScanRecord);
    changeQrError(message, setQrColor, setQrMessage);
};

const changeQrError = (message: string, setQrColor: SetState<string>, setQrMessage: SetState<QrMessage>) => {
    setQrColor(statusColors.error);
    setQrMessage(message);
};

const onQrSuccess = (
    order: Order,
    code: string,
    scanRecord: ScanRecord,
    setScanRecord: SetState<ScanRecord>,
    setQrColor: SetState<string>,
    setQrMessage: SetState<QrMessage>
) => {
    const codeStatus =
        order.status === "picked"
            ? CodeStatus.picked
            : order.status === "returned"
            ? CodeStatus.returned
            : CodeStatus.other;
    const newScanRecord = { ...scanRecord };
    newScanRecord[code] = { status: codeStatus, order };
    setScanRecord(newScanRecord);
    changeQrSuccess(codeStatus, order, setQrColor, setQrMessage);
};

const changeQrSuccess = (
    status: CodeStatus,
    order: Order,
    setQrColor: SetState<string>,
    setQrMessage: SetState<QrMessage>
) => {
    setQrColor(statusColors[status]);
    setQrMessage(order);
};

const onQrPending = (
    code: string,
    scanRecord: ScanRecord,
    setScanRecord: SetState<ScanRecord>,
    setQrColor: SetState<string>,
    setQrMessage: SetState<QrMessage>
) => {
    const newScanRecord = { ...scanRecord };
    newScanRecord[code] = { status: CodeStatus.pending };
    setScanRecord(newScanRecord);
    changeQrPending(setQrColor, setQrMessage);
};

const changeQrPending = (setQrColor: SetState<string>, setQrMessage: SetState<QrMessage>) => {
    setQrColor(statusColors.pending);
    setQrMessage(null);
};
