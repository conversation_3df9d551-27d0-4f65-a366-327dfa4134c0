import { Text, View } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";

interface QrErrorProps {
    errorMessage: string;
}

const QrError = ({ errorMessage }: QrErrorProps) => {
    return (
        <View
            style={{
                height: 30,
                width: "90%",
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: colors.red[300],
                borderRadius: 10,
            }}
        >
            <Text
                style={[
                    {
                        fontSize: 20,
                        color: colors.white,
                        textAlign: "center",
                    },
                    typography.fontMedium,
                    typography.baseText,
                    typography.fontNormal,
                    typography.lineHeight8,
                ]}
            >
                {errorMessage}
            </Text>
        </View>
    );
};

export default QrError;
