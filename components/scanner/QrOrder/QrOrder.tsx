import React from "react";
import { Dimensions, Image, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Tag } from "@components/inputs/chips/Tag/Tag";

type Order = any; // to be defined later

interface QrOrderProps {
    onScrollEnd: () => void;
    onScrollStart: () => void;
    onOrderClick: (order: Order) => void;
    order?: Order;
}

const styles = StyleSheet.create({
    scrollView: {
        position: "absolute",
        bottom: 0,
        left: (Dimensions.get("screen").width * 1) / 20,
        width: (Dimensions.get("screen").width * 9) / 10,
    },
    scrollViewContent: {
        backgroundColor: colors.gray[300],
        borderRadius: 10,
        gap: 10,
    },
    touchableOpacity: {
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        gap: 10,
        padding: 10,
    },
    row: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
    },
    rowFlexStart: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-start",
        alignItems: "center",
    },
    column: {
        display: "flex",
        flexDirection: "column",
        gap: 5,
        width: "100%",
    },
    horizontalBar: {
        width: "100%",
        height: 1,
        backgroundColor: colors.gray[200],
    },
    cartItemBar: {
        width: "70%",
        height: 0.5,
        backgroundColor: colors.gray[200],
        marginVertical: 1,
        alignSelf: "center",
    },
    client: {
        ...typography.baseText,
        ...typography.fontBold,
        ...typography.xs,
        maxWidth: "30%",
    },
});

const OrderItem: React.FC<any> = ({ cartItem }) => {
    return (
        <View style={styles.row}>
            <View style={[styles.column, { maxWidth: "70%" }]}>
                <Text style={[typography.baseText, typography.fontSemibold, typography.xs]}>
                    {cartItem.product.name}
                </Text>
                <View style={[styles.rowFlexStart, { gap: 5 }]}>
                    <Text style={[typography.baseText, typography.fontMedium, { fontSize: 10 }]}>Price:</Text>
                    <View style={[styles.rowFlexStart, { gap: 1 }]}>
                        <Text style={[typography.baseText, typography.fontBold, typography.xs]}>
                            {cartItem.pricePerUnit}
                        </Text>
                        <Text
                            style={[
                                typography.baseText,
                                typography.fontMedium,
                                { fontSize: 8, textTransform: "uppercase" },
                            ]}
                        >
                            TND
                        </Text>
                    </View>
                </View>
                <View style={styles.rowFlexStart}>
                    <Text style={[typography.baseText, typography.fontMedium, { fontSize: 10 }]}>Quantity: </Text>
                    <Text style={[typography.baseText, typography.fontBold, typography.xs]}>{cartItem.quantity}</Text>
                </View>
            </View>
            {cartItem.product?.images?.length > 0 ? (
                <Image
                    source={{ uri: cartItem.product?.images[0].sm }}
                    style={{ width: 50, height: 50, borderRadius: 5 }}
                    resizeMode="contain"
                />
            ) : (
                <View style={{ width: 50, height: 50, backgroundColor: colors.gray[200] }} />
            )}
        </View>
    );
};

const QrOrder: React.FC<QrOrderProps> = ({ onScrollEnd, onScrollStart, onOrderClick, order }) => {
    const hexToRGBA = (hex: string, alpha = 1) => {
        const [r, g, b] = [parseInt(hex.slice(1, 3), 16), parseInt(hex.slice(3, 5), 16), parseInt(hex.slice(5, 7), 16)];
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    };
    return (
        <ScrollView
            style={styles.scrollView}
            contentContainerStyle={[styles.scrollViewContent, { backgroundColor: hexToRGBA(colors.blue[100], 0.6) }]}
            onScrollBeginDrag={onScrollStart}
            onScrollEndDrag={onScrollEnd}
        >
            <TouchableOpacity style={styles.touchableOpacity} onPress={() => order && onOrderClick(order)}>
                <View style={styles.row}>
                    <Text style={styles.client} numberOfLines={3}>
                        {order?.customer?.name}
                    </Text>

                    <Tag delivery={order?.deliveryCompany} />
                    <Tag status={order?.status} />
                </View>
                <View style={styles.horizontalBar} />
                <View style={styles.column}>
                    {order?.cart?.map((cartItem: any, index: number) => (
                        <React.Fragment key={index}>
                            <OrderItem cartItem={cartItem} />
                            {index < order.cart.length - 1 && <View style={styles.cartItemBar} />}
                        </React.Fragment>
                    ))}
                </View>
            </TouchableOpacity>
        </ScrollView>
    );
};

export default QrOrder;
