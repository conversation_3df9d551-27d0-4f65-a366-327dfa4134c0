import { Order } from "@components/orders/types";
import { styles } from "./Styles";
import { Text, View } from "react-native";
import RNPickerSelect from "react-native-picker-select";
import Svg, { Path } from "react-native-svg";
import Input from "@components/inputs/textInputs/Input";
import { cities2, deliveryCostSetter } from "./utils";
import { useStoreStore } from "../../store/storeStore";
import { useEffect, useState } from "react";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import SelectModal from "@components/inputs/select/SelectModal";
import SelectCustom from "@components/Navigation/ModalView/SelectCustom";
import api from "@api/api";

export const EditCustomerDetails = ({
    setUpdatedOrderValues,
    updatedOrderValues,
    order,
}: {
    setUpdatedOrderValues: React.Dispatch<React.SetStateAction<Partial<Order>>>;
    updatedOrderValues: Partial<Order>;
    order: Partial<Order>;
}) => {
    const { store } = useStoreStore();

    // --------------------
    // The inputs array
    // --------------------
    const [inputs, setInputs] = useState<any[]>([
        { name: "name", type: "text", required: true, placeholder: "Name" },
        { name: "email", type: "email", required: false, placeholder: "Email" },
        { name: "phone", type: "number", required: true, placeholder: "Phone" },
        { name: "phone2", type: "number", required: false, placeholder: "Phone 2" },
        { name: "address", type: "text", required: false, placeholder: "Address" },
        {
            name: "city",
            type: "select",
            required: true,
            placeholder: "City",
            options:
                cities2[store?.country?.code]?.map((city: string) => ({
                    value: city,
                    element: <Text>{city}</Text>,
                })) ?? [],
        },
        // Hardcode for Algeria
        ...(store?.country?.code === "DZ"
            ? [
                  {
                      name: "town",
                      type: "select",
                      required: false,
                      placeholder: "Town",
                      options: [],
                  },
                  {
                      name: "stopdesk",
                      type: "select",
                      required: false,
                      placeholder: "Stopdesk",
                      options: [
                          { value: "yes", element: <Text>{"Stopdesk delivery"}</Text> },
                          { value: "no", element: <Text>{"Home delivery"}</Text> },
                      ],
                  },
              ]
            : []),
    ]);

    // --------------------
    // Track selected city
    // --------------------
    const [city, setCity] = useState<string>();

    useEffect(() => {
        // Initialize the city in local state
        setCity(updatedOrderValues.customer.city);
    }, []);

    // --------------------
    // Handle city changes
    // --------------------
    useEffect(() => {
        const presetsIntegration = store?.integrations?.find((integration) => integration.ref === "dz-wilaya-presets");

        if (presetsIntegration && updatedOrderValues?.cart?.length > 0) {
            // Safely extract presets or default to empty object
            const presets =
                presetsIntegration.fields?.presets ??
                ({} as Record<string, Record<string, { delivery: number; stopdesk: number }>>);

            // Safely map cart items to their preset names
            const productPresets = updatedOrderValues.cart.map((cartItem) => {
                const integrationPreset = cartItem?.product?.integrationValues?.["dz-wilaya-presets"];
                return integrationPreset?.preset || "";
            });

            // Map each product's preset to its city-specific data
            const cartPresetsValues = productPresets.map((presetName) => {
                return presets[presetName]?.[city];
            });

            // Find the max delivery among all items (checking for undefined)
            const maxDelivery = cartPresetsValues.reduce((prev, current) => {
                if (!prev?.delivery) return current;
                if (!current?.delivery) return prev;
                return prev.delivery > current.delivery ? prev : current;
            }, undefined as { delivery: number; stopdesk: number } | undefined);

            setUpdatedOrderValues((prev) => ({
                ...prev,
                total: {
                    ...prev.total,
                    deliveryPrice: maxDelivery?.delivery ? maxDelivery.delivery : prev.total.deliveryPrice,
                },
                customer: {
                    ...prev.customer,
                    city,
                },
            }));
        } else {
            // If there's no presets or cart is empty, just set the city
            setUpdatedOrderValues((prev) => ({
                ...prev,
                customer: {
                    ...prev.customer,
                    city,
                },
            }));
        }
    }, [city]);

    // ---------------------------------
    // UNCOMMENTED: Towns fetching logic
    // ---------------------------------
    useEffect(() => {
        // If no city or no updatedOrderValues, do nothing
        if (!updatedOrderValues.customer?.city) return;

        // Only fetch if country code is DZ and city is set
        if (updatedOrderValues.customer.city && store?.country?.code === "DZ") {
            api.get(`/data/towns?city=${updatedOrderValues.customer.city}`)
                .then((res) => {
                    // Update the "town" input's options
                    setInputs((prevInputs) =>
                        prevInputs.map((input) => {
                            if (input.name === "town") {
                                return {
                                    ...input,
                                    options: res.data.map((town: string) => ({
                                        value: town,
                                        element: <Text>{town}</Text>,
                                    })),
                                };
                            }
                            return input;
                        })
                    );

                    // If the current order’s town is not in the new list, reset it
                    if (!res.data.includes(updatedOrderValues.customer.town)) {
                        setUpdatedOrderValues((prev) => ({
                            ...prev,
                            customer: {
                                ...prev.customer,
                                town: "",
                            },
                        }));
                    }
                })
                .catch((err) => {
                    // If fetching towns fails, clear the town options
                    setInputs((prevInputs) =>
                        prevInputs.map((input) => {
                            if (input.name === "town") {
                                return {
                                    ...input,
                                    options: [],
                                };
                            }
                            return input;
                        })
                    );
                    console.log("Error fetching towns:", err?.response?.data?.message);
                });
        }
    }, [updatedOrderValues.customer?.city]);

    // // Just logging the entire customer object for debugging
    // useEffect(() => {
    //     console.log("custome =>",{ customer: updatedOrderValues.customer });
    // }, [updatedOrderValues]);

    return (
        <View style={styles.orderContainer}>
            <Text style={styles.sectionTitle}>Customer Details</Text>

            {/* Name */}
            <View>
                <Text style={styles?.label}>Name</Text>
                <View style={styles.element}>
                    <Input
                        placeholder="Name"
                        inputProps={{
                            defaultValue: updatedOrderValues.customer.name,
                        }}
                        onChange={(value) => {
                            setUpdatedOrderValues({
                                ...updatedOrderValues,
                                customer: { ...updatedOrderValues.customer, name: value },
                            });
                        }}
                        isEditable={!order.barcode}
                    />
                </View>

                {/* Phone */}
                <Text style={styles?.label}>Phone</Text>
                <View style={styles.element}>
                    <Input
                        inputProps={{
                            defaultValue: updatedOrderValues.customer.phone,
                        }}
                        placeholder={"Phone"}
                        onChange={(value) => {
                            setUpdatedOrderValues({
                                ...updatedOrderValues,
                                customer: { ...updatedOrderValues.customer, phone: value },
                            });
                        }}
                        isEditable={!order.barcode}
                    />
                </View>

                {/* Address */}
                <Text style={styles?.label}>Address</Text>
                <View style={styles.element}>
                    <Input
                        inputProps={{
                            defaultValue: updatedOrderValues.customer.address,
                        }}
                        placeholder={"Address"}
                        onChange={(value) => {
                            setUpdatedOrderValues({
                                ...updatedOrderValues,
                                customer: { ...updatedOrderValues.customer, address: value },
                            });
                        }}
                        isEditable={!order.barcode}
                    />
                </View>

                {/* City */}
                <Text style={styles?.label}>City</Text>
                <View style={[styles.element, { paddingBottom: 20 }]}>
                    <SelectCustom
                        disabled={!!order.barcode}
                        placeholder="Select City"
                        title="Select City"
                        action={(selectedCity) => {
                            setCity(() => selectedCity);
                        }}
                        value={city}
                        options={
                            cities2[store?.country?.code]?.map((ct: string) => ({
                                value: ct,
                                label: ct,
                            })) ?? []
                        }
                    />
                </View>

                {/* Town (Visible only if Algeria) */}
                {store?.country?.code === "DZ" && (
                    <>
                        <Text style={styles?.label}>Town</Text>
                        <View style={[styles.element, { paddingBottom: 20 }]}>
                            <SelectCustom
                                disabled={!!order.barcode}
                                placeholder="Select Town"
                                title="Select Town"
                                action={(selectedTown) => {
                                    setUpdatedOrderValues((prev) => ({
                                        ...prev,
                                        customer: {
                                            ...prev.customer,
                                            town: selectedTown,
                                        },
                                    }));
                                }}
                                value={updatedOrderValues.customer.town}
                                options={
                                    inputs
                                        .find((input) => input.name === "town")
                                        ?.options?.map((option: any) => ({
                                            label: option.value,
                                            value: option.value,
                                        })) ?? []
                                }
                            />
                        </View>

                        <Text style={styles?.label}>Stopdesk</Text>
                        <View style={[styles.element, { paddingBottom: 20 }]}>
                            <SelectCustom
                                disabled={!!order.barcode}
                                placeholder="Select Stopdesk Delivery"
                                title="Stopdesk Delivery"
                                action={(selectedStopdesk) => {
                                    setUpdatedOrderValues((prev) => ({
                                        ...prev,
                                        customer: {
                                            ...prev.customer,
                                            stopdesk: selectedStopdesk,
                                        },
                                    }));
                                }}
                                value={updatedOrderValues.customer.stopdesk}
                                options={[
                                    { label: "Stopdesk delivery", value: "yes" },
                                    { label: "Home delivery", value: "no" },
                                ]}
                            />
                        </View>
                    </>
                )}

                {/* Email */}
                <Text style={styles?.label}>Email</Text>
                <View style={styles.element}>
                    <Input
                        inputProps={{
                            defaultValue: updatedOrderValues.customer.email,
                        }}
                        onChange={(value) => {
                            setUpdatedOrderValues({
                                ...updatedOrderValues,
                                customer: { ...updatedOrderValues.customer, email: value },
                            });
                        }}
                        placeholder={"Email"}
                        isEditable={!order.barcode}
                    />
                </View>

                {/* Customer Note */}
                <Text style={styles?.label}>Customer Note</Text>
                <View style={styles.element}>
                    <Input
                        isEditable={!order.barcode}
                        inputProps={{
                            defaultValue: updatedOrderValues.customer.note,
                            multiline: true,
                        }}
                        onChange={(value) => {
                            setUpdatedOrderValues({
                                ...updatedOrderValues,
                                customer: { ...updatedOrderValues.customer, note: value },
                            });
                        }}
                        placeholder="Customer Note"
                        noBottomPadding
                        labelStyle={[typography.fontSemibold, typography.xs, { color: colors.gray[800] }]}
                        placeholderStyle={{ fontFamily: typography.fontSemibold.fontFamily }}
                        containerStyle={{ padding: 0 }}
                        style={{
                            flexDirection: "row",
                            alignItems: "center",
                            flexShrink: 1,
                            paddingVertical: 10,
                            borderWidth: 1,
                            borderColor: "rgba(203, 213, 224, 1)",
                            borderRadius: 5,
                        }}
                    />
                </View>
            </View>
        </View>
    );
};
