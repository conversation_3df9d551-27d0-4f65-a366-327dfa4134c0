import { <PERSON>t, CartItem, Order, ProductsList } from "@components/orders/types";
import { styles } from "./Styles";
import { Image, Text, TouchableOpacity, View, ViewStyle } from "react-native";
import Input from "@components/inputs/textInputs/Input";
import Divider from "@components/dividers/Divider";
import Toast from "react-native-toast-message";
import colors from "@styles/colors";
import { useEffect, useState } from "react";
import { TabsContentCellDelete, EditIcon } from "@components/icons/BudgetManagerIcons";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import { Modal } from "react-native";
import { typography } from "@styles/typography";
import { useProductStore } from "../../store/productStore";
import { isColorHex, isImageLink } from "@components/productDetails/utils";
import { SmallImage, CombinationTag } from "@components/productDetails/components";
import { Product } from "../../types/Product";
import { Dropdown } from "react-native-element-dropdown";
import { CloseIcon } from "@components/icons/HeaderIcons";
import { GestureHandlerRootView, ScrollView, FlatList } from "react-native-gesture-handler";
import AddProductModal from "@components/Navigation/ModalView/AddProductModal";
import { Padding } from "@components/Navigation/ModalView/ModalBase";
import EditProductModal from "@components/Navigation/ModalView/EditProductModal";
import { Store } from "../../types/Store";
import { useOrderStore } from "../../store/orders";
import SelectDeliveryCompanyInput from "@components/Navigation/ModalView/SelectDeliveryCompanyInput";
import React from "react";
import { deliveryCostSetter, deliveryPriceSetter } from "./utils";

export const EditDeliveryDetails = ({
    setUpdatedOrderValues,
    updatedOrderValues,
    order,
    products,
    store,
}: {
    setUpdatedOrderValues: React.Dispatch<React.SetStateAction<Partial<Order>>>;
    updatedOrderValues: Partial<Order>;
    order: Partial<Order>;
    products: ProductsList;
    store: Store;
}) => {
    const [addModalVisible, setAddModalVisible] = useState(false);

    useEffect(() => {
        // Set the delivery cost based on delivery company
        setUpdatedOrderValues(deliveryCostSetter(updatedOrderValues, store.integrations));
        // console.log("delivery cost : ",updatedOrderValues.total.deliveryCost );
        // console.log("updatedOrderValues",updatedOrderValues);
    }, [
        updatedOrderValues.deliveryCompany,
        store.integrations,
        updatedOrderValues.customer?.city,
        updatedOrderValues.customer?.stopdesk,
    ]);

    useEffect(() => {
        if (order) {
            setUpdatedOrderValues({
                ...order,
                total: {
                    ...order.total,
                    deliveryPrice: order.total ? order.total.deliveryPrice || 0 : 0,
                    deliveryCost: order.total ? order.total.deliveryCost || 0 : 0,
                    totalPrice: order.total ? order.total.totalPrice || 0 : 0,
                },
            });
        }
    }, [order, products]);

    useEffect(() => {
        if (order && JSON.stringify(order) === JSON.stringify(updatedOrderValues)) return;
        setUpdatedOrderValues(deliveryPriceSetter(store.integrations));
    }, [updatedOrderValues.cart, store.integrations]);

    useEffect(() => {
        if (order && JSON.stringify(order) === JSON.stringify(updatedOrderValues)) return;
        if (store?.country?.code !== "DZ") return;
        setUpdatedOrderValues(deliveryPriceSetter(store.integrations));
    }, [updatedOrderValues.customer?.city, updatedOrderValues.customer?.stopdesk]);

    return (
        // <GestureHandlerRootView>
        <View style={[styles.orderContainer]}>
            <Text style={styles.sectionTitle}>Delivery Details</Text>
            <View>
                <AddProductModal
                    visible={addModalVisible}
                    setVisible={setAddModalVisible}
                    action={(product) => {
                        if (products.length === 0) return;
                        if (product) {
                            const _variant = product.variants?.map((variant) => ({
                                type: variant.type,
                                name: variant.name,
                                value: variant.values[0],
                            }));
                            setUpdatedOrderValues({
                                ...updatedOrderValues,
                                cart: [
                                    {
                                        product: product,
                                        quantity: 1,
                                        selectedVariants: [..._variant],
                                        pricePerUnit: product.price,
                                    },
                                    ...updatedOrderValues.cart,
                                ],
                                total: {
                                    ...updatedOrderValues.total,
                                    totalPrice: updatedOrderValues.total.totalPrice + product.price,
                                },
                            });
                        }
                    }}
                />
                {!order.barcode && (
                    <TextButton
                        variant="outlined"
                        label="Add Product"
                        onPress={() => {
                            setAddModalVisible(true);
                        }}
                    />
                )}
                <OrderProducts
                    setUpdatedOrderValues={setUpdatedOrderValues}
                    updatedOrderValues={updatedOrderValues}
                    store={store}
                    cart={updatedOrderValues.cart}
                    onDelete={(productID) => {
                        if (order.barcode) {
                            Toast.show({
                                type: "error",
                                text1: "Cannot change cart for uploaded order",
                            });
                            return;
                        }
                        let foundProduct = updatedOrderValues.cart.find((product) => product.product._id === productID);
                        if (foundProduct.quantity > 1) {
                            setUpdatedOrderValues({
                                ...updatedOrderValues,
                                cart: updatedOrderValues.cart.map((product) =>
                                    product.product._id === productID
                                        ? {
                                              ...product,
                                              quantity: product.quantity - 1,
                                          }
                                        : product
                                ),
                                total: {
                                    ...updatedOrderValues.total,
                                    totalPrice: updatedOrderValues.total.totalPrice - foundProduct.pricePerUnit,
                                },
                            });
                            return;
                        }
                        setUpdatedOrderValues({
                            ...updatedOrderValues,
                            cart: updatedOrderValues.cart.filter((product) => product.product._id !== productID),
                            total: {
                                ...updatedOrderValues.total,
                                totalPrice:
                                    updatedOrderValues.total.totalPrice -
                                    foundProduct.pricePerUnit * foundProduct.quantity,
                            },
                        });
                    }}
                />
            </View>

            <Divider color={colors.gray[200]} />
            <View style={styles.element}>
                <Text style={styles?.label}>Delivery Cost</Text>

                <Input
                    inputProps={{
                        defaultValue: updatedOrderValues.total.deliveryCost?.toString(),
                        selectTextOnFocus: true,
                        keyboardType: "decimal-pad",
                    }}
                    isEditable={!order.barcode}
                    placeholder="Delivery Cost"
                    onChange={(value) => {
                        if (value != "") {
                            setUpdatedOrderValues({
                                ...updatedOrderValues,
                                total: {
                                    ...updatedOrderValues.total,
                                    deliveryCost: Number.parseFloat(value),
                                },
                            });
                        }
                    }}
                />
                <Text style={styles?.label}>Delivery Price</Text>
                <Input
                    placeholder={
                        "Delivery Price " + order.total?.deliveryPrice?.toString() + " " + store.currency?.code
                    }
                    isEditable={!order.barcode}
                    inputProps={{
                        value: updatedOrderValues.total.deliveryPrice?.toString(),
                        selectTextOnFocus: true,
                        keyboardType: "decimal-pad",
                    }}
                    onChange={(value) => {
                        if (value != "") {
                            setUpdatedOrderValues({
                                ...updatedOrderValues,
                                total: {
                                    ...updatedOrderValues.total,
                                    deliveryPrice: Number.parseFloat(value),
                                },
                            });
                        }
                    }}
                />
            </View>
            <Divider color={colors.gray[200]} />
            <TotalPriceCard store={store} updatedOrderValues={updatedOrderValues} />
        </View>
        // </GestureHandlerRootView>
    );
};

const OrderProducts = ({
    cart,
    onDelete,
    store,
    setUpdatedOrderValues,
    updatedOrderValues,
}: {
    updatedOrderValues: Partial<Order>;
    cart: Cart;
    onDelete: (productID: string) => void;
    store: Store;
    setUpdatedOrderValues: React.Dispatch<React.SetStateAction<Partial<Order>>>;
}) => {
    if (cart.length > 0) {
        return (
            <FlatList
                horizontal
                data={cart}
                contentContainerStyle={{ gap: 10, paddingVertical: 10 }}
                renderItem={({ item: cartItem, index }) => (
                    <ProductCartCard
                        updatedOrderValues={updatedOrderValues}
                        setUpdatedOrderValues={setUpdatedOrderValues}
                        key={index}
                        cartItem={cartItem}
                        index={index}
                        onDelete={onDelete}
                        store={store}
                    />
                )}
            />
        );
    } else {
        return <Text style={{ textAlign: "center", paddingVertical: 20 }}>No products in the cart</Text>;
    }
};

const ProductCartCard = ({
    cartItem,
    store,
    onDelete,
    index,
    setUpdatedOrderValues,
    updatedOrderValues,
}: {
    setUpdatedOrderValues: React.Dispatch<React.SetStateAction<Partial<Order>>>;
    updatedOrderValues: Partial<Order>;

    cartItem: CartItem;
    store: Store;
    onDelete: (productID: string) => void;
    index: number;
}) => {
    const [editModalVisible, setEditModalVisible] = useState(false);
    const { order } = useOrderStore();

    return (
        <View style={styles.productContainer}>
            <EditProductModal
                setUpdatedOrderValues={setUpdatedOrderValues}
                updatedOrderValues={updatedOrderValues}
                cartItem={cartItem}
                visible={!order.barcode && editModalVisible}
                setVisible={setEditModalVisible}
                action={() => {}}
                index={index}
            />
            <Image
                source={{ uri: cartItem.product?.images[0]?.md ?? cartItem.product?.images[0]?.sm }}
                style={styles.productImage}
            />
            <View style={styles.productInfo}>
                <Text style={styles.productName}>{cartItem.product?.name}</Text>
                <View style={{ flexDirection: "row", alignSelf: "stretch", justifyContent: "space-between" }}>
                    <Text style={styles.cartItemLabel}>Qty.</Text>
                    <Text style={styles.cartItemLabel}>{cartItem.quantity}</Text>
                </View>
                <View style={{ flexDirection: "row", alignSelf: "stretch", justifyContent: "space-between" }}>
                    <Text style={styles.cartItemLabel}>Price</Text>
                    <Text style={styles.cartItemLabel}>
                        {cartItem.pricePerUnit.toFixed(2)}
                        <Text style={{ fontSize: 12 }}>{store.currency?.code}</Text>
                    </Text>
                </View>
                <ScrollView
                    showsHorizontalScrollIndicator={false}
                    horizontal
                    contentContainerStyle={{ paddingVertical: 5, gap: 10, paddingHorizontal: 10 }}
                >
                    {cartItem.selectedVariants &&
                        cartItem.selectedVariants.map((variant, index) =>
                            isImageLink(variant.value) ? (
                                <SmallImage source={{ uri: variant.value }} key={index} />
                            ) : (
                                <CombinationTag key={index} value={variant.value} />
                            )
                        )}
                </ScrollView>
            </View>
            {!order.barcode && (
                <>
                    <TouchableOpacity style={styles.deleteButton} onPress={() => onDelete(cartItem.product._id)}>
                        <TabsContentCellDelete />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={styles.editButton}
                        onPress={() => {
                            setEditModalVisible(true);
                        }}
                    >
                        <EditIcon size={20} />
                    </TouchableOpacity>
                </>
            )}
        </View>
    );
};

const TotalPriceCard = ({ updatedOrderValues, store }: { updatedOrderValues: Partial<Order>; store: Store }) => {
    if (updatedOrderValues.cart.length === 0)
        return (
            <View style={styles.totalPriceCard}>
                <Text style={styles.totalPriceTextLeft}>No Products In Order</Text>
            </View>
        );
    let totalPrice = updatedOrderValues.cart.reduce((acc, product) => acc + product.pricePerUnit * product.quantity, 0);
    totalPrice += updatedOrderValues.total.deliveryPrice;
    return (
        <View style={styles.totalPriceCard}>
            <Text style={styles.totalPriceTextLeft}>Total Price:</Text>
            <Text style={styles.totalPriceTextRight}>
                {totalPrice} {store.currency?.code}
            </Text>
        </View>
    );
};
