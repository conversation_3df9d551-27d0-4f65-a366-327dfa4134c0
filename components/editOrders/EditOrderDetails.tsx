import { Order } from "@components/orders/types";
import { styles } from "./Styles";
import { Text, TouchableOpacity, View } from "react-native";

import Input from "@components/inputs/textInputs/Input";
import SelectStatusInput from "@components/Navigation/ModalView/SelectStatusInput";
import SelectDeliveryCompanyInput from "@components/Navigation/ModalView/SelectDeliveryCompanyInput";
import { typography } from "@styles/typography";
import colors from "@styles/colors";
import SelectCustom from "@components/Navigation/ModalView/SelectCustom";
import React from "react";

export const EditOrderDetails = ({
    setUpdatedOrderValues,
    updatedOrderValues,
    order,
    integratedCompanies,
    pageType,
}: {
    setUpdatedOrderValues: React.Dispatch<React.SetStateAction<Partial<Order>>>;
    updatedOrderValues: Partial<Order>;
    pageType: "edit" | "create";
    order: Partial<Order>;
    integratedCompanies: {
        value: string;
        label: string;
    }[];
}) => {
    return (
        <View style={styles.orderContainer}>
            <Text style={styles.sectionTitle}>Order Details</Text>
            <View style={{ flexDirection: "row", alignItems: "center", gap: 10 }}>
                <View style={[styles.element, { flex: 1 }]}>
                    <Text style={styles?.label}>Status</Text>
                    <SelectStatusInput
                        disabled={!!order.barcode}
                        status={updatedOrderValues.status}
                        pageType={pageType}
                        action={(value) => {
                            updatedOrderValues.status;
                            setUpdatedOrderValues({ ...updatedOrderValues, status: value });
                        }}
                    />
                </View>
                {updatedOrderValues.status === "rejected" && (
                    <View style={[styles.element, { flex: 1 }]}>
                        <Text style={styles.label}>Rejection Reasons</Text>
                        <SelectCustom
                            value={updatedOrderValues.customer.rejectionReason}
                            action={(value) => {
                                setUpdatedOrderValues({
                                    ...updatedOrderValues,
                                    customer: { ...updatedOrderValues.customer, rejectionReason: value },
                                });
                            }}
                            options={[
                                {
                                    value: "busy",
                                    label: "Not Available 📞",
                                },
                                {
                                    value: "expensive",
                                    label: "Expensive 💰",
                                },
                                {
                                    value: "didntClick",
                                    label: "Didn't Click buy 👆",
                                },
                                {
                                    value: "betterPrice",
                                    label: "Better Price 🫰",
                                },
                                {
                                    value: "expensiveDelivery",
                                    label: "Expensive Delivery 🚚",
                                },
                                {
                                    value: "other",
                                    label: "Other 🤔",
                                },
                            ]}
                        />
                    </View>
                )}
                {updatedOrderValues.status === "attempt" && (
                    <View style={[styles.element, { flex: 1 }]}>
                        <Text style={styles.label}>Attemp Number</Text>
                        <View
                            style={[
                                {
                                    alignSelf: "stretch",
                                    borderWidth: 1,
                                    borderColor: colors.gray[300],
                                    borderRadius: 5,
                                    minHeight: 50,
                                    flexDirection: "row",
                                    alignItems: "stretch",
                                    paddingVertical: 10,
                                },
                            ]}
                        >
                            <TouchableOpacity
                                onPress={() => {
                                    setUpdatedOrderValues({
                                        ...updatedOrderValues,
                                        attempt: updatedOrderValues.attempt ? updatedOrderValues.attempt + 1 : 2,
                                    });
                                }}
                                style={{
                                    flex: 1,
                                    paddingHorizontal: 15,
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                <Text style={[typography.fontMedium, typography.sm, { color: colors.gray[600] }]}>
                                    +
                                </Text>
                            </TouchableOpacity>
                            <View
                                style={{
                                    borderRightWidth: 1,
                                    borderLeftWidth: 1,
                                    borderColor: colors.gray[300],
                                    justifyContent: "center",
                                    flex: 4,
                                }}
                            >
                                <Text
                                    style={[
                                        typography.fontMedium,
                                        typography.sm,
                                        { color: colors.gray[800], alignSelf: "center" },
                                    ]}
                                >
                                    {updatedOrderValues.attempt ? updatedOrderValues.attempt : 1}
                                </Text>
                            </View>
                            <TouchableOpacity
                                onPress={
                                    updatedOrderValues.attempt > 1
                                        ? () => {
                                              setUpdatedOrderValues({
                                                  ...updatedOrderValues,
                                                  attempt: updatedOrderValues.attempt
                                                      ? updatedOrderValues.attempt - 1
                                                      : 1,
                                              });
                                          }
                                        : undefined
                                }
                                style={{
                                    flex: 1,
                                    paddingHorizontal: 15,
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                <Text
                                    style={[
                                        typography.fontMedium,
                                        typography.sm,
                                        { color: colors.gray[updatedOrderValues.attempt <= 1 ? 400 : 600] },
                                    ]}
                                >
                                    -
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                )}
            </View>

            <View style={styles.element}>
                <Text style={styles?.label}>Private Note</Text>

                <Input
                    isEditable={!order?.barcode}
                    inputProps={{
                        defaultValue: updatedOrderValues.note,
                        multiline: true,
                    }}
                    onChange={(value) => {
                        setUpdatedOrderValues({ ...updatedOrderValues, note: value });
                    }}
                    placeholder="Private Note"
                    noBottomPadding
                    labelStyle={[typography.fontSemibold, typography.xs, { color: colors.gray[800] }]}
                    placeholderStyle={{ fontFamily: typography.fontSemibold.fontFamily }}
                    containerStyle={{ padding: 0 }}
                    style={{
                        flexDirection: "row",
                        alignItems: "center",
                        flexShrink: 1,
                        paddingVertical: 10,
                        borderWidth: 1,
                        borderColor: "rgba(203, 213, 224, 1)",
                        borderRadius: 5,
                    }}
                />
            </View>

            <View style={styles.element}>
                <Text style={styles?.label}>Delivery Company</Text>
                {updatedOrderValues.status !== "rejected" && updatedOrderValues.status !== "attempt" && (
                    <SelectDeliveryCompanyInput
                        disabled={!!order.barcode}
                        deliveryCompany={updatedOrderValues.deliveryCompany}
                        action={(value) => {
                            setUpdatedOrderValues({ ...updatedOrderValues, deliveryCompany: value });
                        }}
                    />
                )}
            </View>
        </View>
    );
};
