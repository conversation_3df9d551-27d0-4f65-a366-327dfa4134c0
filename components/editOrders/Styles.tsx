import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    container: {
        paddingVertical: 20,
        paddingHorizontal: 10,
        gap: 20,
        backgroundColor: colors.gray[100],
    },
    orderContainer: {
        backgroundColor: colors.white,
        borderRadius: 13,
        padding: 15,
        gap: 10,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 5,
        elevation: 3,
        paddingVertical: 20,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: colors.primary[500],
        textAlign: "center",
    },
    label: {
        fontSize: 14,
        fontWeight: "bold",
        color: colors.gray[800],
    },
    element: {
        width: "100%",
        backgroundColor: colors.white,
        gap: 5,
        borderRadius: 5,
        paddingVertical: 5,
    },
    iconContainer: {
        height: "100%",
        justifyContent: "center",
        paddingRight: 10,
    },
    inputAndroid: {
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.black,
        borderWidth: 0.5,
        borderColor: colors.gray[400],
        borderRadius: 5,
        padding: 10,

        height: 50,
    },
    actionsContainer: {
        flexDirection: "column",
        justifyContent: "space-between",
    },
    actionButton: {
        flex: 1,
    },
    productContainer: {
        backgroundColor: colors.white,

        alignItems: "center",
        borderWidth: 1,
        borderColor: colors.gray[300],
        borderRadius: 10,
        overflow: "hidden",
        width: 200,
    },
    productImage: {
        width: 200,
        height: 180,
    },
    productInfo: {
        paddingVertical: 5,
        paddingHorizontal: 10,
        justifyContent: "space-between",
        alignItems: "stretch",
        alignSelf: "stretch",
        gap: 5,
    },
    productName: {
        fontSize: 14,
        fontWeight: "bold",
        fontFamily: typography.fontBold.fontFamily,
        color: colors.black,
    },
    productDetails: {
        justifyContent: "space-between",
        alignSelf: "stretch",
        fontSize: 12,
        fontFamily: typography.fontNormal.fontFamily,
        color: colors.gray[600],
    },
    deleteButton: {
        opacity: 0.8,
        borderRadius: 100,
        backgroundColor: "rgba(230,230,230,0.5)",
        position: "absolute",
        top: 5,
        right: 5,
        padding: 5,
    },
    editButton: {
        opacity: 0.8,
        borderRadius: 100,
        backgroundColor: "rgba(230,230,230,0.5)",

        position: "absolute",
        top: 5,
        left: 5,
        padding: 5,
    },
    totalPriceCard: {
        backgroundColor: colors.white,
        padding: 10,
        borderRadius: 5,
        flex: 1,
        flexDirection: "row",
        justifyContent: "space-between",
    },
    totalPriceTextLeft: {
        fontSize: 18,
        fontWeight: "bold",
        color: colors.black,
        alignItems: "flex-start",
    },
    totalPriceTextRight: {
        fontSize: 18,
        fontWeight: "bold",
        color: colors.black,
        alignItems: "flex-end",
    },

    cartItemLabel: {
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.gray[600],
    },

    dropdown: {
        height: 50,
        borderColor: colors.gray[400],
        borderWidth: 0.5,
        borderRadius: 8,
        paddingHorizontal: 8,
    },
    icon: {
        marginRight: 5,
    },

    placeholderStyle: {
        fontSize: 16,
    },
    selectedTextStyle: {
        fontSize: 16,
        paddingLeft: 10,
        overflow: "hidden",
        flexWrap: "nowrap",
    },
    iconStyle: {
        width: 20,
        height: 20,
    },
    inputSearchStyle: {
        height: 40,
        fontSize: 16,
    },
});
