import { Order } from "@components/orders/types";
import { Store } from "../../types/Store";
import Toast from "react-native-toast-message";
import { companies } from "../../types/Companies";

export const statusItems = [
    { value: "attempt", label: "Attempt" },
    { value: "pending", label: "Pending" },
    { value: "confirmed", label: "Confirmed" },
    { value: "uploaded", label: "Uploaded" },
    { value: "exchange", label: "Exchange" },
    { value: "rejected", label: "Rejected" },
    { value: "picked", label: "Picked" },
    { value: "in transit", label: "In Transit" },
    { value: "in transit2", label: "In Transit 2" },
    { value: "rescheduled", label: "Rescheduled" },
    { value: "cancelled", label: "Cancelled" },
    { value: "delivered", label: "Delivered" },
    { value: "paid", label: "Paid" },
    { value: "unverified", label: "Unverified" },
    { value: "returned", label: "Returned" },
    { value: "received", label: "Received" },
];

export const cities = [
    "Ariana",
    "<PERSON>ja",
    "Ben Arous",
    "Bizerte",
    "<PERSON>s",
    "<PERSON><PERSON><PERSON>",
    "Jendouba",
    "Kasserine",
    "Kef",
    "Mahdia",
    "Manouba",
    "Monastir",
    "Nabeul",
    "Sfax",
    "Sidi Bouzid",
    "Sousse",
    "Siliana",
    "Tataouine",
    "Tozeur",
    "Tunis",
    "Zaghouan",
    "Medenine",
    "Kebili",
    "Kairouan",
];

export const cities2 = {
    TN: [
        "Ariana",
        "Beja",
        "Ben Arous",
        "Bizerte",
        "Gabes",
        "Gafsa",
        "Jendouba",
        "Kasserine",
        "Kef",
        "Mahdia",
        "Manouba",
        "Monastir",
        "Nabeul",
        "Sfax",
        "Sidi Bouzid",
        "Sousse",
        "Siliana",
        "Tataouine",
        "Tozeur",
        "Tunis",
        "Zaghouan",
        "Medenine",
        "Kebili",
        "Kairouan",
    ],
    DZ: [
        "Adrar",
        "Chlef",
        "Laghouat",
        "Oum El Bouaghi",
        "Batna",
        "Bejaia",
        "Biskra",
        "Bechar",
        "Blida",
        "Bouira",
        "Tamanrasset",
        "Tebessa",
        "Tlemcen",
        "Tiaret",
        "Tizi Ouzou",
        "Alger",
        "Djelfa",
        "Jijel",
        "Setif",
        "Saida",
        "Skikda",
        "Sidi Bel Abbes",
        "Annaba",
        "Guelma",
        "Constantine",
        "Medea",
        "Mostaganem",
        "M'Sila",
        "Mascara",
        "Ouargla",
        "Oran",
        "El Bayadh",
        "Illizi",
        "Bordj Bou Arreridj",
        "Boumerdes",
        "El Tarf",
        "Tindouf",
        "Tissemsilt",
        "El Oued",
        "Khenchela",
        "Souk Ahras",
        "Tipaza",
        "Mila",
        "Ain Defla",
        "Naama",
        "Ain Temouchent",
        "Ghardaia",
        "Relizane",
        "Timimoun",
        "Bordj Badji Mokhtar",
        "Ouled Djellal",
        "Beni Abbes",
        "In Salah",
        "In Guezzam",
        "Touggourt",
        "Djanet",
        "El M ghaier",
        "El Meniaa",
    ],
};

export const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
};

export const deliveryCostSetter = (
    currentOrder: Partial<Order>,

    integrations: Store["integrations"]
): ((values: Partial<Order>) => Partial<Order>) => {
    if (!integrations) return (values) => values;

    const deliveryCompanyIntegration = integrations.find(
        (integration) => integration.ref === currentOrder.deliveryCompany
    );

    if (!deliveryCompanyIntegration) return (values) => values;

    const algerianDeliveryCompanies = companies

        .filter((integration: any) => {
            return !integration.allowedCountries || integration.allowedCountries.includes("DZ");
        })

        .map((dzCompany) => dzCompany.ref);

    if (
        deliveryCompanyIntegration?.ref &&
        algerianDeliveryCompanies.includes(deliveryCompanyIntegration?.ref) &&
        deliveryCompanyIntegration.ref !== "personal"
    ) {
        const presetName = deliveryCompanyIntegration.fields.costPreset;

        if (!presetName) return (values) => values;

        const preset = integrations.find((integration) => integration.ref === "dz-wilaya-presets")?.fields.presets[
            presetName
        ] as Record<string, Record<"stopdesk" | "delivery", number>>;

        if (!preset) return (values) => values;

        const wilaya = currentOrder.customer?.city;

        if (!wilaya) return (values) => values;

        let deliveryCost;

        const stopdesk = currentOrder.customer?.stopdesk;

        if (stopdesk && stopdesk == "yes") {
            deliveryCost = preset[wilaya].stopdesk;
        } else {
            deliveryCost = preset[wilaya].delivery;
        }

        return (values) => {
            return {
                ...values,

                total: {
                    ...values.total,

                    deliveryCost,
                },
            };
        };
    }

    return (values) => {
        if (
            (!deliveryCompanyIntegration.fields || deliveryCompanyIntegration.fields.deliveryCost === undefined) &&
            deliveryCompanyIntegration.ref !== "personal"
        )
            Toast.show({ text1: "No delivery cost found for this company!", text2: `Using 0 as default value` });

        const deliveryCost = deliveryCompanyIntegration.fields?.deliveryCost || 0;

        return {
            ...values,

            total: {
                ...values.total,

                deliveryCost,
            },
        };
    };
};

export const deliveryPriceSetter = (integrations: Store["integrations"]) => (order: Partial<Order>) => {
    if (!order.cart) return order;

    const deliveryPrice = Math.max(
        ...order.cart.map((cartItem) => {
            let deliveryPrice = cartItem.product.deliveryPrice || 0;

            const wilaya = order.customer?.city;
            if (!wilaya) return deliveryPrice;

            if (cartItem.product.integrationValues && cartItem.product.integrationValues["dz-wilaya-presets"]) {
                const presetName = cartItem.product.integrationValues["dz-wilaya-presets"].preset;
                if (!presetName) return deliveryPrice;
                if (!integrations) return deliveryPrice;

                const preset = integrations.find((integration) => integration.ref === "dz-wilaya-presets")?.fields
                    .presets[presetName] as Record<string, Record<"stopdesk" | "delivery", number>>;
                if (!preset) return deliveryPrice;

                const stopdesk = order.customer?.stopdesk;
                if (stopdesk && stopdesk == "yes") {
                    deliveryPrice = preset[wilaya].stopdesk;
                } else {
                    deliveryPrice = preset[wilaya].delivery;
                }
            }

            return deliveryPrice;
        })
    );

    const totalPrice = order.cart.reduce((acc, cartItem) => {
        return acc + cartItem.pricePerUnit * cartItem.quantity;
    }, deliveryPrice);

    return {
        ...order,
        total: {
            ...order.total,
            deliveryPrice,
            totalPrice,
        },
    };
};
