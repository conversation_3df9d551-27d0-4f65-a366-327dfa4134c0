import { useEffect } from "react";
import * as Linking from "expo-linking";
import { useFocusEffect } from "expo-router";

export const useDeepLinks = () => {
    useFocusEffect(() => {
        // Set up a listener for incoming links
        const subscription = Linking.addEventListener("url", (event) => {
            console.log("Deep link received:", event.url);
            // The router will automatically handle the URL
        });

        return () => {
            subscription.remove();
        };
    });
};
