import { useState, useCallback, useMemo, useEffect } from "react";
import Toast from "react-native-toast-message";
import { useOrderStore } from "../store/orders";
import { Order } from "../components/orders/types";
import { 
    isSuspiciousActivity, 
    calculateSnapPoints, 
    createAndroidDelay, 
    getResetUIState 
} from "../utils/blockCustomerUtils";

// Type definitions for the hook
export interface BlockCustomerState {
    loading: boolean;
    shouldDeleteOrders: boolean;
    selectedOrdersId: string[];
    deleteId: string | undefined;
    modalVisible: boolean;
    modalMode: "edit" | "delete" | "send" | "restore";
    dataReady: boolean;
}

export interface BlockCustomerBottomSheetProps {
    visible: boolean;
    orderId: string;
    onClose: () => void;
    onSuccess: (isDeleteAll: boolean) => void;
}

export interface UseBlockCustomerReturn {
    state: BlockCustomerState;
    customerOrders: Order[];
    showWarning: boolean;
    snapPoints: string[];
    swipeableClose: (() => void) | undefined;
    actions: {
        setState: React.Dispatch<React.SetStateAction<BlockCustomerState>>;
        setSwipeableClose: React.Dispatch<React.SetStateAction<(() => void) | undefined>>;
        handleSetSwipeableClose: (callback: () => void) => void;
        fetchCustomerOrders: () => Promise<Order[]>;
        handleBlockCustomer: () => Promise<void>;
        handleDeleteOrder: (deleteId: string) => Promise<void>;
        handleRestoreOrder: (restoreId: string) => Promise<void>;
        handleSheetChanges: (index: number) => void;
    };
}

const initialState: BlockCustomerState = {
    loading: false,
    shouldDeleteOrders: false,
    selectedOrdersId: [],
    deleteId: undefined,
    modalVisible: false,
    modalMode: "delete",
    dataReady: false,
};

export const useBlockCustomer = ({
    visible,
    orderId,
    onClose,
    onSuccess,
}: BlockCustomerBottomSheetProps): UseBlockCustomerReturn => {
    const {
        customerOrders,
        fetchCustomerOrdersByOrderId,
        blockCustomer,
        deleteCustomerOrders,
        deleteOrder,
        restoreOrder,
    } = useOrderStore();

    const [state, setState] = useState<BlockCustomerState>(initialState);
    const [swipeableClose, setSwipeableClose] = useState<(() => void) | undefined>(undefined);

    // Memoized calculations
    const showWarning = useMemo(() => isSuspiciousActivity(customerOrders), [customerOrders]);
    
    const snapPoints = useMemo(() => 
        calculateSnapPoints(
            customerOrders.length,
            state.loading,
            state.dataReady,
            showWarning
        ), 
        [customerOrders.length, state.loading, state.dataReady, showWarning]
    );

    // Optimized callback for swipeable close
    const handleSetSwipeableClose = useCallback((callback: () => void) => {
        setSwipeableClose(() => callback);
    }, []);

    // Data fetching
    const fetchCustomerOrders = useCallback(async () => {
        if (!orderId) return [];

        try {
            setState((prev) => ({ ...prev, loading: true, dataReady: false }));
            const orders = await fetchCustomerOrdersByOrderId(orderId);
            
            console.log("Orders status => ", orders.map((order) => order.status));

            createAndroidDelay(() => {
                setState((prev) => ({ ...prev, loading: false, dataReady: true }));
            });

            return orders;
        } catch (error) {
            console.error("Error fetching customer orders:", error);
            setState((prev) => ({ ...prev, loading: false, dataReady: true }));
            return [];
        }
    }, [orderId, fetchCustomerOrdersByOrderId]);

    // Block customer action
    const handleBlockCustomer = useCallback(async () => {
        try {
            setState((prev) => ({ ...prev, loading: true }));
            await blockCustomer(orderId);

            if (state.shouldDeleteOrders) {
                await deleteCustomerOrders(orderId);
            }

            onSuccess(state.shouldDeleteOrders);
            onClose();
        } catch (error) {
            // Errors are handled in the store
        } finally {
            setState((prev) => ({ ...prev, loading: false }));
        }
    }, [orderId, state.shouldDeleteOrders, blockCustomer, deleteCustomerOrders, onSuccess, onClose]);

    // Delete order action
    const handleDeleteOrder = useCallback(async (deleteId: string) => {
        if (!deleteId) return;

        setState((prev) => ({ ...prev, loading: true }));

        try {
            const response = await deleteOrder(deleteId);
            console.log("Delete response => ", response);

            if (response) {
                const isCurrentOrder = deleteId === orderId;
                if (isCurrentOrder) {
                    onSuccess(false);
                }
            }
        } catch (error: any) {
            Toast.show({
                type: "error",
                text1: error?.response?.data?.message || "Failed to delete order",
            });
        } finally {
            if (swipeableClose) {
                swipeableClose();
            }
            setSwipeableClose(undefined);
            setState((prev) => ({
                ...prev,
                loading: false,
                deleteId: undefined,
            }));
        }
    }, [deleteOrder, orderId, onSuccess, swipeableClose]);

    // Restore order action
    const handleRestoreOrder = useCallback(async (restoreId: string) => {
        if (!restoreId) return;

        setState((prev) => ({ ...prev, loading: true }));

        try {
            const response = await restoreOrder(restoreId);

            if (response) {
                const isCurrentOrder = restoreId === orderId;
                if (isCurrentOrder) {
                    onSuccess(false);
                }
            }
        } catch (error: any) {
            Toast.show({
                type: "error",
                text1: error?.response?.data?.message || "Failed to restore order",
            });
        } finally {
            if (swipeableClose) {
                swipeableClose();
            }
            setSwipeableClose(undefined);
            setState((prev) => ({
                ...prev,
                loading: false,
            }));
        }
    }, [restoreOrder, orderId, onSuccess, swipeableClose]);

    // Sheet changes handler
    const handleSheetChanges = useCallback((index: number) => {
        console.log("index => ", index);
        if (index === -1) {
            setState((prev) => ({
                ...prev,
                ...getResetUIState(),
            }));
            setSwipeableClose(undefined);
            onClose();
        }
    }, [onClose]);

    // Effect for visibility changes
    useEffect(() => {
        if (visible) {
            fetchCustomerOrders();
            console.log("visible => ", visible);
        } else {
            setState((prev) => ({
                ...prev,
                ...getResetUIState(),
            }));
            setSwipeableClose(undefined);
        }
    }, [visible, orderId, fetchCustomerOrders]);

    // Cleanup effect
    useEffect(() => {
        return () => {
            setState((prev) => ({
                ...prev,
                ...getResetUIState(),
            }));
            setSwipeableClose(undefined);
        };
    }, []);

    // Debug effect
    useEffect(() => {
        console.log("customerorders list => ", customerOrders.length);
    }, [visible, customerOrders.length]);

    return {
        state,
        customerOrders,
        showWarning,
        snapPoints,
        swipeableClose,
        actions: {
            setState,
            setSwipeableClose,
            handleSetSwipeableClose,
            fetchCustomerOrders,
            handleBlockCustomer,
            handleDeleteOrder,
            handleRestoreOrder,
            handleSheetChanges,
        },
    };
};
