import { useAnimatedStyle, interpolateColor, withTiming } from 'react-native-reanimated';
import colors from '@styles/colors';

interface UseOrderCardAnimationProps {
    selected: boolean;
}

interface UseOrderCardAnimationReturn {
    selectedAnimation: any;
}

/**
 * Custom hook for OrderCard selection animation
 * Handles the background color transition when card is selected
 */
export const useOrderCardAnimation = ({
    selected,
}: UseOrderCardAnimationProps): UseOrderCardAnimationReturn => {
    const selectedAnimation = useAnimatedStyle(() => {
        const color = interpolateColor(
            selected ? 1 : 0,
            [0, 1],
            [colors.blue[50], colors.blue[100]]
        );
        const colorValue = withTiming(color, { duration: 100 });

        return {
            backgroundColor: colorValue,
        };
    });

    return {
        selectedAnimation,
    };
};
