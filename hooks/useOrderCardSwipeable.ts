import { useCallback, RefObject } from 'react';
import { router } from 'expo-router';
import { Order } from '@components/orders/types';
import { useOrderStore } from '../store/orders';

interface UseOrderCardSwipeableProps {
    order: Order;
    swipeableRef: RefObject<any>;
    setDeleteId: (id: string) => void;
    setModalVisible: (visible: boolean) => void;
    setModalMode: (mode: 'edit' | 'delete' | 'send' | 'restore') => void;
    setSwipeableClose: (callback: () => () => void) => void;
    disableInteractions: boolean;
}

interface UseOrderCardSwipeableReturn {
    handleSwipeableWillOpen: (direction: 'left' | 'right') => void;
}

/**
 * Custom hook for OrderCard swipeable actions
 * Handles left swipe (edit) and right swipe (delete/restore) logic
 */
export const useOrderCardSwipeable = ({
    order,
    swipeableRef,
    setDeleteId,
    setModalVisible,
    setModalMode,
    setSwipeableClose,
    disableInteractions,
}: UseOrderCardSwipeableProps): UseOrderCardSwipeableReturn => {
    const { setOrder } = useOrderStore();

    const handleSwipeableWillOpen = useCallback((direction: 'left' | 'right') => {
        if (direction === 'right') {
            if (order.status === 'deleted') {
                // Show restore modal for deleted orders
                setDeleteId(order._id);
                setModalVisible(true);
                setModalMode('restore');
                setSwipeableClose(()  => swipeableRef?.current?.close());
            } else {
                setDeleteId(order._id);
                setModalVisible(true);
                setModalMode('delete');
                setSwipeableClose(()  => swipeableRef?.current?.close());
            }
        } else if (!disableInteractions) {
            // Left swipe - Edit action
            setOrder(order);
            router.navigate({ params: { id: order._id }, pathname: '/order/edit' });
            swipeableRef?.current?.close();
        }
    }, [
        order,
        swipeableRef,
        setDeleteId,
        setModalVisible,
        setModalMode,
        setSwipeableClose,
        disableInteractions,
        setOrder,
    ]);

    return {
        handleSwipeableWillOpen,
    };
};
