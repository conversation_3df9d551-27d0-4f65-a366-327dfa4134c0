import { useState, useCallback, useEffect } from "react";
import { Keyboard } from "react-native";
import * as Notifications from "expo-notifications";
import { z } from "zod";
import { registerForPushNotificationsAsync } from "../utils/notificationsUtils";
import { useAuthStore } from "../store/authStore";
import { useStoreStore } from "../store/storeStore";

// Validation schemas - moved outside component for performance
const emailSchema = z
    .string()
    .min(1, "Email/Username is required")
    .trim();

const passwordSchema = z
    .string()
    .min(1, "Password is required")
    .trim();

// Form state interface
interface LoginFormState {
    email: string;
    password: string;
    emailError: string | null;
    passwordError: string | null;
    isSubmitting: boolean;
    token?: Notifications.DevicePushToken;
}

// Hook return interface
interface UseLoginFormReturn {
    // Form state
    formState: LoginFormState;
    
    // Form actions
    setEmail: (email: string) => void;
    setPassword: (password: string) => void;
    handleSubmit: () => Promise<void>;
    
    // Validation state
    isFormValid: boolean;
    hasErrors: boolean;
    
    // Utility
    clearErrors: () => void;
    resetForm: () => void;
}

/**
 * Custom hook for managing login form state and validation
 * Follows best practices for form handling and validation
 */
export const useLoginForm = (): UseLoginFormReturn => {
    // Store hooks - using selectors for better performance
    const { login, setAuth } = useAuthStore();
    const { getUser, setStores } = useStoreStore();
    const { setShowStoreSelector } = useStoreStore();

    // Form state
    const [formState, setFormState] = useState<LoginFormState>({
        email: "",
        password: "",
        emailError: null,
        passwordError: null,
        isSubmitting: false,
        token: undefined,
    });

    // Initialize push notification token
    useEffect(() => {
        let isMounted = true;

        registerForPushNotificationsAsync()
            .then((token) => {
                if (isMounted) {
                    setFormState(prev => ({ ...prev, token }));
                }
            })
            .catch((error) => {
                console.error("Failed to register for push notifications:", error);
            });

        return () => {
            isMounted = false;
        };
    }, []);

    // Validate email field
    const validateEmail = useCallback((email: string): string | null => {
        const result = emailSchema.safeParse(email);
        return result.success ? null : result.error.errors[0].message;
    }, []);

    // Validate password field
    const validatePassword = useCallback((password: string): string | null => {
        const result = passwordSchema.safeParse(password);
        return result.success ? null : result.error.errors[0].message;
    }, []);

    // Set email with validation
    const setEmail = useCallback((email: string) => {
        const emailError = validateEmail(email);
        setFormState(prev => ({
            ...prev,
            email,
            emailError,
        }));
    }, [validateEmail]);

    // Set password with validation
    const setPassword = useCallback((password: string) => {
        const passwordError = validatePassword(password);
        setFormState(prev => ({
            ...prev,
            password,
            passwordError,
        }));
    }, [validatePassword]);

    // Validate entire form
    const validateForm = useCallback((): boolean => {
        const emailError = validateEmail(formState.email);
        const passwordError = validatePassword(formState.password);

        // Only update state if errors have changed
        setFormState(prev => {
            if (prev.emailError !== emailError || prev.passwordError !== passwordError) {
                return {
                    ...prev,
                    emailError,
                    passwordError,
                };
            }
            return prev;
        });

        return !emailError && !passwordError;
    }, [formState.email, formState.password, validateEmail, validatePassword]);

    // Handle form submission
    const handleSubmit = useCallback(async () => {
        // Dismiss keyboard
        Keyboard.dismiss();

        // Validate form
        if (!validateForm()) {
            return;
        }

        // Prevent double submission
        if (formState.isSubmitting) {
            return;
        }

        setFormState(prev => ({ ...prev, isSubmitting: true }));

        try {
            // Use the original auth store login method for better performance
            const authResult = await login("unauth", {
                username: formState.email.trim(),
                password: formState.password.trim(),
                deviceToken: formState.token?.data || "testToken",
                deviceType: formState.token?.type || "ios",
            });

            // Update auth state
            setAuth(authResult);

            // Handle multistore case
            if (authResult === "multistore") {
                setShowStoreSelector(true);
            }

            // Fetch user data on successful auth
            if (authResult === "auth" || authResult === "multistore") {
                await getUser();
            }

        } catch (error) {
            console.error("Login submission error:", error);
        } finally {
            setFormState(prev => ({ ...prev, isSubmitting: false }));
        }
    }, [
        formState.email,
        formState.password,
        formState.token,
        formState.isSubmitting,
        validateForm,
        setAuth,
        getUser,
    ]);

    // Clear all errors
    const clearErrors = useCallback(() => {
        setFormState(prev => ({
            ...prev,
            emailError: null,
            passwordError: null,
        }));
    }, []);

    // Reset entire form
    const resetForm = useCallback(() => {
        setFormState(prev => ({
            ...prev,
            // email: "",
            // password: "",
            emailError: null,
            passwordError: null,
            isSubmitting: false,
        }));
    }, []);

    // Computed values
    const isFormValid = !formState.emailError &&
                       !formState.passwordError &&
                       formState.email.trim().length > 0 &&
                       formState.password.trim().length > 0;

    const hasErrors = !!formState.emailError || !!formState.passwordError;

    return {
        formState,
        setEmail,
        setPassword,
        handleSubmit,
        isFormValid,
        hasErrors,
        clearErrors,
        resetForm,
    };
};
