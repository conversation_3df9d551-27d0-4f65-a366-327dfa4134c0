import { useCallback, useMemo, useState } from 'react';
import { useSharedValue, withTiming } from 'react-native-reanimated';
import { Order } from '@components/orders/types';

// Constants
const ANIMATION_DURATION = 200;

// Types
interface OrdersPerformanceState {
  selectedOrdersId: string[];
  selectionMode: boolean;
  modalVisible: boolean;
  modalMode: 'edit' | 'delete' | 'send' | 'restore';
  deleteId?: string;
  swipeableClose?: () => void;
}

interface UseOrdersPerformanceReturn {
  // State
  selectedOrdersId: string[];
  selectionMode: boolean;
  modalVisible: boolean;
  modalMode: 'edit' | 'delete' | 'send' | 'restore';
  deleteId?: string;
  swipeableClose?: () => void;

  // Animated values
  actionButtonRight: any;
  actionButtonBottom: any;

  // Handlers
  handleSetSelectedOrdersId: (ids: string[]) => void;
  handleSetDeleteId: (id: string, orders: Order[]) => void;
  handleSetModalVisible: (visible: boolean) => void;
  handleSetModalMode: (mode: 'edit' | 'delete' | 'send' | 'restore') => void;
  handleSetSwipeableClose: (closeFunction: (() => void) | undefined) => void;

  // Context value (memoized)
  contextValue: any;

  // FlatList extraData (memoized)
  extraData: any;
}

/**
 * useOrdersPerformance Hook
 *
 * Handles performance-critical state management for orders UI interactions.
 * Includes memoized handlers, animated values, and optimized context values.
 */
export const useOrdersPerformance = (filter: any): UseOrdersPerformanceReturn => {
  // Animated values for selection mode
  const actionButtonRight = useSharedValue(-100);
  const actionButtonBottom = useSharedValue(-100);

  // Local state for UI interactions
  const [state, setState] = useState<OrdersPerformanceState>({
    selectedOrdersId: [],
    selectionMode: false,
    modalVisible: false,
    modalMode: 'edit',
    deleteId: undefined,
    swipeableClose: undefined,
  });

  // Memoized callback functions for OrderCard - prevents re-renders
  const handleSetSelectedOrdersId = useCallback((ids: string[]) => {
    setState((prev) => ({ ...prev, selectedOrdersId: ids }));

    // Update selection mode and animate buttons
    const newSelectionMode = ids.length > 0;
    setState((prev) => ({ ...prev, selectionMode: newSelectionMode }));

    actionButtonRight.value = withTiming(newSelectionMode ? 0 : -100, { duration: ANIMATION_DURATION });
    actionButtonBottom.value = withTiming(newSelectionMode ? 0 : -100, { duration: ANIMATION_DURATION });
  }, [actionButtonRight, actionButtonBottom]);

  const handleSetDeleteId = useCallback(
    (id: string, orders: Order[]) => {
      const order = orders.find((o) => o._id === id);
      setState((prev) => ({
        ...prev,
        deleteId: id,
        modalVisible: true,
        modalMode: order?.status === 'deleted' ? 'restore' : 'delete',
      }));
    },
    []
  );

  const handleSetModalVisible = useCallback((visible: boolean) => {
    setState((prev) => ({ ...prev, modalVisible: visible }));

    // Clear deleteId when modal is closed
    if (!visible) {
      setState((prev) => ({
        ...prev,
        deleteId: undefined,
      }));
    }
  }, []);

  const handleSetModalMode = useCallback((mode: 'edit' | 'delete' | 'send' | 'restore') => {
    setState((prev) => ({ ...prev, modalMode: mode }));
  }, []);

  const handleSetSwipeableClose = useCallback((closeFunction: (() => void) | undefined) => {
    setState((prev) => ({ ...prev, swipeableClose: closeFunction }));
  }, []);

  // Memoized context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      modalMode: state.modalMode,
      selectedOrdersId: state.selectedOrdersId,
      selectionMode: state.selectionMode,
      setDeleteId: handleSetDeleteId,
      setModalMode: handleSetModalMode,
      setSelectedOrdersId: handleSetSelectedOrdersId,
      setVisible: handleSetModalVisible,
      visible: state.modalVisible,
    }),
    [
      state.modalMode,
      state.selectedOrdersId,
      state.selectionMode,
      state.modalVisible,
      handleSetDeleteId,
      handleSetModalMode,
      handleSetSelectedOrdersId,
      handleSetModalVisible,
    ]
  );

  // Memoized extraData for FlatList optimization
  const extraData = useMemo(
    () => ({
      selectionMode: state.selectionMode,
      selectedCount: state.selectedOrdersId.length,
      filter,
    }),
    [state.selectionMode, state.selectedOrdersId.length, filter]
  );

  return {
    // State
    selectedOrdersId: state.selectedOrdersId,
    selectionMode: state.selectionMode,
    modalVisible: state.modalVisible,
    modalMode: state.modalMode,
    deleteId: state.deleteId,
    swipeableClose: state.swipeableClose,

    // Animated values
    actionButtonRight,
    actionButtonBottom,

    // Handlers
    handleSetSelectedOrdersId,
    handleSetDeleteId,
    handleSetModalVisible,
    handleSetModalMode,
    handleSetSwipeableClose,

    // Memoized values
    contextValue,
    extraData,
  };
};