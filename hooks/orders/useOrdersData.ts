import { useCallback, useEffect, useMemo, useState } from 'react';
import { router, useLocalSearchParams } from 'expo-router';
import Toast from 'react-native-toast-message';
import api from '@api/api';
import { useOrderStore } from '../../store/orders';
import { useStoreStore } from '../../store/storeStore';

import { Order, Filter, OrderResponseProp } from '@components/orders/types';
import { Product } from '../../types/Product';

// Constants
const ITEMS_PER_PAGE = 10;

// Types
interface OrdersDataState {
  loading: boolean;
  page: number;
  noMore: boolean;
  filter: Filter;
  searchValue: string;
  activeFilterChip: number;
  activeDeliveryCompany: string | null;
  activeProductItem: Product | null;
}

interface UseOrdersDataReturn {
  // State
  orders: Order[];
  loading: boolean;
  noMore: boolean;
  filter: Filter;
  searchValue: string;
  activeFilterChip: number;
  activeDeliveryCompany: string | null;
  activeProductItem: Product | null;
  hasOrderPermissions: boolean;

  // Actions
  handleLoadMore: (forcePage?: number, forceFilter?: Filter) => Promise<void>;
  handleRefresh: () => Promise<void>;
  handleFilterChange: (newFilter: Partial<Filter>) => void;
  handleStatusFilter: (status: string, index: number) => void;
  handleSearchFilter: (searchText: string) => void;
  handleDeliveryCompanyFilter: (deliveryCompany: string | null) => void;
  handleProductFilter: (product: Product | null) => void;
}

/**
 * useOrdersData Hook
 *
 * A comprehensive hook for managing orders data, filtering, pagination, and API calls.
 * Follows Zustand best practices with optimized selectors and minimal re-renders.
 */
export const useOrdersData = (): UseOrdersDataReturn => {
  // Zustand stores with optimized selectors
  const { orders, setOrders, pushToOrders } = useOrderStore();

  const { user, storeChanged, setStoreChanged } = useStoreStore();
  const { refresh: refreshParam } = useLocalSearchParams();

  // Local state for data management
  const [state, setState] = useState<OrdersDataState>({
    loading: false,
    page: 1,
    noMore: false,
    filter: {},
    searchValue: '',
    activeFilterChip: 0,
    activeDeliveryCompany: null,
    activeProductItem: null,
  });

  // Memoized permissions check
  const hasOrderPermissions = useMemo(() => {
    return user?.permissions === 'all' || user?.permissions?.order?.read;
  }, [user?.permissions]);

  // API call for fetching orders
  const fetchOrders = useCallback(
    async (page: number, filter: Filter): Promise<Order[]> => {
      if (!hasOrderPermissions) {
        router.navigate({
          pathname: '/network-error',
          params: { message: 'You do not have permission to view orders' },
        });
        return [];
      }

      try {
        const queryParams = new URLSearchParams();
        queryParams.append('mobile', 'true');
        queryParams.append('limit', String(ITEMS_PER_PAGE));
        queryParams.append('page', String(page));

        Object.entries(filter).forEach(([key, value]) => {
          if (value !== undefined) {
            queryParams.append(key, String(value));
          }
        });

        console.log('Orders API query =>', queryParams.toString());
        const response: OrderResponseProp = await api.get(`/order?${queryParams.toString()}`);

        return response.data || [];
      } catch (error) {
        console.error('Failed to fetch orders:', error);
        router.navigate({ pathname: '/network-error', params: error });
        return [];
      }
    },
    [hasOrderPermissions]
  );

  // Load more orders with pagination
  const handleLoadMore = useCallback(
    async (forcePage?: number, forceFilter?: Filter) => {
      const currentPage = forcePage !== undefined ? forcePage : state.page;
      const currentFilter = forceFilter !== undefined ? forceFilter : state.filter;

      if (state.loading || state.noMore) return;

      setState((prev) => ({ ...prev, loading: true }));

      try {
        const newOrders = await fetchOrders(currentPage, currentFilter);

        if (newOrders.length === 0 || newOrders.length < ITEMS_PER_PAGE) {
          setState((prev) => ({ ...prev, noMore: true }));
        }

        if (currentPage === 1) {
          setOrders(newOrders);
        } else {
          pushToOrders(newOrders);
        }

        setState((prev) => ({
          ...prev,
          page: currentPage + 1,
          loading: false,
        }));
      } catch (error) {
        console.error('Failed to load more orders:', error);
        Toast.show({ type: 'error', text1: 'Failed to load orders' });
        setState((prev) => ({ ...prev, loading: false }));
      }
    },
    [state.page, state.filter, state.loading, state.noMore, fetchOrders, setOrders, pushToOrders]
  );

  // Refresh orders with reset
  const handleRefresh = useCallback(async () => {
    setState((prev) => ({
      ...prev,
      loading: true,
      page: 1,
      activeFilterChip: 0, // Reset to first chip (which is "all")
      filter: {}, // Clear all filters
      activeDeliveryCompany: null, // Reset delivery company
      activeProductItem: null, // Reset product
      searchValue: '', // Reset search
    }));

    try {
      const response = await api.get('/order?mobile=true&limit=10&page=1');
      setOrders(response.data || []); // Replace entire list with fresh data
      setState((prev) => ({
        ...prev,
        noMore: (response.data?.length || 0) < ITEMS_PER_PAGE,
      }));
    } catch (error) {
      console.error('Failed to refresh orders:', error);
      Toast.show({
        type: 'error',
        text1: 'Failed to refresh orders',
        text2: error.message,
      });
    } finally {
      setState((prev) => ({
        ...prev,
        loading: false,
      }));
    }
  }, [setOrders]);

  // Handle filter changes with debounced API calls
  const handleFilterChange = useCallback(
    (newFilter: Partial<Filter>) => {
      // Create a new filter object
      const updatedFilter = { ...state.filter };

      // Handle each filter type specifically
      Object.entries(newFilter).forEach(([key, value]) => {
        if (value === 'all' || value === '' || value === null || value === undefined) {
          // Remove the filter if it's set to 'all' or is empty
          delete updatedFilter[key];
        } else {
          // Otherwise set the new value
          updatedFilter[key] = value;
        }
      });

      // Update state with reset pagination
      setState((prev) => ({
        ...prev,
        filter: updatedFilter,
        page: 1,
        noMore: false,
      }));

      // Reset orders and immediately fetch with new filter and page 1
      setOrders([]);

      // Use setTimeout to ensure state update has been processed
      setTimeout(() => {
        handleLoadMore(1, updatedFilter);
      }, 0);
    },
    [state.filter, handleLoadMore, setOrders]
  );

  // Status filter handler
  const handleStatusFilter = useCallback(
    (status: string, index: number) => {
      setState((prev) => ({
        ...prev,
        activeFilterChip: index,
      }));

      handleFilterChange({
        status: status === 'all' ? undefined : status,
      });
    },
    [handleFilterChange]
  );

  // Search filter handler
  const handleSearchFilter = useCallback(
    (searchText: string) => {
      setState((prev) => ({ ...prev, searchValue: searchText }));
      handleFilterChange({ search: searchText || undefined });
    },
    [handleFilterChange]
  );

  // Delivery company filter handler
  const handleDeliveryCompanyFilter = useCallback(
    (deliveryCompany: string | null) => {
      setState((prev) => ({
        ...prev,
        activeDeliveryCompany: deliveryCompany,
      }));

      // If "All Companies" is selected or deliveryCompany is null/store, remove from filter
      if (
        deliveryCompany === 'All Companies' ||
        !deliveryCompany ||
        deliveryCompany === 'store' ||
        deliveryCompany === 'all'
      ) {
        handleFilterChange({
          deliveryCompany: undefined, // This will ensure the parameter is omitted
        });
      } else {
        handleFilterChange({
          deliveryCompany, // Include specific company in filter
        });
      }
    },
    [handleFilterChange]
  );

  // Product filter handler
  const handleProductFilter = useCallback(
    (product: Product | null) => {
      setState((prev) => ({
        ...prev,
        activeProductItem: product,
      }));

      // If "All Products" is selected or product is null/all, remove from filter
      if (!product || product.name === 'All Products' || product._id === 'all' || product._id === 'store') {
        handleFilterChange({
          product: undefined, // This will ensure the parameter is omitted
        });
      } else {
        handleFilterChange({
          product: product._id, // Include specific product ID in filter
        });
      }
    },
    [handleFilterChange]
  );

  // Effects
  // Handle store changes
  useEffect(() => {
    if (storeChanged) {
      handleRefresh();
      setStoreChanged(false);
    }
  }, [storeChanged, handleRefresh, setStoreChanged]);

  // Initial load effect - only runs when orders are empty and not loading
  useEffect(() => {
    if (orders.length === 0 && !state.loading) {
      handleLoadMore();
    }
  }, [orders.length, state.loading, handleLoadMore]);

  // Handle refresh parameter from navigation
  useEffect(() => {
    if (refreshParam === 'true') {
      handleRefresh();
      router.setParams({ refresh: undefined });
    }
  }, [refreshParam, handleRefresh]);

  // Return hook interface
  return {
    // State
    orders,
    loading: state.loading,
    noMore: state.noMore,
    filter: state.filter,
    searchValue: state.searchValue,
    activeFilterChip: state.activeFilterChip,
    activeDeliveryCompany: state.activeDeliveryCompany,
    activeProductItem: state.activeProductItem,
    hasOrderPermissions,

    // Actions
    handleLoadMore,
    handleRefresh,
    handleFilterChange,
    handleStatusFilter,
    handleSearchFilter,
    handleDeliveryCompanyFilter,
    handleProductFilter,
  };
};