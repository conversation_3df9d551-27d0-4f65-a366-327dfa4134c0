import { useCallback } from 'react';
import { Gesture } from 'react-native-gesture-handler';
import { runOnJS } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { Order } from '@components/orders/types';
import { useOrderStore } from '../store/orders';

interface UseOrderCardGesturesProps {
    order: Order;
    selectionMode: boolean;
    selectedOrdersId: string[];
    setSelectedOrdersId: (ids: string[]) => void;
    disableInteractions: boolean;
}

interface UseOrderCardGesturesReturn {
    gestures: any;
    handleCardClick: () => void;
    toggleSelection: () => void;
}

/**
 * Custom hook for OrderCard gesture handling
 * Isolates tap, long press, and selection logic from the main component
 */
export const useOrderCardGestures = ({
    order,
    selectionMode,
    selectedOrdersId,
    setSelectedOrdersId,
    disableInteractions,
}: UseOrderCardGesturesProps): UseOrderCardGesturesReturn => {
    const { setOrder } = useOrderStore();

    const handleCardClick = useCallback(() => {
        if (disableInteractions) return;
        setOrder(order);
        router.navigate({
            pathname: '/order/orderDetails',
            params: { order: order._id },
        });
    }, [disableInteractions, order, setOrder]);

    const toggleSelection = useCallback(() => {
        if (disableInteractions) return;
        Haptics.impactAsync();
        setSelectedOrdersId(
            selectedOrdersId.includes(order._id)
                ? selectedOrdersId.filter((id: string) => id !== order._id)
                : [...selectedOrdersId, order._id]
        );
    }, [disableInteractions, selectedOrdersId, order._id, setSelectedOrdersId]);

    const handleLongPress = useCallback(() => {
        if (disableInteractions) return;
        toggleSelection();
    }, [disableInteractions, toggleSelection]);

    const handleTap = useCallback(() => {
        if (disableInteractions) return;
        if (!selectionMode) {
            handleCardClick();
        } else {
            toggleSelection();
        }
    }, [disableInteractions, selectionMode, handleCardClick, toggleSelection]);

    // Gesture configuration
    const longPress = Gesture.LongPress().onStart(() => {
        runOnJS(handleLongPress)();
    });

    const tap = Gesture.Tap().onStart(() => {
        runOnJS(handleTap)();
    });

    const emptyGesture = Gesture.Tap().enabled(false);
    const gestures = disableInteractions ? emptyGesture : Gesture.Race(tap, longPress);

    return {
        gestures,
        handleCardClick,
        toggleSelection,
    };
};
