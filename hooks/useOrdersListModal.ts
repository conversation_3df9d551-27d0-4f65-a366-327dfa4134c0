import React, { useState, useCallback } from 'react';
import { useOrderStore, useOrderActionsStore, useOrdersStore } from '../store/orders';
import { Order } from '@components/orders/types';
import { Status } from '../types/Order';
import { OrderEditFormData, RejectionReason, MODAL_CONFIG } from '../components/modals/types/OrdersListModalTypes';
import { OrderModalUtils } from '../utils/orderModalUtils';
import api from '@api/api';
import Toast from 'react-native-toast-message';

/**
 * Custom hook for managing order actions in the modal
 */
export const useOrderActions = () => {
    const [loading, setLoading] = useState(false);

    const handleSendLeads = useCallback(async (selectedOrders: Order[]) => {
        try {
            setLoading(true);

            // Validate if orders can be sent as leads
            const canSend = OrderModalUtils.canSendAsLeads(selectedOrders);
            if (!canSend.canSend) {
                Toast.show({
                    type: "error",
                    text1: "Cannot Send Leads",
                    text2: canSend.reason
                });
                return { success: false, orders: [] };
            }

            const response = await api.post("/order/send-leads", {
                references: OrderModalUtils.extractOrderReferences(selectedOrders),
            });
            
            if (response.success) {
                Toast.show({ 
                    text1: "Leads Sent", 
                    text2: response.message 
                });
                return { success: true, orders: response.data };
            } else {
                Toast.show({ 
                    text1: "Failed to Send", 
                    text2: response.message, 
                    type: "error" 
                });
                return { success: false, orders: [] };
            }
        } catch (error: any) {
            console.error("Error sending leads:", error);
            Toast.show({ 
                text1: "Failed to Send", 
                text2: error.message, 
                type: "error" 
            });
            return { success: false, orders: [] };
        } finally {
            setLoading(false);
        }
    }, []);

    const handleEditOrders = useCallback(async (
        selectedOrders: Order[],
        formData: OrderEditFormData,
        onClear?: () => void
    ) => {
        try {
            setLoading(true);

            // Validate form data
            const validation = OrderModalUtils.validateEditFormData(formData);
            if (!validation.isValid) {
                Toast.show({
                    type: "error",
                    text1: "Validation Error",
                    text2: validation.errors.join(", ")
                });
                return { success: false, orders: [] };
            }

            // Sanitize and build request body
            const sanitizedData = OrderModalUtils.sanitizeFormData(formData);
            const body = OrderModalUtils.buildEditRequestBody(sanitizedData);

            const response = await api.patch("/order", {
                references: OrderModalUtils.extractOrderReferences(selectedOrders),
                ...body,
            });

            console.log('bulk edit body =>', body);
            
            if (response.success) {
                useOrdersStore.getState().updateOrderInList(response.data[0].reference, response.data[0]);
                onClear?.();
                Toast.show({ 
                    type: "success", 
                    text1: "Orders Updated", 
                    text2: response.message 
                });
                return { success: true, orders: response.data };
            } else {
                Toast.show({ 
                    type: "error", 
                    text1: "Orders Update Failed", 
                    text2: response.message 
                });
                return { success: false, orders: [] };
            }
        } catch (error: any) {
            console.error("Error updating orders:", error);
            Toast.show({ 
                text1: "Failed to Update Order", 
                text2: error.message, 
                type: "error" 
            });
            return { success: false, orders: [] };
        } finally {
            setLoading(false);
        }
    }, []);

    const handleDeleteOrder = useCallback(async (
        selectedOrder: Order,
        onCancel?: () => void,
        setModalLoading?: (loading: boolean) => void
    ) => {
        try {
            // Validate if order can be deleted
            const canDelete = OrderModalUtils.canDeleteOrder(selectedOrder);
            if (!canDelete.canDelete) {
                Toast.show({
                    type: "error",
                    text1: "Cannot Delete Order",
                    text2: canDelete.reason
                });
                return { success: false };
            }

            onCancel?.(); // Close swipeable first
            setModalLoading?.(true);

            const response = await api.delete(`/order/${selectedOrder._id}`);
            
            if (!response.success) {
                Toast.show({
                    type: "error",
                    text1: "Failed to Delete Order",
                    text2: response.message,
                });
                return { success: false };
            }
            
            return { success: true };
        } catch (error: any) {
            Toast.show({
                type: "error",
                text1: `Error Deleting Order ${selectedOrder.reference}`,
                text2: error.response?.data?.message || "Unknown error",
            });
            return { success: false };
        } finally {
            setModalLoading?.(false);
        }
    }, []);

    const handleRestoreOrder = useCallback(async (
        orderId: string,
        selectedOrder: Order,
        onCancel?: () => void,
        onSuccess?: () => void,
        setModalLoading?: (loading: boolean) => void
    ) => {
        try {
            // Validate if order can be restored
            const canRestore = OrderModalUtils.canRestoreOrder(selectedOrder);
            if (!canRestore.canRestore) {
                Toast.show({
                    type: "error",
                    text1: "Cannot Restore Order",
                    text2: canRestore.reason
                });
                return { success: false };
            }

            onCancel?.(); // Close swipeable first
            setModalLoading?.(true);

            await useOrderActionsStore
                .getState()
                .restoreOrder(orderId);
            
            onSuccess?.();
            return { success: true };
        } catch (error: any) {
            Toast.show({
                type: "error",
                text1: "Error Restoring Order",
                text2: error.response?.data?.message || "Unknown error",
            });
            return { success: false };
        } finally {
            setModalLoading?.(false);
        }
    }, []);

    return {
        loading,
        handleSendLeads,
        handleEditOrders,
        handleDeleteOrder,
        handleRestoreOrder,
    };
};

/**
 * Custom hook for managing modal state
 */
export const useModalState = () => {
    const [attempt, setAttempt] = useState<number>(MODAL_CONFIG.DEFAULT_ATTEMPT);
    const [deliveryCompany, setDeliveryCompany] = useState<string>();
    const [status, setStatus] = useState<Status>(MODAL_CONFIG.DEFAULT_STATUS);
    const [rejectionReason, setRejectionReason] = useState<string>();
    const [note, setNote] = useState<string>();

    const resetForm = useCallback(() => {
        setNote(undefined);
        setAttempt(MODAL_CONFIG.DEFAULT_ATTEMPT);
        setDeliveryCompany(undefined);
        setRejectionReason(undefined);
        setStatus(MODAL_CONFIG.DEFAULT_STATUS);
    }, []);

    const getFormData = useCallback((): OrderEditFormData => ({
        status,
        deliveryCompany,
        attempt,
        rejectionReason,
        note,
    }), [status, deliveryCompany, attempt, rejectionReason, note]);

    return {
        // State
        attempt,
        deliveryCompany,
        status,
        rejectionReason,
        note,
        
        // Setters
        setAttempt,
        setDeliveryCompany,
        setStatus,
        setRejectionReason,
        setNote,
        
        // Utilities
        resetForm,
        getFormData,
    };
};

/**
 * Custom hook for managing order selection
 */
export const useOrderSelection = (orderIds: string[]) => {
    const { orders } = useOrderStore();
    const [selectedOrders, setSelectedOrders] = useState<Order[]>([]);

    const updateSelectedOrders = useCallback(() => {
        if (orderIds.length > 0) {
            const selected = orders.filter((order) => orderIds.includes(order._id));
            setSelectedOrders(selected);
        } else {
            setSelectedOrders([]);
        }
    }, [orderIds, orders]);

    // Update selected orders when orderIds or orders change
    React.useEffect(() => {
        updateSelectedOrders();
    }, [updateSelectedOrders]);

    return {
        selectedOrders,
        updateSelectedOrders,
    };
};
