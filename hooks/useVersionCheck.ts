import { useCallback } from "react";
import checkVersion from "../utils/versionUtils";

interface VersionCheckResult {
    forceUpdate: boolean;
}

export const useVersionCheck = () => {
    const checkForUpdates = useCallback(async (): Promise<VersionCheckResult> => {
        try {
            const result = await checkVersion();
            return result;
        } catch (error) {
            console.error("Error checking version:", error);
            return { forceUpdate: false };
        }
    }, []);

    return {
        checkForUpdates,
    };
};
