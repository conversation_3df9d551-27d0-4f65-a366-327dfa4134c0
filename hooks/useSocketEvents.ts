import { useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Toast from "react-native-toast-message";
import { socketManager } from "../socket/socket";
import { useOrdersStore, useCustomerOrdersStore } from "../store/orders";
import { useStoreStore } from "../store/storeStore";
import { Order } from "../components/orders/types";
import { Store } from "../types/Store";
import { SocketInfo } from "../socket/socketManager";

/**
 * Custom hook for setting up order and store socket event listeners
 * Socket manager handles connection management, this hook handles business logic
 */
export const useSocketEvents = () => {
    // Order event handlers
    const onOrderCreated = async (_info: SocketInfo, order: Order) => {
        console.log(`Socket: order ${order.reference} created`);
        useOrdersStore.getState().addOrderToList(order);
        console.log(`Order ${order.reference} added!`);
    };

    const onOrderUpdated = async (_info: SocketInfo, order: Order) => {
        useOrdersStore.getState().updateOrderInList(order.reference, order);
        Toast.show({
            type: "info",
            text1: "Order Updated",
            text2: `Order ${order.reference} has been updated`,
        });
        console.log(`order ${order.reference} updated!`);
    };

    const onOrderDeleted = async (_info: SocketInfo, orderData: any) => {
        const orderId = typeof orderData === 'string' ? orderData : orderData._id;
        const orderReference = orderData?.reference;
        console.log(`Socket: order ${orderId} deleted`);

        // Handle deletion in main orders list
        useOrdersStore.getState().removeOrderFromList(orderId);

        // Update status in customer orders list (block customer functionality)
        useCustomerOrdersStore.getState().updateCustomerOrderStatus(orderId, "deleted");

        // Show toast notification with conditional reference
        Toast.show({
            type: "info",
            text1: "Order Deleted",
            text2: orderReference ? `Order #${orderReference} has been deleted` : "Order has been deleted",
        });
    };

    const onOrderRestored = async (_info: SocketInfo, restoredOrder: Order) => {
        console.log(`Socket: order ${restoredOrder.reference} (${restoredOrder._id}) restored`);

        // Handle restoration in main orders list
        useOrdersStore.getState().handleOrderRestored(restoredOrder);

        // Update status in customer orders list (block customer functionality)
        useCustomerOrdersStore.getState().updateCustomerOrderStatus(restoredOrder._id, "restored");

        // Show toast notification with conditional reference
        Toast.show({
            type: "success",
            text1: "Order Restored",
            text2: restoredOrder.reference ? `Order #${restoredOrder.reference} has been restored` : "Order has been restored",
        });

        console.log(`order ${restoredOrder.reference} restored!`);
    };

    const onOrdersUpdated = (_info: SocketInfo, orders: Order[]) => {
        useOrdersStore.getState().updateMultipleOrders(orders);
        console.log(`orders ${orders.map((order) => order.reference)} updated!`);
        Toast.show({
            type: "info",
            text1: "Orders Updated.",
            text2: `Orders ${orders.map((order) => order.reference)} updated!`,
        });
    };

    // Store event handler
    const onStoreUpdated = async (_info: SocketInfo, data: Partial<Store>) => {
        try {
            const newNotifications = JSON.parse((await AsyncStorage.getItem("notifications")) || "[]");

            useStoreStore.getState().setStore(data);

            Toast.show({
                type: "info",
                text1: "Store Updated",
                text2: "Store information has been updated",
            });

            newNotifications.push({
                title: "Store Updated",
                message: "Store information has been updated",
                date: new Date().toLocaleString("en-US", {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                    hour: "numeric",
                    minute: "numeric",
                }),
                tag: "Store Update",
            });

            await AsyncStorage.setItem("notifications", JSON.stringify(newNotifications));
        } catch (error) {
            console.error("Error handling store update:", error);
        }
    };

    useEffect(() => {
        // Setup order and store event listeners only
        socketManager.onOrderCreated(onOrderCreated);
        socketManager.onOrderUpdated(onOrderUpdated);
        socketManager.onOrderDeleted(onOrderDeleted);
        socketManager.onOrderRestored(onOrderRestored);
        socketManager.onOrdersUpdated(onOrdersUpdated);
        socketManager.onStoreUpdated(onStoreUpdated);

        // Cleanup on unmount
        return () => {
            socketManager.off("orderCreated");
            socketManager.off("orderUpdated");
            socketManager.off("orderDeleted");
            socketManager.off("orderRestored");
            socketManager.off("ordersUpdated");
            socketManager.off("storeUpdated");
        };
    }, []);
};