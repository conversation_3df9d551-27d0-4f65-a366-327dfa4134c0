export type ApiStatsProps = {
    stats: {
        date: string;
        adStats: Record<
            string,
            Record<
                string,
                {
                    rejectedValue: number;
                    rejectedCount: number;
                    pendingValue: number;
                    pendingCount: number;
                    confirmedValue: number;
                    confirmedCount: number;
                    deliveredValue: number;
                    deliveredCount: number;
                    deliveredProfit: number;
                    returnedValue: number;
                    returnedCount: number;
                    returnedCost: number;
                    inTransitValue: number;
                    inTransitCount: number;
                    deletedValue: number;
                    deletedCount: number;
                    totalValue: number;
                    totalCount: number;
                }
            >
        >;
        deliveryStats: Record<
            string,
            {
                deliveredOrders: number;
                deliveredOrdersValue: number;
                deliveredOrdersPayment: number;
                deliveredOrdersProfit: number;
                returnedOrders: number;
                returnedOrdersCost: number;
                depositOrders: number;
                depositOrdersValue: number;
                depositOrdersPayment: number;
                depositOrdersProfit: number;
                inTransitOrders: number;
                inTransitOrdersValue: number;
                inTransitOrdersPayment: number;
                inTransitOrdersProfit: number;
                unverifiedOrders: number;
                avgDeliveryTime: number;
            }
        >;
        adminStats: Record<
            string,
            {
                confirmedOrders: number;
                rejectedOrders: number;
                createdOrders: number;
                deletedOrders: number;
                receivedOrders: number;
                packedOrders: number;
                attempts: number;
                firstAttempts: number;
                avgConfirmationTime: number;
                avgTimeToFirstAttempt: number;
                rejectionReasons: Record<string, number>;
            }
        >;
        productStats: Record<
            string,
            {
                deposit: number;
                depositValue: number;
                depositProfit: number;
                transit: number;
                transitValue: number;
                transitProfit: number;
                delivered: number;
                deliveredValue: number;
                deliveredProfit: number;
                returned: number;
                returnedCost: number;
                unverified: number;
                total: number;
                totalValue: number;
                totalProfit: number;
            }
        >;
    }[];
    campaigns: Record<
        string,
        {
            name: string;
            adsets: Record<
                string,
                {
                    name: string;
                    ads: Record<
                        string,
                        {
                            id: string;
                            name: string;
                            spend: number;
                        }
                    >;
                }
            >;
        }
    >;
};

export type FormatedProductProps = Record<
    string,
    Record<
        string,
        {
            deposit: number;
            depositValue: number;
            depositProfit: number;
            transit: number;
            transitValue: number;
            transitProfit: number;
            delivered: number;
            deliveredValue: number;
            deliveredProfit: number;
            returned: number;
            returnedCost: number;
            unverified: number;
            total: number;
            totalValue: number;
            totalProfit: number;
        }
    >
>;

export type DeliveryCompanyChartDataProps = {
    deliveredOrders: number;
    deliveredOrdersValue: number;
    deliveredOrdersPayment: number;
    deliveredOrdersProfit: number;
    returnedOrders: number;
    returnedOrdersCost: number;
    depositOrders: number;
    depositOrdersValue: number;
    depositOrdersPayment: number;
    depositOrdersProfit: number;
    inTransitOrders: number;
    inTransitOrdersValue: number;
    inTransitOrdersPayment: number;
    inTransitOrdersProfit: number;
    unverifiedOrders: number;
    avgDeliveryTime: number;
    date: string;
}[];
export type ProductChartDataProps = {
    deposit: number;
    depositValue: number;
    depositProfit: number;
    transit: number;
    transitValue: number;
    transitProfit: number;
    delivered: number;
    deliveredValue: number;
    deliveredProfit: number;
    returned: number;
    returnedCost: number;
    unverified: number;
    total: number;
    totalValue: number;
    totalProfit: number;
    date: string;
}[];
export type AdminChartDataProps = {
    confirmedOrders: number;
    rejectedOrders: number;
    createdOrders: number;
    deletedOrders: number;
    receivedOrders: number;
    packedOrders: number;
    attempts: number;
    firstAttempts: number;
    avgConfirmationTime: number;
    avgTimeToFirstAttempt: number;
    rejectionReasons: Record<string, number>;
    date: string;
}[];

export type DeliveryCompanyEntries = Record<
    string,
    {
        deliveredOrders: number;
        deliveredOrdersValue: number;
        deliveredOrdersPayment: number;
        deliveredOrdersProfit: number;
        returnedOrders: number;
        returnedOrdersCost: number;
        depositOrders: number;
        depositOrdersValue: number;
        depositOrdersPayment: number;
        depositOrdersProfit: number;
        inTransitOrders: number;
        inTransitOrdersValue: number;
        inTransitOrdersPayment: number;
        inTransitOrdersProfit: number;
        unverifiedOrders: number;
        avgDeliveryTime: number;
    }
>;

export type FormatedDeliveryProps = Record<string, DeliveryCompanyEntries>;

export type FormatedAdminsProps = Record<
    string,
    Record<
        string,
        {
            confirmedOrders: number;
            rejectedOrders: number;
            createdOrders: number;
            deletedOrders: number;
            receivedOrders: number;
            packedOrders: number;
            attempts: number;
            firstAttempts: number;
            avgConfirmationTime: number;
            avgTimeToFirstAttempt: number;
            rejectionReasons: Record<string, number>;
        }
    >
>;

export type FormatedStatsProps = {
    products: FormatedProductProps;
    delivery: FormatedDeliveryProps;
    admins: FormatedAdminsProps;
};

export type StatsActionsProps = {
    selectRange: (range: number) => void;
    setRange: (range: string[]) => void;
    updateStats: (range: { from: string; to: string }) => void;
    setLoading: (loading: boolean) => void;
    setRangeToLoadedRange: () => void;
    setLoadedRange: () => void;
};

export type RangeProps = string[];

export type StatsState = {
    stats: FormatedStatsProps;
    range: RangeProps;
    loadedRange: RangeProps;
    loading: boolean;
};

export type TeamStatsCard = {
    confirmedOrders: number;
    rejectedOrders: number;
    createdOrders: number;
    deletedOrders: number;
    receivedOrders: number;
    packedOrders: number;
    attempts: number;
    firstAttempts: number;
    avgConfirmationTime: number;
    avgTimeToFirstAttempt: number;
    rejectionReasons: Record<string, number>;
};

export type TeamStatsResult = {
    cards: TeamStatsCard;
    charts: TeamStatsCard[];
};
