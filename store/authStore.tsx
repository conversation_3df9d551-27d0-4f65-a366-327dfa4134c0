import api from "@api/api";
import { create } from "zustand";
import { socketManager } from "../socket/socket";
import Toast from "react-native-toast-message";
import * as SecureStore from "expo-secure-store";
import { getDevicePushTokenAsync } from "expo-notifications";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useOrderActionsStore, useOrdersStore } from "./orders";
import { useStoreStore } from "./storeStore";

type State = {
    auth: "multistore" | "auth" | "unauth" | "pending";
    cookie: string;
};

type Action = {
    login: (
        auth: State["auth"],
        {
            username,
            password,
            deviceToken,
            deviceType,
        }: {
            username: string;
            password: string;
            deviceToken: string;
            deviceType: string;
        }
    ) => Promise<State["auth"]>;
    setCookie: (cookie: string) => void;
    logout: () => void;
    resetAppState: () => void;
    checkSession: () => void;
    updateProfile: (user: any) => void;
    updatePassword: (passwords: any) => void;
    goToLogin: () => void;
    setAuth: (auth: State["auth"]) => void;
};

export const authenticate = async (email: string, password: string, deviceToken?: string, deviceType?: string) => {
    try {
        console.log("authenticating with v2 API");

        // Use v2 API endpoint directly with fetch
        const response = await fetch("https://partner.converty.shop/api/v2/auth/login", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                username: email,
                password: password,
                deviceToken: deviceToken || "testToken",
                deviceType: deviceType || "ios",
            }),
            credentials: "include",
        });

        const result = await response.json();
        console.log("Authentication response:", { success: result.success, hasStores: !!result.data?.stores });

        if (result.success) {
            console.log("successful response");

            // Save cookie from response headers
            await saveCookie(response);

            // Store user image if available
            if (result.data.image?.sm) {
                try {
                    await AsyncStorage.setItem("user-image", result.data.image.sm);
                } catch (error) {
                    console.error("Error storing user image:", error);
                }
            }

            // Connect socket
            (socketManager as any).socket.connect();

            // Handle multistore case
            if (result.data?.stores?.length > 1) {
                console.log("is multistore");
                useStoreStore.getState().setStores(result.data.stores);
                useStoreStore.getState().setShowStoreSelector(true);
                return "multistore";
            }

            return "auth";
        } else {
            console.log("Authentication failed:", result.message);
            Toast.show({
                type: "error",
                text1: "Login Failed",
                text2: result.message || "Invalid credentials"
            });
            return "unauth";
        }
    } catch (error) {
        console.error("Authentication error:", error);
        Toast.show({
            type: "error",
            text1: "Network Error",
            text2: "Unable to connect to server. Please try again."
        });
        return "unauth";
    }
};

const unauthenticate = async () => {
    try {
        const cookie = await loadCookie();
        const token = await getDevicePushTokenAsync();
        if (cookie) {
            api.post("/auth/logout", {
                deviceToken: token.data,
            });
        }
        deleteCookie();
        (socketManager as any).socket.disconnect();
        return "unauth";
    } catch (error) {
        return "unauth";
    }
};

const saveCookie = async (response: Response) => {
    try {
        const cookieValue = response.headers.get("set-cookie");

        if (!cookieValue) {
            throw new Error("Got a response but no cookie was there!");
        }
        const cookie = "token=" + cookieValue.split("=")[1].split(";")[0];
        await SecureStore.setItemAsync("auth-cookie", cookie);
        useAuthStore.getState().setCookie(cookieValue);

        return cookie;
    } catch (error) {
        console.error(error);

        return;
    }
};

export const loadCookie = async () => {
    try {
        const cookie = await SecureStore.getItemAsync("auth-cookie");
        console.log("loadCookie => ", cookie);
        return cookie;
    } catch (error) {
        console.error(error);

        return;
    }
};

const deleteCookie = async () => {
    try {
        SecureStore.deleteItemAsync("auth-cookie");
        useAuthStore.getState().setCookie("");
    } catch (error) {
        console.error(error);
        return;
    }
};

// Fast cookie validation using the original efficient method
const checkValidCookie = async () => {
    try {
        // Use the faster /store endpoint like the original implementation
        await api.get("/store");
        return true;
    } catch (error) {
        console.error("Cookie validation failed:", error);
        return false;
    }
};

// const iosRefresh = async (cookie: string) => {
//     if (Platform.OS === "ios") {
//         try {
//             const res = await fetch(URL + "/api/v1/auth/refresh", {
//                 method: "GET",
//                 credentials: "include",
//                 headers: [["cookie", cookie]],
//             });
//             console.log({ res });
//         } catch (error) {
//             console.error(error);
//         }
//     }
// };

export const useAuthStore = create<State & Action>((set) => ({
    auth: "pending", // Start with pending to allow checkSession to run
    cookie: "",
    setCookie: (cookie) => {
        set(() => {
            return { cookie };
        });
    },
    setAuth: (auth: State["auth"]) => {
        set(() => {
            return { auth };
        });
    },
    goToLogin: () => {
        set(() => {
            return { auth: "unauth" };
        });
    },
    login: async (_auth, { username, password, deviceToken, deviceType }) => {
        set(() => ({ auth: "pending" }));

        let res: "unauth" | "auth" | "multistore" = "unauth";

        try {
            res = await authenticate(username, password, deviceToken, deviceType);
        } catch (error) {
            console.error(error);
        }

        set(() => ({ auth: res }));
        return res;
    },

    logout: async () => {
        set(() => ({ auth: "pending" }));

        // Reset all app state
        useAuthStore.getState().resetAppState();

        await unauthenticate();

        return set(() => ({ auth: "unauth" }));
    },

    checkSession: async () => {
        console.log("checking session");
        set(() => ({ auth: "pending" }));

        let cookie = await loadCookie();

        if (cookie) {
            let valid = await checkValidCookie();

            if (valid) {
                // Connect socket for valid session
                (socketManager as any).socket.connect();
                set(() => ({ auth: "auth" }));
                return;
            }

            deleteCookie();
            //toast expired session
            Toast.show({
                type: "info",
                text1: "Session Expired",
                text2: "Please login again",
            });
        }
        set(() => ({ auth: "unauth" }));
    },

    updateProfile: async (user: any) => {
        set(() => ({ auth: "pending" }));
        try {
            const { _id, password, createdAt, updatedAt, __v, ...rest } = user;
            const data = await api.patch("/user", { ...rest });
            return data.data;
        } catch (error) {
            console.error(error);
        } finally {
            set(() => ({ auth: "auth" }));
        }
    },

    updatePassword: async (passwords) => {
        set(() => ({
            auth: "pending",
        }));
        const { currentPassword, newPassword } = passwords;
        try {
            const data = await api.patch("/user/password", {
                password: currentPassword,
                newPassword,
            });
            set(() => ({ auth: "auth" }));
            return data.data;
        } catch (error) {
            console.error(error);
            set(() => ({ auth: "auth" }));
        }
    },
    resetAppState: () => {
        // Reset orders state using the individual stores directly
      
        useOrdersStore.getState().setOrders([]);
        useOrderActionsStore.getState().setCurrentOrder({});

        // Reset store state
        useStoreStore.getState().setUser(undefined);
        useStoreStore.getState().setStore({ _id: "" });
        useStoreStore.getState().setStores([]);

        // Clear local storage
        AsyncStorage.removeItem("store-logo");
        AsyncStorage.removeItem("user-image");
    },
}));
