import { create } from "zustand";
import { router } from "expo-router";
import { Order, ProductsList } from "@components/orders/types";
import { OrderService, OrderCreatePayload, OrderUpdatePayload } from "../../services/orderService";
import { ToastHandler } from "../../utils/ToastHandler";
import { useLoaderStore } from "../loaderStore";

// Types
interface OrderActionsState {
    currentOrder: Partial<Order>;
    products: ProductsList;
    scanLoading: boolean;
    scanError?: string | null;
}

interface OrderActionsActions {
    // Current order management
    setCurrentOrder: (order: Partial<Order>) => void;
    clearCurrentOrder: () => void;
    
    // API operations
    scanOrder: (orderID: string) => Promise<void>;
    updateOrder: (updatedOrder: OrderUpdatePayload) => Promise<void>;
    createOrder: (order: OrderCreatePayload) => Promise<Order | null>;
    deleteOrder: (orderId: string) => Promise<string>;
    restoreOrder: (orderId: string) => Promise<string>;
    
    // Products
    getProducts: () => Promise<void>;
    setProducts: (products: ProductsList) => void;
}

type OrderActionsStore = OrderActionsState & OrderActionsActions;

export const useOrderActionsStore = create<OrderActionsStore>((set, get) => ({
    // Initial state
    currentOrder: {},
    products: [],
    scanLoading: false,
    scanError: null,

    // Current order management
    setCurrentOrder: (order) => {
        set({ currentOrder: order });
    },

    clearCurrentOrder: () => {
        set({ currentOrder: {} });
    },

    // Scan order by ID
    scanOrder: async (orderID) => {
        set({ scanLoading: true, scanError: null });
        
        try {
            const response = await OrderService.scanOrder(orderID);
            
            if (response.success) {
                set({
                    currentOrder: response.data,
                    scanLoading: false,
                });
                
                ToastHandler.showOrderSuccess(
                    "Scanned",
                    response.data.reference
                );
            } else {
                set({
                    scanError: response.message,
                    scanLoading: false,
                });
                
                ToastHandler.showErrorToast(
                    "Scan Failed",
                    response.message || "Unable to scan order"
                );
            }
        } catch (error) {
            set({
                scanError: error.message,
                scanLoading: false,
            });
            
            ToastHandler.handleOrderError(error, "Scan");
        }
    },

    // Update existing order
    updateOrder: async (updatedOrder) => {
        const { setLoading } = useLoaderStore.getState();
        
        try {
            setLoading(true);
            const response = await OrderService.updateOrder(updatedOrder);

            if (response.success) {
                const updatedOrderData: Order = response.data;
                
                set({ currentOrder: updatedOrderData });
                
                ToastHandler.showOrderSuccess(
                    "Updated",
                    updatedOrderData.reference
                );
                
                router.back();
            }
        } catch (error) {
            ToastHandler.handleOrderError(error, "Update");
        } finally {
            setLoading(false);
        }
    },

    // Create new order
    createOrder: async (order) => {
        const { setLoading } = useLoaderStore.getState();
        
        try {
            setLoading(true);
            const response = await OrderService.createOrder(order);
            
            if (response.success) {
                return response.data;
            } else {
                ToastHandler.showErrorToast(
                    "Order Creation Failed",
                    response.message || "Unable to create order"
                );
                return null;
            }
        } catch (error) {
            ToastHandler.handleOrderError(error, "Create");
            return null;
        } finally {
            setLoading(false);
        }
    },

    // Delete order
    deleteOrder: async (orderId) => {
        try {
            const response = await OrderService.deleteOrder(orderId);
            
            if (response.success) {
                return orderId;
            } else {
                throw new Error(response.message || "Failed to delete order");
            }
        } catch (error) {
            ToastHandler.handleOrderError(error, "Delete");
            throw error;
        }
    },

    // Restore order
    restoreOrder: async (orderId) => {
        try {
            const response = await OrderService.restoreOrder(orderId);
            
            if (response.success) {
                return orderId;
            } else {
                throw new Error(response.message || "Failed to restore order");
            }
        } catch (error) {
            ToastHandler.handleOrderError(error, "Restore");
            throw error;
        }
    },

    // Get products list
    getProducts: async () => {
        try {
            const response = await OrderService.getProducts();
            
            if (response.success) {
                set({ products: response.data });
            } else {
                ToastHandler.showErrorToast(
                    "Products Error",
                    response.message || "Failed to load products"
                );
                router.navigate("/network-error");
            }
        } catch (error) {
            ToastHandler.handleApiError(
                error,
                "Products Error",
                {
                    showToast: true,
                    navigateToError: true,
                    customMessage: "Failed to load products"
                }
            );
        }
    },

    // Set products
    setProducts: (products) => {
        set({ products });
    },
}));
