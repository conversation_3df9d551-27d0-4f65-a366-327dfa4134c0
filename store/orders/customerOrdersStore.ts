import { create } from "zustand";
import { Order } from "@components/orders/types";
import { OrderService } from "../../services/orderService";
import { ToastHandler } from "../../utils/ToastHandler";
import { OrderUtils } from "../../utils/orderUtils";

// Types
interface CustomerOrdersState {
    customerOrders: Order[];
    loading: boolean;
    error?: string | null;
}

interface CustomerOrdersActions {
    // State management
    setCustomerOrders: (orders: Order[]) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    clearCustomerOrders: () => void;
    
    // API operations
    fetchCustomerOrdersByOrderId: (orderId: string) => Promise<Order[]>;
    blockCustomer: (orderId: string) => Promise<void>;
    deleteCustomerOrders: (orderId: string) => Promise<void>;
    
    // Order status updates
    updateCustomerOrderStatus: (orderId: string, status: string) => void;

    // **🔥 RESTORED: Socket event handlers for customer orders list**
    handleCustomerOrderDeleted: (deletedOrderId: string) => void;
    handleCustomerOrderRestored: (restoredOrder: Order) => void;
    handleCustomerOrdersDeleted: (deletedOrderIds: string[]) => void;
}

type CustomerOrdersStore = CustomerOrdersState & CustomerOrdersActions;

export const useCustomerOrdersStore = create<CustomerOrdersStore>((set, get) => ({
    // Initial state
    customerOrders: [],
    loading: false,
    error: null,

    // State management
    setCustomerOrders: (orders) => {
        set({ customerOrders: orders });
    },

    setLoading: (loading) => {
        set({ loading });
    },

    setError: (error) => {
        set({ error });
    },

    clearCustomerOrders: () => {
        set({ customerOrders: [], error: null });
    },

    // Fetch customer orders by order ID
    fetchCustomerOrdersByOrderId: async (orderId) => {
        set({ loading: true, error: null });
        
        try {
            const response = await OrderService.fetchCustomerOrdersByOrderId(orderId);
            
            if (response.success) {
                const customerOrders = response.data;
                set({ customerOrders, loading: false });
                
                console.log(
                    "Customer orders status =>",
                    customerOrders.map((order: Order) => order.status)
                );
                
                return customerOrders;
            } else {
                set({ 
                    error: "Failed to fetch customer orders", 
                    loading: false 
                });
                
                ToastHandler.showErrorToast(
                    "Failed to fetch customer orders",
                    response.message || "Unknown error occurred"
                );
                
                return [];
            }
        } catch (error) {
            console.error("Error fetching customer orders:", error);
            
            set({
                error: error.message,
                loading: false,
            });
            
            ToastHandler.showErrorToast(
                "Failed to fetch customer orders",
                error.message || "Network error occurred"
            );
            
            return [];
        }
    },

    // Block customer by order ID
    blockCustomer: async (orderId) => {
        try {
            const response = await OrderService.blockCustomer(orderId);
            
            if (response.success) {
                ToastHandler.showSuccessToast(
                    "Success",
                    response.message || "Customer blocked successfully"
                );
            }
        } catch (error) {
            ToastHandler.handleApiError(
                error,
                "Block Customer",
                {
                    customMessage: "Failed to block customer"
                }
            );
            throw error;
        }
    },

    // Delete all customer orders
    deleteCustomerOrders: async (orderId) => {
        try {
            const response = await OrderService.deleteCustomerOrders(orderId);
            
            if (response.success) {
                ToastHandler.showSuccessToast(
                    "Success",
                    response.message || "All orders from this customer have been deleted"
                );
            }
        } catch (error) {
            ToastHandler.handleApiError(
                error,
                "Delete Customer Orders",
                {
                    customMessage: "Failed to delete customer orders"
                }
            );
            throw error;
        }
    },

    // Update customer order status
    updateCustomerOrderStatus: (orderId, status) => {
        const { customerOrders } = get();
        
        const { orders: updatedOrders, wasUpdated } = OrderUtils.updateOrderStatus(
            customerOrders,
            orderId,
            status
        );

        if (wasUpdated) {
            set({ customerOrders: updatedOrders });
            console.log(`Customer order ${orderId} status updated to ${status}`);
        }
    },

    // **🔥 RESTORED: Handle customer order deletion from socket**
    handleCustomerOrderDeleted: (deletedOrderId) => {
        console.log(`Socket: Customer order ${deletedOrderId} deleted`);

        const { customerOrders } = get();
        const updatedOrders = customerOrders.filter(order => order._id !== deletedOrderId);

        if (updatedOrders.length !== customerOrders.length) {
            set({ customerOrders: updatedOrders });
            console.log(`Customer order ${deletedOrderId} removed from customer orders list`);
        } else {
            console.log(`Customer order ${deletedOrderId} not found in customer orders list`);
        }
    },

    // **🔥 RESTORED: Handle customer order restoration from socket**
    handleCustomerOrderRestored: (restoredOrder) => {
        console.log(`Socket: Customer order ${restoredOrder.reference} restored`);

        const { customerOrders } = get();
        const existingOrderIndex = customerOrders.findIndex(order => order._id === restoredOrder._id);

        if (existingOrderIndex !== -1) {
            // Update existing order
            const updatedOrders = [...customerOrders];
            updatedOrders[existingOrderIndex] = restoredOrder;
            set({ customerOrders: updatedOrders });
            console.log(`Updated existing customer order ${restoredOrder.reference} in customer orders list`);
        } else {
            // Add new order to the list
            set({ customerOrders: [...customerOrders, restoredOrder] });
            console.log(`Added restored customer order ${restoredOrder.reference} to customer orders list`);
        }
    },

    // **🔥 RESTORED: Handle multiple customer orders deletion from socket**
    handleCustomerOrdersDeleted: (deletedOrderIds) => {
        console.log(`Socket: Customer orders deleted`, deletedOrderIds);

        const { customerOrders } = get();
        const updatedOrders = customerOrders.filter(order => !deletedOrderIds.includes(order._id));

        if (updatedOrders.length !== customerOrders.length) {
            set({ customerOrders: updatedOrders });
            const deletedCount = customerOrders.length - updatedOrders.length;
            console.log(`${deletedCount} customer orders removed from customer orders list`);
        } else {
            console.log(`No customer orders found in customer orders list for deletion`);
        }
    },
}));
