/**
 * Unified Order Store
 * 
 * This file provides a clean API that combines all order-related stores
 * while maintaining separation of concerns internally.
 * 
 * Usage:
 * import { useOrderStore } from '@store/orders';
 * 
 * const { orders, addOrder, updateOrder, deleteOrder } = useOrderStore();
 */

import { useOrdersStore } from './ordersStore';
import { useOrderActionsStore } from './orderActionsStore';
import { useCustomerOrdersStore } from './customerOrdersStore';

// Re-export individual stores for specific use cases
export { useOrdersStore } from './ordersStore';
export { useOrderActionsStore } from './orderActionsStore';
export { useCustomerOrdersStore } from './customerOrdersStore';

// Unified hook that combines all order functionality
export const useOrderStore = () => {
    // Get store instances
    const ordersStore = useOrdersStore();
    const orderActionsStore = useOrderActionsStore();
    const customerOrdersStore = useCustomerOrdersStore();

    // Combined loading state
    const loading = ordersStore.loading || orderActionsStore.scanLoading || customerOrdersStore.loading;

    // Combined error state
    const error = ordersStore.error || orderActionsStore.scanError || customerOrdersStore.error;

    // Legacy compatibility - set loading state
    const setLoading = (_loading: boolean) => {
        // This is handled by individual stores now
        console.warn('setLoading is deprecated. Loading state is managed automatically.');
    };

    return {
        // State
        orders: ordersStore.orders,
        order: orderActionsStore.currentOrder,
        products: orderActionsStore.products,
        customerOrders: customerOrdersStore.customerOrders,
        loading,
        error,

        // Orders list operations
        setOrders: ordersStore.setOrders,
        addOrderToList: ordersStore.addOrderToList,
        findOrderAndUpdate: ordersStore.updateOrderInList,
        findOrderAndDelete: ordersStore.removeOrderFromList,
        pushToOrders: ordersStore.pushToOrders,
        updateOrders: ordersStore.updateMultipleOrders,
        restoreOrderFromSocket: ordersStore.handleOrderRestored,

        // Order operations
        setOrder: orderActionsStore.setCurrentOrder,
        clearCurrentOrder: orderActionsStore.clearCurrentOrder,
        scanOder: orderActionsStore.scanOrder, // Keep typo for backward compatibility
        updateOrder: orderActionsStore.updateOrder,
        addOrder: orderActionsStore.createOrder,
        deleteOrder: orderActionsStore.deleteOrder,
        restoreOrder: orderActionsStore.restoreOrder,

        // Products
        getProducts: orderActionsStore.getProducts,
        setProducts: orderActionsStore.setProducts,

        // Customer orders
        setCustomerOrders: customerOrdersStore.setCustomerOrders,
        clearCustomerOrders: customerOrdersStore.clearCustomerOrders,
        fetchCustomerOrdersByOrderId: customerOrdersStore.fetchCustomerOrdersByOrderId,
        blockCustomer: customerOrdersStore.blockCustomer,
        deleteCustomerOrders: customerOrdersStore.deleteCustomerOrders,
        updateCustomerOrderStatus: customerOrdersStore.updateCustomerOrderStatus,

        // Legacy compatibility
        setLoading,

        // Auth state (for backward compatibility)
        auth: "loaded" as const,
    };
};

// Default export for backward compatibility
export default useOrderStore;
