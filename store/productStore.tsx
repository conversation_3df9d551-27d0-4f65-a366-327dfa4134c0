import { create } from "zustand";
import { Product, ProductStatistics } from "../types/Product";
import { produce } from "immer";
import api from "@api/api";
import { router } from "expo-router";

type ProductStoreType = {
    status: "failed" | "loading" | "success" | "unloaded";
    products: Product[];
    productStatistics: ProductStatistics;
    selectedProduct: Product | undefined;
    error?: string;
    setProductStatistics: (data: ProductStatistics) => void;
    setSelectedProduct: (product: Product) => void;
    setProducts: (products: Product[]) => void;
    updateProductsList: (products: Product[]) => void;
    getProducts: () => void;
    setStatus: (status: "failed" | "loading" | "success" | "unloaded") => void;
};

export const useProductStore = create<ProductStoreType>((set) => ({
    status: "unloaded",
    products: [],
    productStatistics: null,
    selectedProduct: undefined,
    setStatus: (status) =>
        set(
            produce((state) => {
                state.status = status;
            })
        ),
    setProductStatistics: (data: ProductStatistics) => {
        set(
            produce((state) => {
                state.productStatistics = data;
            })
        );
    },
    setProducts: (products) => {
        set(
            produce((state) => {
                state.status = "loading";
            })
        );
        set(
            produce((state) => {
                state.products = products;
                state.status = "success";
            })
        );
    },
    updateProductsList: (newProducts) => {
        set(
            produce((state) => {
                state.status = "loading";
            })
        );
        set(
            produce((state) => {
                state.products.push(...newProducts);
                state.status = "success";
            })
        );
    },
    getProducts: async () => {
        set(
            produce((state) => {
                state.status = "loading";
            })
        );
        try {
            const response = await api.get("/product/list");

            if (response.success) {
                set(
                    produce((state) => {
                        state.products = response.data;
                        state.status = "success";
                    })
                );
            } else {
                set(
                    produce((state) => {
                        state.status = "failed";
                        state.error = response.message;
                    })
                );
                router.navigate("/network-error");
            }
        } catch (error) {
            console.error(error);
            set(
                produce((state) => {
                    state.status = "failed";
                    state.error = error.message;
                })
            );
            router.navigate("/network-error");
        }
    },
    setSelectedProduct: (product) => {
        set(
            produce((state) => {
                state.selectedProduct = product;
            })
        );
    },
}));
