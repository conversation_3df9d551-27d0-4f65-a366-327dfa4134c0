import api from "@api/api";
import { create } from "zustand";
import {
    ApiStatsProps,
    FormatedAdminsProps,
    FormatedDeliveryProps,
    FormatedProductProps,
    FormatedStatsProps,
    StatsActionsProps,
    StatsState,
} from "./statsStoreProps";
import { getPeriod, fetchStats } from "../utils/statsUtils";

export const useStatsStore = create<StatsState & StatsActionsProps>((set) => ({
    stats: { products: { store: {} }, delivery: { store: {} }, admins: { store: {} } },
    loading: true,
    range: [],
    loadedRange: [],

    setRange: (range: string[]) => {
        set((state) => ({ ...state, range }));
    },

    setLoadedRange: () => {
        set((state) => {
            return { loadedRange: Object.keys(state.stats.delivery["store"]) };
        });
    },

    setRangeToLoadedRange: () => {
        set((state) => {
            return { ...state, range: [...state.loadedRange] };
        });
    },

    selectRange: async (number: number) => {
        // (async () => {
        //     let temp: StatsState & StatsActionsProps;
        //     let keys: string[];
        //     set((state) => {
        //         temp = state;
        //         keys = state.range;
        //         return { ...state, loading: true };
        //     });

        //     if (number > keys.length) {
        //         await temp.updateStats(getPeriod(number));
        //         temp.setRangeToLoadedRange(number);
        //     } else {
        //         const range = keys.slice(keys.length - number, keys.length - 1);
        //         set((state) => {
        //             return { range };
        //         });
        //     }

        //     set((state) => {
        //         return { loading: false };
        //     });
        // })();
        set((state) => {
            state.loading = true;
            state.setLoadedRange();
            let loadedRange = state.loadedRange;
            if (number < loadedRange.length) {
                setTimeout(() => {
                    let newRange: string[];
                    newRange = loadedRange.slice(-number, undefined);
                    state.setRange(newRange);
                    state.setLoading(false);
                }, 1000);
                return state;
            }
            if (number === loadedRange.length) {
                setTimeout(() => {
                    state.setRangeToLoadedRange();
                    state.setLoading(false);
                }, 1000);
                return state;
            }
            // i need to controle to see if the range is the maximum possible that day
            //ndaymaximum =  first date entry - current day
            if (number > loadedRange.length) {
                const handleUpdateRange = async () => {
                    await state.updateStats(getPeriod(number));
                    state.setLoadedRange();
                    state.setRangeToLoadedRange();
                    state.setLoading(false);
                };
                handleUpdateRange();
                return state;
            }
        });
    },

    updateStats: async ({ from, to }) => {
        set((state) => ({ ...state, loading: true }));
        let newStats: FormatedStatsProps = await fetchStats({
            from,
            to,
        });

        set((state) => {
            return { ...state, range: Object.keys(state.stats.delivery["store"]) };
        });
        set((state) => {
            for (const categorykey in newStats) {
                const category: FormatedProductProps | FormatedDeliveryProps | FormatedAdminsProps =
                    newStats[categorykey];
                for (const name in category) {
                    const elementStats = category[name];
                    state.stats[categorykey][name] = { ...elementStats };
                }
            }
            return state;
        });

        return set((state) => {
            return { ...state, loading: false };
        });
    },

    setLoading: (loading: boolean) => set((state) => ({ ...state, loading: loading })),
}));
