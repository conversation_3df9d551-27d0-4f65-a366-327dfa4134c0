import { create } from "zustand";
import api from "@api/api";
import { CartItem } from "@components/orders/types";
import toast from "react-native-toast-message";
import { router } from "expo-router";

export type UpsellStateProps = {
    upsellItems: UpsellTypeProp[];
    loading: boolean;
};

export type UpsellTypeProp = {
    _id: string;
    name: string;
    status?: string;
    products: CartItem[];
    offer: {
        product: string;
        quantity: number;
        price: number;
    }[];
    priority?: number;
    type?: string;
    html?: string;
    store: string;
    isDeleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
};

export type ApiActionProps = {
    fetchUpsellItems: () => void;
    updateUpsell: (upsell: any) => void;
};

const determineType = (upsell) => {
    if (
        upsell.products.length === 1 &&
        upsell.offer.length === 1 &&
        upsell.products[0]._id === upsell.offer[0].product
    ) {
        return "upsell";
    } else {
        return "cross-sell";
    }
};

const useUpsellStore = create<UpsellStateProps & ApiActionProps>((set) => ({
    upsellItems: [],
    loading: false,

    fetchUpsellItems: async () => {
        set({ loading: true });
        try {
            const response = await api.get("/upsell");
            set({ upsellItems: response.data });
        } catch (error) {
            console.error(error);
            router.navigate("/network-error");
        } finally {
            set({ loading: false });
        }
    },

    updateUpsell: async (upsell: any) => {
        set({ loading: true });
        try {
            const { _id, store, products, createdAt, updatedAt, __v, ...newUpsell } = upsell;
            const response = await api.patch("/upsell/" + _id, {
                ...newUpsell,
                products: products?.map((product) => product._id),
                type: determineType(upsell),
            });
            if (response.success) {
                toast.show({
                    type: "info",
                    text1: "Upsell/Cross-Sell has been updated",
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            set({ loading: false });
        }
    },
}));

export default useUpsellStore;
