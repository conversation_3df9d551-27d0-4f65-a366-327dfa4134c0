import api from "@api/api";
import { create } from "zustand";
import { produce } from "immer";
import { router } from "expo-router";
import { Store, User } from "../types/Store";
import Toast from "react-native-toast-message";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useAuthStore } from "./authStore";
import * as Notifications from "expo-notifications";
import { isDeviceTokenRegistered, registerForPushNotificationsAsync } from "../utils/notificationsUtils";
import { socket, socketManager } from "../socket/socket";
import { useOrdersStore } from "./orders";

export type DzCities =
    | "Adrar"
    | "Chlef"
    | "Laghouat"
    | "Oum El Bouaghi"
    | "Batna"
    | "Bejaia"
    | "Biskra"
    | "Bechar"
    | "Blida"
    | "Bouira"
    | "Tamanrasset"
    | "Tebessa"
    | "Tlemcen"
    | "Tiaret"
    | "Tizi Ouzou"
    | "Alger"
    | "<PERSON>jelf<PERSON>"
    | "<PERSON><PERSON><PERSON>"
    | "<PERSON><PERSON>"
    | "<PERSON><PERSON>"
    | "<PERSON><PERSON><PERSON>"
    | "Sid<PERSON> Bel Abbes"
    | "Anna<PERSON>"
    | "<PERSON>uel<PERSON>"
    | "Constantine"
    | "Medea"
    | "Mostaganem"
    | "M'Sila"
    | "Mascara"
    | "Ouargla"
    | "Oran"
    | "El Bayadh"
    | "Illizi"
    | "Bordj Bou Arreridj"
    | "Boumerdes"
    | "El Tarf"
    | "Tindouf"
    | "Tissemsilt"
    | "El Oued"
    | "Khenchela"
    | "Souk Ahras"
    | "Tipaza"
    | "Mila"
    | "Ain Defla"
    | "Naama"
    | "Ain Temouchent"
    | "Ghardaia"
    | "Relizane"
    | "Timimoun"
    | "Bordj Badji Mokhtar"
    | "Ouled Djellal"
    | "Beni Abbes"
    | "In Salah"
    | "In Guezzam"
    | "Touggourt"
    | "Djanet"
    | "El M ghaier"
    | "El Meniaa";

type State = {
    auth: "loading" | "loaded" | "failed";
    storeLoading: boolean;
    store: Store;
    stores: Store[];
    showStoreSelector: boolean;
    user: Partial<User>;
    storeChanged: boolean; // Add this flag to track store changes
};

type ApiAction = {
    getStore: () => Promise<void>;
    setStore: (store: Partial<Store>) => void;
    getUser: () => Promise<boolean>;
    setStores: (stores: Store[]) => void;
    updateStore: (store: Partial<Store>) => void;
    setShowStoreSelector: (visible: boolean) => void;
    getRate: () => void;
    updateUser(user: Partial<User>): void;
    setUser: (user: User) => void;
    setStoreChanged: (changed: boolean) => void;
    fetchStores: () => Promise<void>;
    handleStoreSelect: (selectedStore: Store) => Promise<void>;
};

export const useStoreStore = create<State & ApiAction>((set, get) => ({
    auth: "loading",
    storeLoading: false,
    showStoreSelector: false,
    store: {
        _id: "",
    },
    stores: [],
    user: {
        // permissions: "all",
    },
    storeChanged: false,
    fetchStores: async () => {
        const { stores, user } = get();
        if (stores && stores.length > 0) return;

        set({ storeLoading: true });
        try {
            const res = await api.get("/store/list");
            if (res.success && res.data) {
                set(() => {
                    return { stores: res.data };
                });
            }
        } catch (error) {
            console.error("Failed to fetch stores:", JSON.stringify(error.message));
            Toast.show({
                type: "error",
                text1: "Error fetching stores",
                text2: "Please try again later",
            });
        } finally {
            set({ storeLoading: false });
        }
    },
    handleStoreSelect: async (selectedStore: Store) => {
        const { stores, user } = get();

        if (!selectedStore?._id) {
            Toast.show({
                type: "error",
                text1: "Error: No Store Selected",
                text2: "Please select a store",
            });
            return;
        }

        set({ storeLoading: true });

        try {
            const response = await api.patch(`/store/select-store/${selectedStore._id}`);
            if (response.success) {
                const storeResponse = await api.get("/store");
                if (storeResponse.success) {
                    
                    (socketManager as any).socket.disconnect();

                    // Reset orders list for the new store
                    useOrdersStore.getState().setOrders([]);

                    // Reconnect to the new store channel
                    
                    // setTimeout(() => {
                    (socketManager as any).socket.connect();
                    // }, 1000);

                    // Set the store changed flag
                    set({ storeChanged: true });

                    // Update store data
                    set({ store: storeResponse.data });

                    // Preserve existing permissions when setting user data
                    const currentPermissions = user?.permissions;
                    set({
                        user: {
                            ...storeResponse.user,
                            stores: stores,
                            permissions: currentPermissions || storeResponse.user.permissions,
                        },
                    });

                    console.log("storeResponse.user => ", storeResponse.user);
                    console.log("is this device token registered in database =>", await isDeviceTokenRegistered());

                    // Update AsyncStorage with new store logo
                    try {
                        // Remove old store logo first
                        await AsyncStorage.removeItem("store-logo");

                        // Set new store logo if available
                        if (storeResponse.data.logo?.sm) {
                            await AsyncStorage.setItem("store-logo", storeResponse.data.logo.sm);
                        }
                    } catch (error) {
                        console.error("Failed to update store logo in AsyncStorage:", error);
                    }

                    if (useAuthStore.getState().auth === "auth") {
                        return;
                    }
                    useAuthStore.getState().setAuth("auth");
                }
            }
        } catch (error) {
            console.error("Failed to select store:", error);
            Toast.show({
                type: "error",
                text1: "Error selecting store",
                text2: "Please try again",
            });
        } finally {
            useStoreStore.getState().setShowStoreSelector(false);
            set({ storeLoading: false });
        }
    },
    setStoreChanged: (changed: boolean) => {
        set({ storeChanged: changed });
    },
    setShowStoreSelector: (visible: boolean) => {
        set({ showStoreSelector: visible });
    },
    setUser: (user: User) => {
        set({ user });
    },
    setStores: (_stores: Store[]) => {
        set({ stores: _stores });
        //     produce((state) => {
        //         state.user.stores = _stores;
        //     })
        // );
    },
    getStore: async () => {
        try {
            set({ storeLoading: true });
            set(() => {
                return { auth: "loading" };
            });
            console.log("==> Fetching store");

            const response = await api.get("/store");
            console.log("response store getstore => ", response);
            if (response.success) {
                set(
                    produce((state) => {
                        state.auth = "loaded";
                        state.store = response.data;
                        state.user = response.user;
                        state.user.stores = response.user?.stores;
                        state.user.notificationTokens = response.user?.notificationTokens;
                    })
                );

                // Update AsyncStorage with current store logo
                try {
                    await AsyncStorage.removeItem("store-logo");
                    if (response.data.logo?.sm) {
                        await AsyncStorage.setItem("store-logo", response.data.logo.sm);
                    }
                } catch (error) {
                    console.error("Failed to update store logo in AsyncStorage:", error);
                }

                // Check if user has multiple stores and fetch them automatically
                const userStores = response.user?.stores;
                if (userStores && userStores.length > 0) {
                    console.log("==> User has multistore, fetching stores automatically");
                    // Call fetchStores to populate the stores list
                    get().fetchStores();
                }

                console.log("==> store user =>", useStoreStore.getState().store);
                console.log("is this device token registered in database =>", await isDeviceTokenRegistered());
                set({ storeLoading: false });
            }
            // * This will never be reached since if the success is false the api will throw an error
            // else {
            //     set(
            //         produce((state) => {
            //             state.auth = "failed";
            //         })
            //     );
            //     router.navigate("/network-error");
            // }
        } catch (error) {
            console.error(error);

            router.navigate({
                pathname: "/network-error",
                params: error,
            });
            set({ storeLoading: false });
            set(
                produce((state) => {
                    state.auth = "failed";
                })
            );
        }
    },
    getRate: async () => {
        try {
            await api.get("/payment/rates", {
                params: {
                    from: "TND",
                    to: "TND",
                    amount: 1,
                },
            });
        } catch (error) {
            console.error(error);
            router.navigate({
                pathname: "/network-error",
                params: error,
            });
        }
    },
    setStore: (store: Partial<Store>) => {
        set(({ store: _store }) => ({ store: { ..._store, ...store } }));
    },
    updateStore: (store: Partial<Store>) => {
        api.patch("/store", store)
            .then(({ data }: { data: Store }) => {
                console.log(data.integrations[0].fields.presets["preset 1"]);

                set(({ store: _store }) => {
                    return { store: { ..._store, ...store } };
                });
            })
            .catch((error) => {
                router.navigate({
                    pathname: "/network-error",
                    params: error,
                });
            });
    },
    updateUser: async (user) => {
        set(() => ({ auth: "loading" }));
        console.log("user => ", user);
        try {
            const data = await api.patch("/user", user);
            set(() => ({ user: { ...data?.data, permissions: user.permissions } }));
            // console.log("data => ", data.data);
            // console.log("user neww => ", useStoreStore.getState().user);
            return data.data;
        } catch (error) {
            console.error(error);
        } finally {
            set(() => ({ auth: "loaded" }));
        }
    },
    getUser: async () => {
        try {
            const res: { status: number; data: User } = await api.get("/user");

            console.log("USERRR res.data => ", res.data);
            set(() => {
                return { user: res.data };
            });
            return true;
        } catch (error) {
            console.error(error);

            return false;
        }
    },
}));
