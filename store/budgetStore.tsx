import { create } from "zustand";
import api from "@api/api";
import { produce } from "immer";
import { router } from "expo-router";

export type BudgetStateProps = {
    revenue: CashFlowProps;
    expense: CashFlowProps;
    initialBudget: number;
    activeTab: 0 | 1;
    allBudgets: Budget[];
    latestBudget: Budget;
    selectedBudget: Budget;
};

export type CashFlowProps = {
    categories: CategoryProps[];
    totalValue: number;
};

export type CategoryProps = {
    totalValue: number;
    name: string;
    subCategories: SubCategoryProps[];
};

export type SubCategoryProps = {
    name: string;
    createdAt: string;
    value: number;
    _id: string;
};

export type Budget = {
    balance: number;
    createdAt: string;
    description: string;
    expenses: Array<{
        name: string;
        subCategories: Array<any>;
        _id: string;
    }>;
    isDeleted: boolean;
    name: string;
    reference: number;
    revenues: Array<{
        name: string;
        subCategories: Array<any>;
        _id: string;
    }>;
    slug: string;
    startingBalance: number;
    store: string;
    updatedAt: string;
    __v: number;
    _id: string;
};

export type ActionProps = {
    setInitialBudget: (initialBudget: number) => void;
    updateTotalValue: (categoryName: string) => void;
    addCategory: (category: CategoryProps) => void;
    deleteCategory: (categoryName: string) => void;
    addSubCategory: (categoryName: string, subCategory: SubCategoryProps) => void;
    deleteSubCategory: (categoryName: string, subCategoryName: string) => void;
    setActiveTab: (tab: 0 | 1) => void;
    getLatestBudget: () => void;
    getAllBudgets: () => void;
    setSelectedBudget: (budget: Budget) => void;
};

export type ApiActionProps = {
    fetchBuget: () => Promise<void>;
    fetchBudgets: () => Promise<void>;
    addBudget: (budget: Budget) => Promise<void>;
    updateBudget: (budget: Budget, body: any) => Promise<void>;
};

const useBudgetStore = create<BudgetStateProps & ActionProps & ApiActionProps>((set) => ({
    revenue: { categories: [], totalValue: 0 },
    expense: { categories: [], totalValue: 0 },
    initialBudget: 0,
    activeTab: 0,
    allBudgets: [],
    latestBudget: {
        balance: 0,
        createdAt: "",
        description: "",
        expenses: [],
        isDeleted: false,
        name: "",
        reference: 0,
        revenues: [],
        slug: "",
        startingBalance: 0,
        store: "",
        updatedAt: "",
        __v: 0,
        _id: "",
    },
    selectedBudget: {
        balance: 0,
        createdAt: "",
        description: "",
        expenses: [],
        isDeleted: false,
        name: "",
        reference: 0,
        revenues: [],
        slug: "",
        startingBalance: 0,
        store: "",
        updatedAt: "",
        __v: 0,
        _id: "",
    },

    setInitialBudget: (initialBudget) =>
        set(
            produce((draftState) => {
                draftState.initialBudget = initialBudget;
            })
        ),

    updateTotalValue: (categoryName) =>
        set(
            produce((draftState) => {
                draftState.revenue.categories.forEach((cat) => {
                    if (cat.name === categoryName) {
                        cat.totalValue = cat.subCategories.reduce((total, sub) => total + sub.value, 0);
                    }
                });
                draftState.revenue.totalValue = draftState.revenue.categories.reduce(
                    (total, cat) => total + cat.totalValue,
                    0
                );

                draftState.expense.categories.forEach((cat) => {
                    if (cat.name === categoryName) {
                        cat.totalValue = cat.subCategories.reduce((total, sub) => total + sub.value, 0);
                    }
                });
                draftState.expense.totalValue = draftState.expense.categories.reduce(
                    (total, cat) => total + cat.totalValue,
                    0
                );
            })
        ),

    addCategory: (category) =>
        set(
            produce((draftState) => {
                if (draftState.activeTab === 0) {
                    draftState.revenue.categories.push(category);
                    draftState.revenue.totalValue += category.totalValue;
                } else if (draftState.activeTab === 1) {
                    draftState.expense.categories.push(category);
                    draftState.expense.totalValue += category.totalValue;
                }
            })
        ),

    deleteCategory: (categoryName) =>
        set(
            produce((draftState) => {
                draftState.revenue.categories = draftState.revenue.categories.filter((cat) => cat._id !== categoryName);
                draftState.revenue.totalValue = draftState.revenue.categories.reduce(
                    (total, cat) => total + cat.totalValue,
                    0
                );

                draftState.expense.categories = draftState.expense.categories.filter((cat) => cat._id !== categoryName);
                draftState.expense.totalValue = draftState.expense.categories.reduce(
                    (total, cat) => total + cat.totalValue,
                    0
                );
            })
        ),

    addSubCategory: (categoryName, subCategory) =>
        set(
            produce((draftState) => {
                draftState.revenue.categories.forEach((cat) => {
                    if (cat.name === categoryName) {
                        cat.subCategories.push(subCategory);
                        cat.totalValue += subCategory.value;
                    }
                });
                draftState.revenue.totalValue = draftState.revenue.categories.reduce(
                    (total, cat) => total + cat.totalValue,
                    0
                );

                draftState.expense.categories.forEach((cat) => {
                    if (cat.name === categoryName) {
                        cat.subCategories.push(subCategory);
                        cat.totalValue += subCategory.value;
                    }
                });
                draftState.expense.totalValue = draftState.expense.categories.reduce(
                    (total, cat) => total + cat.totalValue,
                    0
                );
            })
        ),

    deleteSubCategory: (categoryName, subCategoryName) =>
        set(
            produce((draftState) => {
                draftState.revenue.categories.forEach((cat) => {
                    if (cat.name === categoryName) {
                        cat.subCategories = cat.subCategories.filter((sub) => sub.name !== subCategoryName);
                        cat.totalValue = cat.subCategories.reduce((total, sub) => total + sub.value, 0);
                    }
                });
                draftState.revenue.totalValue = draftState.revenue.categories.reduce(
                    (total, cat) => total + cat.totalValue,
                    0
                );

                draftState.expense.categories.forEach((cat) => {
                    if (cat.name === categoryName) {
                        cat.subCategories = cat.subCategories.filter((sub) => sub.name !== subCategoryName);
                        cat.totalValue = cat.subCategories.reduce((total, sub) => total + sub.value, 0);
                    }
                });
                draftState.expense.totalValue = draftState.expense.categories.reduce(
                    (total, cat) => total + cat.totalValue,
                    0
                );
            })
        ),

    setActiveTab: (tab) =>
        set(
            produce((draftState) => {
                draftState.activeTab = tab;
            })
        ),

    getLatestBudget: async () => {
        try {
            const latestBudget = await api.get("/budgets/latest");
            set(
                produce((draftState) => {
                    draftState.latestBudget = latestBudget.data;
                    draftState.selectedBudget = latestBudget.data;
                })
            );
            return latestBudget.data;
        } catch (error) {
            console.error("Failed to fetch latest budget:", error);
            router.navigate("/network-error");
        }
    },

    getAllBudgets: async () => {
        try {
            const allBudgets = await api.get("/budgets");
            set(
                produce((draftState) => {
                    draftState.allBudgets = allBudgets.data;
                })
            );
        } catch (e) {
            console.error(e);
            router.navigate("/network-error");
        }
    },

    setSelectedBudget: (budget) =>
        set(
            produce((draftState) => {
                draftState.selectedBudget = budget;
            })
        ),

    // API Actions

    fetchBuget: async () => {
        try {
            const response = await api.get("/budgets/latest");
            set((state) => ({
                ...state,
                latestBudget: response.data,
                selectedBudget: response.data,
            }));
        } catch (err) {
            throw err;
        }
    },

    fetchBudgets: async () => {
        try {
            const response = await api.get("/budgets");
            set((state) => ({ ...state, allBudgets: response.data }));
        } catch (err) {
            throw err;
        }
    },

    addBudget: async (newBudget) => {
        try {
            const response = await api.post("/budgets", newBudget);
            set((state) => ({ ...state, latestBudget: response.data.data }));
        } catch (error) {
            throw error;
        }
    },

    updateBudget: async (budget, body: any) => {
        try {
            const response = await api.patch(`/budgets/${budget._id}`, body);
            set((state) => ({ ...state, selectedBudget: response.data }));
        } catch (err: any) {
            throw err;
        }
    },
}));

export default useBudgetStore;
