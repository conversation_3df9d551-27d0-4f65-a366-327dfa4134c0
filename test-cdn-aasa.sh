#!/bin/bash

# Nginx AASA Testing Script for Converty
# Tests various aspects of AASA file delivery through Nginx

DOMAIN="partner.converty.shop"
AASA_URL="https://${DOMAIN}/.well-known/apple-app-site-association"
LOCAL_AASA="/var/www/converty/.well-known/apple-app-site-association"

echo "🌐 Testing CDN Configuration for Universal Links"
echo "================================================"
echo "Domain: $DOMAIN"
echo "AASA URL: $AASA_URL"
echo ""

# Test 1: Basic accessibility
echo "📡 Test 1: Basic AASA File Accessibility"
echo "----------------------------------------"
response=$(curl -s -w "HTTP_CODE:%{http_code}\nTIME_TOTAL:%{time_total}\nSIZE:%{size_download}\n" "$AASA_URL")
echo "$response"
echo ""

# Test 2: Headers analysis
echo "📋 Test 2: HTTP Headers Analysis"
echo "--------------------------------"
curl -I "$AASA_URL" 2>/dev/null | while read line; do
    echo "   $line"
done
echo ""

# Test 3: Content-Type verification
echo "🏷️  Test 3: Content-Type Verification"
echo "-------------------------------------"
content_type=$(curl -s -I "$AASA_URL" | grep -i "content-type" | head -1)
echo "   $content_type"

if echo "$content_type" | grep -q "application/json"; then
    echo "   ✅ Correct Content-Type found"
else
    echo "   ❌ Incorrect Content-Type (should be application/json)"
fi
echo ""

# Test 4: JSON validation
echo "🔍 Test 4: JSON Validation"
echo "--------------------------"
json_content=$(curl -s "$AASA_URL")
echo "$json_content" | python3 -m json.tool > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "   ✅ Valid JSON format"
else
    echo "   ❌ Invalid JSON format"
fi
echo ""

# Test 5: Apple's User-Agent test
echo "🍎 Test 5: Apple User-Agent Test"
echo "--------------------------------"
apple_response=$(curl -s -H "User-Agent: com.apple.trustd/2.0" "$AASA_URL")
echo "   Response length: ${#apple_response} characters"
if [ ${#apple_response} -gt 50 ]; then
    echo "   ✅ Apple User-Agent gets valid response"
else
    echo "   ❌ Apple User-Agent gets invalid/empty response"
fi
echo ""

# Test 6: Cache behavior
echo "💾 Test 6: Cache Behavior Analysis"
echo "----------------------------------"
echo "   First request:"
first_headers=$(curl -s -I "$AASA_URL")
echo "$first_headers" | grep -i "cache\|etag\|last-modified" | while read line; do
    echo "      $line"
done

echo "   Second request (checking cache):"
second_headers=$(curl -s -I "$AASA_URL")
echo "$second_headers" | grep -i "cache\|etag\|last-modified" | while read line; do
    echo "      $line"
done
echo ""

# Test 7: Geographic distribution test
echo "🌍 Test 7: Geographic Distribution"
echo "----------------------------------"
echo "   Testing from different locations..."

# Test with different CF-IPCountry headers (if using Cloudflare)
for country in US EU JP; do
    response_time=$(curl -s -w "%{time_total}" -H "CF-IPCountry: $country" "$AASA_URL" -o /dev/null)
    echo "   $country: ${response_time}s"
done
echo ""

# Test 8: SSL/TLS verification
echo "🔒 Test 8: SSL/TLS Certificate"
echo "------------------------------"
ssl_info=$(curl -s -I "$AASA_URL" 2>&1 | grep -i "ssl\|tls\|certificate")
if [ $? -eq 0 ]; then
    echo "   ✅ SSL/TLS connection successful"
else
    echo "   ❌ SSL/TLS issues detected"
    echo "   🔍 Debugging SSL/TLS issues:"
    
    # Check SSL certificate validity
    ssl_debug=$(openssl s_client -connect "${DOMAIN}:443" -servername "$DOMAIN" 2>&1)
    if echo "$ssl_debug" | grep -q "verify error"; then
        echo "      - Certificate verification failed"
        echo "      - Error: $(echo "$ssl_debug" | grep "verify error")"
    fi
    
    # Check certificate expiration
    cert_dates=$(echo "$ssl_debug" | openssl x509 -noout -dates 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo "      - Certificate dates:"
        echo "$cert_dates" | sed 's/^/        /'
    fi
    
    # Check SSL/TLS protocol version
    protocol_ver=$(echo "$ssl_debug" | grep "Protocol" | sed 's/^/        /')
    echo "      - Protocol version:"
    echo "$protocol_ver"
fi

# Check certificate details
echo "   Certificate details:"
openssl s_client -connect "${DOMAIN}:443" -servername "$DOMAIN" 2>/dev/null | openssl x509 -noout -subject -dates 2>/dev/null | while read line; do
    echo "      $line"
done
echo ""

# Test 9: CDN detection
echo "🔧 Test 9: CDN Detection"
echo "------------------------"
headers=$(curl -s -I "$AASA_URL")
echo "$headers" | grep -i "server\|cf-\|x-cache\|x-served-by\|x-amz-\|x-goog-" | while read line; do
    echo "   $line"
done
echo ""

# Test 10: Apple's CDN check (iOS 14+ behavior)
echo "🍎 Test 10: Apple's CDN Validation (iOS 14+ Critical)"
echo "======================================================"
apple_cdn_url="https://app-site-association.cdn-apple.com/a/v1/${DOMAIN}"
echo "   Apple CDN URL: $apple_cdn_url"
echo "   This is where iOS 14+ devices actually fetch your AASA file from!"
echo ""

apple_response=$(curl -s -w "HTTP_CODE:%{http_code}\nTIME_TOTAL:%{time_total}\n" "$apple_cdn_url")
apple_status=$(echo "$apple_response" | grep "HTTP_CODE" | cut -d: -f2)
apple_time=$(echo "$apple_response" | grep "TIME_TOTAL" | cut -d: -f2)

echo "   Apple CDN Status: $apple_status"
echo "   Response Time: ${apple_time}s"

if [ "$apple_status" = "200" ]; then
    echo "   ✅ Apple has cached your AASA file"
    echo "   📱 iOS 14+ devices will use this cached version"

    # Compare Apple's cached version with your server's version
    echo ""
    echo "   🔍 Comparing Apple's cached version with your server's version:"
    apple_content=$(curl -s "$apple_cdn_url")
    server_content=$(curl -s "$AASA_URL")

    if [ "$apple_content" = "$server_content" ]; then
        echo "   ✅ Apple's cached version matches your server's version"
    else
        echo "   ⚠️  Apple's cached version differs from your server's version"
        echo "   📝 This means Apple needs to re-fetch your updated AASA file"
        echo "   ⏳ This can take 24-48 hours after you fix your server"
    fi

    # Show Apple's cached content
    echo ""
    echo "   📄 Apple's cached AASA content:"
    echo "$apple_content" | python3 -m json.tool 2>/dev/null || echo "$apple_content"

else
    echo "   ⏳ Apple hasn't cached your AASA file yet"
    echo "   📱 iOS 14+ devices won't be able to use Universal Links"
    echo "   🔄 Possible reasons:"
    echo "      - Your AASA file has validation errors"
    echo "      - Apple's CDN hasn't crawled your site yet"
    echo "      - Your server was unreachable when Apple tried to fetch"
    echo "      - This can take 24-48 hours for new domains/changes"
fi
echo ""

# Summary
echo "📊 Summary and Recommendations"
echo "==============================="

# Check for common issues
issues_found=0

# Check duplicate content-type
duplicate_ct=$(curl -s -I "$AASA_URL" | grep -i "content-type" | wc -l)
if [ "$duplicate_ct" -gt 1 ]; then
    echo "❌ Issue: Duplicate Content-Type headers detected"
    issues_found=$((issues_found + 1))
fi

# Check content-type correctness
if ! curl -s -I "$AASA_URL" | grep -i "content-type" | grep -q "application/json"; then
    echo "❌ Issue: Incorrect Content-Type (should be application/json)"
    issues_found=$((issues_found + 1))
fi

# Check JSON validity
if ! curl -s "$AASA_URL" | python3 -m json.tool > /dev/null 2>&1; then
    echo "❌ Issue: Invalid JSON format"
    issues_found=$((issues_found + 1))
fi

if [ $issues_found -eq 0 ]; then
    echo "✅ No major issues detected!"
    echo "💡 If Safari banner still doesn't appear:"
    echo "   1. Clear Safari cache and data"
    echo "   2. Wait 24-48 hours for Apple's CDN"
    echo "   3. Test on a fresh iOS device"
else
    echo "⚠️  $issues_found issue(s) found that need to be fixed"
    echo "💡 Recommended actions:"
    echo "   1. Fix Content-Type header configuration"
    echo "   2. Ensure only one Content-Type header is sent"
    echo "   3. Validate AASA JSON format"
    echo "   4. Configure proper CDN rules for AASA file"
fi

echo ""
echo "🍎 iOS 14+ Important Information:"
echo "================================="
echo "Starting with iOS 14, devices fetch AASA files from Apple's CDN, not your server directly!"
echo ""
echo "� What this means:"
echo "   - Your server fixes won't immediately affect iOS 14+ devices"
echo "   - Apple's CDN needs to re-fetch and cache your updated AASA file"
echo "   - This process can take 24-48 hours or longer"
echo "   - The Safari banner won't reappear until Apple's CDN is updated"
echo ""
echo "🔄 If you need immediate testing (development only):"
echo "   You can use alternate mode in your app.json:"
echo "   \"associatedDomains\": [\"applinks:partner.converty.shop?mode=developer\"]"
echo "   This bypasses Apple's CDN and fetches directly from your server"
echo "   ⚠️  Remove this before production release!"
echo ""
echo "�🔗 Useful links:"
echo "   - Apple's AASA validator: https://search.developer.apple.com/appsearch-validation-tool/"
echo "   - Your AASA file: $AASA_URL"
echo "   - Apple's CDN: $apple_cdn_url"
echo "   - Apple's CDN documentation: https://developer.apple.com/documentation/xcode/supporting-associated-domains"
