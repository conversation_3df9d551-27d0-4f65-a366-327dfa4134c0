{"name": "mobile-v2", "version": "2.2.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "prettier --write .", "check": "tsc --noEmit"}, "dependencies": {"@expo/metro-runtime": "~4.0.1", "@gorhom/bottom-sheet": "^4.6.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/slider": "4.5.5", "@react-native-cookies/cookies": "^6.2.1", "@react-native-picker/picker": "2.9.0", "@react-native/babel-preset": "^0.74.2", "@react-navigation/material-top-tabs": "^7.0.6", "@react-navigation/native": "^7.0.0", "@types/react": "~18.3.12", "babel-plugin-module-resolver": "^5.0.0", "base-64": "^1.0.0", "chroma-js": "^3.1.1", "expo": "~52.0.46", "expo-av": "~15.0.2", "expo-build-properties": "^0.14.8", "expo-camera": "~16.0.18", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.3", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-picker": "~16.0.6", "expo-jwt": "^1.7.1", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-module-scripts": "^3.4.1", "expo-navigation-bar": "~4.0.9", "expo-notifications": "~0.29.14", "expo-router": "~4.0.20", "expo-screen-orientation": "~8.0.4", "expo-secure-store": "~14.0.1", "expo-sensors": "~14.0.2", "expo-status-bar": "~2.0.1", "expo-web-browser": "~14.0.2", "immer": "^10.1.1", "lodash": "^4.17.21", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-color-picker": "^0.6.0", "react-native-element-dropdown": "^2.12.1", "react-native-elements": "^3.4.3", "react-native-expo-swipe-button": "^1.0.2", "react-native-gesture-handler": "~2.20.2", "react-native-gifted-charts": "^1.4.10", "react-native-image-picker": "^7.1.2", "react-native-linear-gradient": "^2.8.3", "react-native-pager-view": "^6.5.1", "react-native-picker-select": "^9.1.3", "react-native-reanimated": "~3.16.1", "react-native-reanimated-carousel": "^3.5.1", "react-native-render-html": "^6.3.4", "react-native-root-toast": "^3.5.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-star-rating": "^1.1.0", "react-native-svg": "15.8.0", "react-native-swipeout": "^2.3.6", "react-native-toast-message": "^2.2.0", "react-native-vector-icons": "^10.1.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "reanimated-color-picker": "^3.0.4", "rn-swipe-button": "^1.3.8", "semver": "^7.6.2", "socket.io-client": "^4.7.5", "typescript": "~5.3.3", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/ngrok": "^4.1.3", "@types/base-64": "^1.0.2", "@types/chroma-js": "^2.4.4", "@types/lodash": "^4.17.13", "prettier": "^3.2.5", "ts-jest": "^29.4.0"}, "private": true}