import api from "@api/api";
import { Order } from "@components/orders/types";

export interface OrderApiResponse {
    success: boolean;
    data: any;
    message?: string;
}

export interface OrderUpdatePayload {
    _id: string;
    [key: string]: any;
}

export interface OrderCreatePayload {
    [key: string]: any;
}

/**
 * Service class for handling all order-related API operations
 * Centralizes HTTP requests and provides consistent error handling
 */
export class OrderService {
    /**
     * Scan an order by ID
     */
    static async scanOrder(orderID: string): Promise<OrderApiResponse> {
        try {
            const response = await api.patch(`/order/${orderID}`);
            return {
                success: response.success,
                data: response.data,
                message: response.message,
            };
        } catch (error) {
            console.error("Order scan error:", error);
            throw new Error(`Failed to scan order: ${error.message}`);
        }
    }

    /**
     * Update an existing order
     */
    static async updateOrder(updatedOrder: OrderUpdatePayload): Promise<OrderApiResponse> {
        try {
            const response = await api.patch(`/order/${updatedOrder._id}`, updatedOrder);
            return {
                success: response.success,
                data: response.data,
                message: response.message,
            };
        } catch (error) {
            console.error("Order update error:", error);
            throw new Error(`Failed to update order: ${error.message}`);
        }
    }

    /**
     * Create a new order
     */
    static async createOrder(order: OrderCreatePayload): Promise<OrderApiResponse> {
        try {
            const response = await api.post("/order", order);
            return {
                success: response.success,
                data: response.data,
                message: response.message,
            };
        } catch (error) {
            console.error("Order creation error:", error);
            throw new Error(`Failed to create order: ${error.message}`);
        }
    }

    /**
     * Delete an order
     */
    static async deleteOrder(orderId: string): Promise<OrderApiResponse> {
        try {
            const response = await api.delete(`/order/${orderId}`);
            return {
                success: response.success,
                data: response.data,
                message: response.message,
            };
        } catch (error) {
            console.error("Order deletion error:", error);
            throw new Error(`Failed to delete order: ${error.message}`);
        }
    }

    /**
     * Restore a deleted order
     */
    static async restoreOrder(orderId: string): Promise<OrderApiResponse> {
        try {
            const response = await api.patch(`/order/${orderId}/restore`);
            return {
                success: response.success,
                data: response.data,
                message: response.message,
            };
        } catch (error) {
            console.error("Order restoration error:", error);
            throw new Error(`Failed to restore order: ${error.message}`);
        }
    }

    /**
     * Get products list
     */
    static async getProducts(): Promise<OrderApiResponse> {
        try {
            const response = await api.get("/product/list");
            return {
                success: response.success,
                data: response.data,
                message: response.message,
            };
        } catch (error) {
            console.error("Products fetch error:", error);
            throw new Error(`Failed to fetch products: ${error.message}`);
        }
    }

    /**
     * Fetch customer orders by order ID
     */
    static async fetchCustomerOrdersByOrderId(orderId: string): Promise<OrderApiResponse> {
        try {
            const response = await api.get(`/blocked-customers/orders-by-order/${orderId}`);
            return {
                success: response.success,
                data: response.data.data || response.data,
                message: response.message,
            };
        } catch (error) {
            console.error("Customer orders fetch error:", error);
            throw new Error(`Failed to fetch customer orders: ${error.message}`);
        }
    }

    /**
     * Block customer by order ID
     */
    static async blockCustomer(orderId: string): Promise<OrderApiResponse> {
        try {
            const response = await api.post(`/blocked-customers/block-by-order/${orderId}`);
            return {
                success: true,
                data: response.data,
                message: "Customer blocked successfully",
            };
        } catch (error) {
            console.error("Customer blocking error:", error);
            throw new Error(`Failed to block customer: ${error.response?.data?.message || error.message}`);
        }
    }

    /**
     * Delete customer orders
     */
    static async deleteCustomerOrders(orderId: string): Promise<OrderApiResponse> {
        try {
            const response = await api.delete(`/blocked-customers/delete-orders/${orderId}`);
            return {
                success: true,
                data: response.data,
                message: "All orders from this customer have been deleted",
            };
        } catch (error) {
            console.error("Customer orders deletion error:", error);
            throw new Error(`Failed to delete customer orders: ${error.response?.data?.message || error.message}`);
        }
    }
}
