import * as Notifications from "expo-notifications";
import * as SecureStore from "expo-secure-store";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Store } from "../types/Store";
import { ToastHandler } from "../utils/ToastHandler";
import { socketManager } from "../socket/socket";

// Types
export interface LoginCredentials {
    email: string;
    password: string;
    token?: Notifications.DevicePushToken;
}

export interface AuthResponse {
    success: boolean;
    message: string;
    data: {
        stores?: Store[];
        image?: {
            sm?: string;
        };
        notificationTokens?: any;
        [key: string]: any;
    };
    headers: {
        "set-cookie"?: string;
        [key: string]: any;
    };
}

export type AuthResult = "auth" | "unauth" | "multistore";

/**
 * Authentication Service
 * Handles all authentication-related API operations with proper error handling
 */
export class AuthService {
    private static readonly API_BASE_URL = "https://partner.converty.shop/api/v2";
    private static readonly AUTH_COOKIE_KEY = "auth-cookie";
    private static readonly USER_IMAGE_KEY = "user-image";

    /**
     * Authenticate user with email and password
     */
    static async authenticate(credentials: LoginCredentials): Promise<AuthResponse> {
        const { email, password, token } = credentials;

        try {
            console.log("Authenticating user:", { email, hasToken: !!token });

            const response = await fetch(`${this.API_BASE_URL}/auth/login`, {
                method: "POST",
                headers: { 
                    "Content-Type": "application/json" 
                },
                body: JSON.stringify({
                    username: email,
                    password: password,
                    deviceToken: token?.data || "testToken",
                    deviceType: token?.type || "ios",
                }),
                credentials: "include",
            });

            const result = await response.json();
            
            console.log("Authentication response:", {
                success: result.success,
                hasStores: !!result.data?.stores,
                storeCount: result.data?.stores?.length || 0
            });

            if (result.success) {
                // Extract cookies from response headers
                const setCookieHeader = response.headers.get("set-cookie");
                console.log("Set-Cookie header:", setCookieHeader);

                return {
                    ...result,
                    headers: {
                        "set-cookie": setCookieHeader
                    }
                };
            } else {
                throw new Error(result.message || "Authentication failed");
            }
        } catch (error) {
            console.error("Authentication error:", error);
            throw new Error(error.message || "Network error during authentication");
        }
    }

    /**
     * Complete login process with proper state management
     */
    static async login(credentials: LoginCredentials): Promise<{
        result: AuthResult;
        stores?: Store[];
    }> {
        try {
            const response = await this.authenticate(credentials);

            if (response.success) {
                // Store authentication cookie
                await this.storeAuthCookie(response.headers);
                console.log("cooke =>",await this.getAuthCookie());

                // Store user image if available
                await this.storeUserImage(response.data.image);
                console.log("image =>",await AsyncStorage.getItem(this.USER_IMAGE_KEY));

                // Connect socket
                (socketManager as any).socket.connect();

                // Log token information
                console.log("Login successful:", {
                    hasToken: !!credentials.token,
                    notificationTokens: response.data.notificationTokens
                });

                // Determine auth result based on store count
                if (response.data?.stores?.length > 1) {
                    return {
                        result: "multistore",
                        stores: response.data.stores
                    };
                }

                return { result: "auth" };
            }

            return { result: "unauth" };
        } catch (error) {
            console.error("Login error:", error);

            ToastHandler.showErrorToast(
                "Authentication Error",
                error.message || "Failed to authenticate. Please try again."
            );

            return { result: "unauth" };
        }
    }

    /**
     * Store authentication cookie securely
     * Matches the format used by the original auth system
     */
    private static async storeAuthCookie(headers: any): Promise<void> {
        try {
            const cookieValue = headers["set-cookie"];
            console.log("Attempting to store cookie:", { cookieValue, hasHeaders: !!headers });

            if (!cookieValue) {
                console.warn("No set-cookie header found in response");
                return;
            }

            // Parse the cookie to extract the token value (matching original implementation)
            const tokenValue = cookieValue.split("=")[1]?.split(";")[0];
            if (!tokenValue) {
                console.error("Could not extract token from cookie:", cookieValue);
                return;
            }

            // Format the cookie as "token=value" (matching original format)
            const formattedCookie = `token=${tokenValue}`;

            console.log("Storing formatted auth cookie:", formattedCookie);
            await SecureStore.setItemAsync(this.AUTH_COOKIE_KEY, formattedCookie);
            console.log("Auth cookie stored successfully");

            // Also update the auth store cookie state (matching original implementation)
            const { useAuthStore } = require("../store/authStore");
            useAuthStore.getState().setCookie(cookieValue);

            // Verify the cookie was stored
            const storedCookie = await SecureStore.getItemAsync(this.AUTH_COOKIE_KEY);
            console.log("Verification - stored cookie:", storedCookie ? "✓ Found" : "✗ Not found");

        } catch (error) {
            console.error("Error storing auth cookie:", error);
        }
    }

    /**
     * Store user image in AsyncStorage
     */
    private static async storeUserImage(image?: { sm?: string }): Promise<void> {
        try {
            if (image?.sm) {
                await AsyncStorage.setItem(this.USER_IMAGE_KEY, image.sm);
                console.log("User image stored successfully");
            }
        } catch (error) {
            console.error("Error storing user image:", error);
        }
    }

    /**
     * Get stored authentication cookie
     */
    static async getAuthCookie(): Promise<string | null> {
        try {
            return await SecureStore.getItemAsync(this.AUTH_COOKIE_KEY);
        } catch (error) {
            console.error("Error retrieving auth cookie:", error);
            return null;
        }
    }

    /**
     * Clear stored authentication data
     */
    static async clearAuthData(): Promise<void> {
        try {
            await Promise.all([
                SecureStore.deleteItemAsync(this.AUTH_COOKIE_KEY).catch(() => {}),
                AsyncStorage.removeItem(this.USER_IMAGE_KEY).catch(() => {})
            ]);
            console.log("Auth data cleared successfully");
        } catch (error) {
            console.error("Error clearing auth data:", error);
        }
    }

    /**
     * Validate authentication state
     */
    static async validateAuthState(): Promise<boolean> {
        try {
            const cookie = await this.getAuthCookie();
            return !!cookie;
        } catch (error) {
            console.error("Error validating auth state:", error);
            return false;
        }
    }
}
