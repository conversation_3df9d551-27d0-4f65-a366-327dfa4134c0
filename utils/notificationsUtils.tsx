import * as Notifications from "expo-notifications";
import Toast from "react-native-toast-message";
import * as Device from "expo-device";
import Constants from "expo-constants";
import { Platform } from "react-native";

import { useStoreStore } from "../store/storeStore";

// Generate unique ID for notifications to prevent replacement
function generateUniqueNotificationId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

const GROUP_ID = "CONVERTY_ORDERS";

async function sendPushNotification() {
    await Notifications.scheduleNotificationAsync({
        content: {
            title: "You have a new notification",
            body: "Here is the notification body",
            sound: true,
        },
        trigger: null,
    });
}

function handleRegistrationError(errorMessage: string) {
    Toast.show({
        text1: "Error registering for push notifications",
        text2: errorMessage,
    });
}

// Add this function to check if device token exists in user's tokens
const isDeviceTokenRegistered = async () => {
    try {
        // Get current device token
        const deviceToken = await Notifications.getDevicePushTokenAsync();

        // Get user from store
        const user = useStoreStore.getState().user;
        console.log("device token => ", deviceToken, "user tokens => ", user?.notificationTokens.map((tokenObj) => tokenObj.token === deviceToken.data && tokenObj.type === deviceToken.type));
        // Check if token exists in user's notification tokens
        return (
            user?.notificationTokens?.some(
                (tokenObj) => tokenObj.token === deviceToken.data && tokenObj.type === deviceToken.type
            ) || false
        );
    } catch (error) {
        console.error("Error checking device token:", error);
        return false;
    }
};

async function registerForPushNotificationsAsync(): Promise<Notifications.DevicePushToken> {
    if (Platform.OS === "android") {
        Notifications.setNotificationChannelAsync("default", {
            name: "default",
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: "#FF231F7C",
            groupId: GROUP_ID,
            showBadge: true,
        });

        Notifications.setNotificationChannelAsync("ahmed_mohsen", {
            name: "ahmed_mohsen",
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: "#FF231F7C",
            sound: "ahmed_mohsen.wav",
            groupId: GROUP_ID,
            showBadge: true,
        });

        Notifications.setNotificationChannelAsync("chaching", {
            name: "chaching",
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: "#FF231F7C",
            sound: "chaching.wav",
            groupId: GROUP_ID,
            showBadge: true,
        });
        Notifications.setNotificationChannelAsync("siuu", {
            name: "siuu",
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: "#FF231F7C",
            sound: "siuu.wav",
            groupId: GROUP_ID,
            showBadge: true,
        });
    }

    if (Device.isDevice) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;
        if (existingStatus !== "granted") {
            const { status } = await Notifications.requestPermissionsAsync();
            finalStatus = status;
        }
        if (finalStatus !== "granted") {
            handleRegistrationError("Permission not granted to get push token for push notification!");
            return;
        }
        const projectId = Constants?.expoConfig?.extra?.eas?.projectId ?? Constants?.easConfig?.projectId;
        if (!projectId) {
            handleRegistrationError("Project ID not found");
        }
        try {
            return await Notifications.getDevicePushTokenAsync();
        } catch (e: unknown) {
            handleRegistrationError(`${e}`);
        }
    } else {
        handleRegistrationError("Must use physical device for push notifications");
    }
}

// Function to intercept and immediately re-schedule notifications with unique IDs
async function interceptAndRescheduleNotification(notification: Notifications.Notification) {
    console.log("Intercepting notification:", notification.request.content.title);

    // Extract notification data
    const { title, body, sound, data } = notification.request.content;

    // Generate unique ID
    const uniqueId = generateUniqueNotificationId();

    console.log(`Re-scheduling with unique ID: ${uniqueId}`);

    // Immediately re-schedule with unique ID
    await Notifications.scheduleNotificationAsync({
        identifier: uniqueId,
        content: {
            title: title || "New Notification",
            body: body || "",
            sound: sound,
            data: {
                ...data,
                notificationId: uniqueId,
                timestamp: Date.now(),
            },
        },
        trigger: null, // Immediate
    });
}

// Setup notification interceptor
function setupNotificationInterceptor() {
    Notifications.setNotificationHandler({
        handleNotification: async (notification) => {
            // Check if this notification already has our unique ID format (already processed)
            const identifier = notification.request.identifier;
            if (identifier && identifier.startsWith("notification_")) {
                console.log("Notification already processed, showing it:", notification.request.content.title);
                // Show the processed notification normally
                return {
                    shouldShowAlert: true,
                    shouldPlaySound: true,
                    shouldSetBadge: true,
                };
            }

            console.log("New notification from backend, intercepting:", notification.request.content.title);
            // Intercept and re-schedule with unique ID
            await interceptAndRescheduleNotification(notification);

            // Don't show the original notification
            return {
                shouldShowAlert: false,
                shouldPlaySound: false,
                shouldSetBadge: false,
            };
        },
    });

    console.log("Notification interceptor setup complete");
}

export {
    registerForPushNotificationsAsync,
    isDeviceTokenRegistered,
    sendPushNotification,
    setupNotificationInterceptor,
    generateUniqueNotificationId,
};
