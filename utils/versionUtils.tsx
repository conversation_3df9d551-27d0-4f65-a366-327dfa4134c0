import api from "@api/api";
import semver from "semver";

type checkVersion = {
    forceUpdate: boolean;
    minVersion?: string;
    latestVersion?: string;
    releaseNote?: string;
};

const currentVersion = "2.0.0";

const checkVersion = async () => {
    try {
        const response = await api.get("/auth/version");
        if (semver.lt(currentVersion, response.data.minVersion)) {
            return { forceUpdate: true, ...response.data };
        } else {
            return { forceUpdate: false };
        }
    } catch (error) {
        return { forceUpdate: false };
    }
};

export default checkVersion;
