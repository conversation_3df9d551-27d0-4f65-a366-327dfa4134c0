import { Order } from "@components/orders/types";
import { Status } from "../types/Order";
import { OrderEditFormData, RejectionReason } from "../components/modals/types/OrdersListModalTypes";

/**
 * Utility functions for order modal operations
 * Handles validation, data transformation, and business logic
 */
export class OrderModalUtils {
    /**
     * Validate order selection for modal operations
     */
    static validateOrderSelection(
        selectedOrders: Order[], 
        mode: string
    ): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (selectedOrders.length === 0) {
            errors.push("No orders selected");
        }

        if (mode === "delete" || mode === "restore") {
            if (selectedOrders.length > 1) {
                errors.push(`${mode} operation supports only single order selection`);
            }
        }

        // Check for deleted orders in non-restore operations
        if (mode !== "restore") {
            const deletedOrders = selectedOrders.filter(order => order.isDeleted);
            if (deletedOrders.length > 0) {
                errors.push("Cannot perform operation on deleted orders");
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Validate form data for edit operations
     */
    static validateEditFormData(formData: OrderEditFormData): { 
        isValid: boolean; 
        errors: string[] 
    } {
        const errors: string[] = [];

        if (!formData.status) {
            errors.push("Status is required");
        }

        if (formData.status === "rejected" && !formData.rejectionReason) {
            errors.push("Rejection reason is required for rejected status");
        }

        if (formData.status === "attempt") {
            if (!formData.attempt || formData.attempt < 1) {
                errors.push("Valid attempt number is required");
            }
        }

        if (formData.status && 
            !["rejected", "attempt"].includes(formData.status) && 
            !formData.deliveryCompany) {
            errors.push("Delivery company is required for this status");
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Build API request body for order edit operations
     */
    static buildEditRequestBody(formData: OrderEditFormData): Record<string, any> {
        const { status, attempt, note, rejectionReason, deliveryCompany } = formData;

        switch (status) {
            case "attempt":
                return { status, attempt, note };
            
            case "rejected":
                return { 
                    status, 
                    customer: { rejectionReason }, 
                    note 
                };
            
            default:
                return { status, deliveryCompany, note };
        }
    }

    /**
     * Extract order references for API calls
     */
    static extractOrderReferences(orders: Order[]): number[] {
        return orders.map(order => order.reference);
    }

    /**
     * Format order references for display
     */
    static formatOrderReferencesForDisplay(orders: Order[]): string {
        if (orders.length === 0) return "";
        if (orders.length === 1) return `#${orders[0].reference}`;
        
        const references = orders.map(order => `#${order.reference}`);
        if (references.length <= 3) {
            return references.join(", ");
        }
        
        return `${references.slice(0, 2).join(", ")} and ${references.length - 2} more`;
    }

    /**
     * Check if orders can be sent as leads
     */
    static canSendAsLeads(orders: Order[]): { canSend: boolean; reason?: string } {
        if (orders.length === 0) {
            return { canSend: false, reason: "No orders selected" };
        }

        const invalidOrders = orders.filter(order => 
            order.isDeleted || 
            order.status === "delivered" || 
            order.status === "cancelled"
        );

        if (invalidOrders.length > 0) {
            return { 
                canSend: false, 
                reason: "Cannot send delivered, cancelled, or deleted orders as leads" 
            };
        }

        return { canSend: true };
    }

    /**
     * Check if order can be deleted
     */
    static canDeleteOrder(order: Order): { canDelete: boolean; reason?: string } {
        if (order.isDeleted) {
            return { canDelete: false, reason: "Order is already deleted" };
        }

        if (order.status === "delivered") {
            return { canDelete: false, reason: "Cannot delete delivered orders" };
        }

        return { canDelete: true };
    }

    /**
     * Check if order can be restored
     */
    static canRestoreOrder(order: Order): { canRestore: boolean; reason?: string } {
        if (!order.isDeleted) {
            return { canRestore: false, reason: "Order is not deleted" };
        }

        return { canRestore: true };
    }

    /**
     * Get modal title based on mode and selection
     */
    static getModalTitle(mode: string, selectedOrders: Order[]): string {
        const orderCount = selectedOrders.length;
        const orderRef = selectedOrders[0]?.reference;

        switch (mode) {
            case "delete":
                return `Delete Order ${orderRef ? `#${orderRef}` : ""}`;
            
            case "restore":
                return `Restore Order ${orderRef ? `#${orderRef}` : ""}`;
            
            case "send":
                return "Send Orders";
            
            case "edit":
                return orderCount === 1 ? "Edit Order" : "Edit Orders";
            
            default:
                return "Order Operation";
        }
    }

    /**
     * Get confirmation message for operations
     */
    static getConfirmationMessage(mode: string, selectedOrders: Order[]): string {
        const orderCount = selectedOrders.length;
        const orderRef = selectedOrders[0]?.reference;

        switch (mode) {
            case "delete":
                return `Are you sure you want to delete Order #${orderRef}?`;
            
            case "restore":
                return `Are you sure you want to restore Order #${orderRef}?`;
            
            case "send":
                return `Send ${orderCount} order${orderCount > 1 ? "s" : ""} as leads?`;
            
            default:
                return "Are you sure you want to proceed?";
        }
    }

    /**
     * Sanitize form input data
     */
    static sanitizeFormData(formData: OrderEditFormData): OrderEditFormData {
        return {
            ...formData,
            note: formData.note?.trim() || undefined,
            deliveryCompany: formData.deliveryCompany?.trim() || undefined,
            rejectionReason: formData.rejectionReason?.trim() || undefined,
        };
    }

    /**
     * Check if form has unsaved changes
     */
    static hasUnsavedChanges(
        currentData: OrderEditFormData, 
        initialData: OrderEditFormData
    ): boolean {
        return (
            currentData.status !== initialData.status ||
            currentData.deliveryCompany !== initialData.deliveryCompany ||
            currentData.attempt !== initialData.attempt ||
            currentData.rejectionReason !== initialData.rejectionReason ||
            currentData.note !== initialData.note
        );
    }

    /**
     * Get status-specific field requirements
     */
    static getFieldRequirements(status: Status): {
        requiresDeliveryCompany: boolean;
        requiresRejectionReason: boolean;
        requiresAttempt: boolean;
        allowsNote: boolean;
    } {
        return {
            requiresDeliveryCompany: !["rejected", "attempt"].includes(status),
            requiresRejectionReason: status === "rejected",
            requiresAttempt: status === "attempt",
            allowsNote: true,
        };
    }
}
