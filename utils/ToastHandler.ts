import Toast from "react-native-toast-message";
import { router } from "expo-router";

export interface ErrorConfig {
    showToast?: boolean;
    navigateToError?: boolean;
    logError?: boolean;
    customMessage?: string;
}

export interface ToastConfig {
    type: "success" | "error" | "info";
    text1: string;
    text2?: string;
    visibilityTime?: number;
}

/**
 * Centralized error handling utility
 * Provides consistent error handling patterns across the application
 */
export class ToastHandler {
    /**
     * Handle API errors with consistent patterns
     */
    static handleApiError(
        error: any,
        context: string,
        config: ErrorConfig = {}
    ): void {
        const {
            showToast = true,
            navigateToError = false,
            logError = true,
            customMessage
        } = config;

        const errorMessage = customMessage || 
            error?.response?.data?.message || 
            error?.message || 
            "An unexpected error occurred";

        if (logError) {
            console.error(`${context} error:`, error);
        }

        if (showToast) {
            this.showErrorToast(context, errorMessage);
        }

        if (navigateToError) {
            router.navigate("/network-error");
        }
    }

    /**
     * Handle order operation errors
     */
    static handleOrderError(
        error: any,
        operation: string,
        orderReference?: string | number
    ): void {
        const context = orderReference 
            ? `${operation} Order #${orderReference}`
            : `${operation} Order`;

        this.handleApiError(error, context, {
            showToast: true,
            logError: true,
        });
    }

    /**
     * Show success toast for order operations
     */
    static showOrderSuccess(
        operation: string,
        orderReference?: string | number,
        customMessage?: string
    ): void {
        const message = customMessage || 
            (orderReference 
                ? `Order #${orderReference} ${operation.toLowerCase()} successfully`
                : `Order ${operation.toLowerCase()} successfully`);

        Toast.show({
            type: "success",
            text1: `Order ${operation}`,
            text2: message,
        });
    }

    /**
     * Show info toast for order operations
     */
    static showOrderInfo(
        operation: string,
        orderReference?: string | number,
        customMessage?: string
    ): void {
        const message = customMessage || 
            (orderReference 
                ? `Order #${orderReference} ${operation.toLowerCase()}`
                : `Order ${operation.toLowerCase()}`);

        Toast.show({
            type: "info",
            text1: `Order ${operation}`,
            text2: message,
        });
    }

    /**
     * Show error toast
     */
    static showErrorToast(title: string, message: string): void {
        Toast.show({
            type: "error",
            text1: title,
            text2: message,
        });
    }

    /**
     * Show success toast
     */
    static showSuccessToast(title: string, message: string): void {
        Toast.show({
            type: "success",
            text1: title,
            text2: message,
        });
    }

    /**
     * Show info toast
     */
    static showInfoToast(title: string, message: string): void {
        Toast.show({
            type: "info",
            text1: title,
            text2: message,
        });
    }

    /**
     * Show custom toast
     */
    static showToast(config: ToastConfig): void {
        Toast.show({
            type: config.type,
            text1: config.text1,
            text2: config.text2,
            visibilityTime: config.visibilityTime,
        });
    }

    /**
     * Handle validation errors
     */
    static handleValidationError(
        field: string,
        message: string
    ): void {
        this.showErrorToast(
            "Validation Error",
            `${field}: ${message}`
        );
    }

    /**
     * Handle network errors
     */
    static handleNetworkError(context: string): void {
        this.handleApiError(
            new Error("Network connection failed"),
            context,
            {
                showToast: true,
                navigateToError: true,
                customMessage: "Please check your internet connection and try again"
            }
        );
    }
}
