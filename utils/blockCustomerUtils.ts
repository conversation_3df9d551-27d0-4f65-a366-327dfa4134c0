import { Platform } from "react-native";
import { Order } from "@components/orders/types";

// Type definitions for utility functions
export interface SnapPointsConfig {
    loading: string[];
    empty: string[];
    single: string[];
    double: string[];
    triple: string[];
    multiple: string[];
}

export interface PlatformOptimizations {
    getItemLayout?: (data: any, index: number) => { length: number; offset: number; index: number };
    initialNumToRender?: number;
    maxToRenderPerBatch?: number;
    windowSize?: number;
    removeClippedSubviews?: boolean;
    updateCellsBatchingPeriod?: number;
    legacyImplementation?: boolean;
}

/**
 * Determines if customer activity is suspicious based on order frequency
 * @param orders Array of customer orders
 * @returns boolean indicating if activity is suspicious
 */
export const isSuspiciousActivity = (orders: Order[]): boolean => {
    if (orders.length < 3) return false;

    // Check for multiple orders in a short time span (within 24 hours)
    const recentOrders = orders.filter((order) => {
        const orderDate = new Date(order.createdAt);
        const now = new Date();
        return now.getTime() - orderDate.getTime() <= 24 * 60 * 60 * 1000;
    });

    return recentOrders.length >= 3;
};

/**
 * Calculates appropriate snap points based on order count and loading state
 * @param orderCount Number of orders
 * @param isLoading Whether data is loading
 * @param isDataReady Whether data is ready to display
 * @param isSuspicious Whether activity is suspicious
 * @returns Array of snap point percentages
 */
export const calculateSnapPoints = (
    orderCount: number,
    isLoading: boolean,
    isDataReady: boolean,
    isSuspicious: boolean
): string[] => {
    const config: SnapPointsConfig = {
        loading: ["50%"],
        empty: ["50%"],
        single: Platform.OS === "ios" ? ["45%"] : ["48%"],
        double: Platform.OS === "ios" ? ["60%"] : ["66%"],
        triple: Platform.OS === "ios" ? ["77%"] : ["84%"],
        multiple: ["90%"],
    };

    if (isLoading || !isDataReady) return config.loading;
    if (orderCount === 0) return config.empty;
    if (orderCount <= 1) return config.single;
    if (orderCount <= 2) return config.double;
    if (orderCount <= 3 && !isSuspicious) return config.triple;
    return config.multiple;
};

/**
 * Gets Android-specific FlatList optimizations
 * @param orderCount Number of orders for optimization calculations
 * @returns Platform-specific optimization props
 */
export const getAndroidOptimizations = (orderCount: number): PlatformOptimizations => {
    if (Platform.OS !== "android") return {};

    const ITEM_HEIGHT = 120;
    const ITEM_SEPARATOR_HEIGHT = 12;

    return {
        getItemLayout: (_data: any, index: number) => ({
            length: ITEM_HEIGHT,
            offset: (ITEM_HEIGHT + ITEM_SEPARATOR_HEIGHT) * index,
            index,
        }),
        initialNumToRender: Math.min(orderCount, 5),
        maxToRenderPerBatch: 5,
        windowSize: 10,
        removeClippedSubviews: false,
        updateCellsBatchingPeriod: 100,
        legacyImplementation: false,
    };
};

/**
 * Creates a delay for Android platform to ensure proper layout
 * @param callback Function to execute after delay
 * @param delay Delay in milliseconds (default: 150)
 */
export const createAndroidDelay = (callback: () => void, delay: number = 150): void => {
    if (Platform.OS === "android") {
        setTimeout(callback, delay);
    } else {
        callback();
    }
};

/**
 * Resets UI-related state while preserving data
 * @returns Partial state object for resetting UI state
 */
export const getResetUIState = () => ({
    shouldDeleteOrders: false,
    selectedOrdersId: [],
    deleteId: undefined,
    modalVisible: false,
    modalMode: "delete" as const,
    dataReady: false,
});
