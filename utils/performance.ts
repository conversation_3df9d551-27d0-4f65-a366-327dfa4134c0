import { useCallback, useMemo, useRef } from 'react';

/**
 * Performance Utilities
 *
 * Collection of utilities and hooks for optimizing React Native performance
 */

// Debounce hook for expensive operations
export const useDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    }) as T,
    [callback, delay]
  );
};

// Throttle hook for high-frequency events
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastCallRef = useRef<number>(0);

  return useCallback(
    ((...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCallRef.current >= delay) {
        lastCallRef.current = now;
        callback(...args);
      }
    }) as T,
    [callback, delay]
  );
};

// Memoized style creator
export const useStyles = <T>(styleFactory: () => T, deps: any[]): T => {
  return useMemo(styleFactory, deps);
};

// Stable callback creator with dependency optimization
export const useStableCallback = <T extends (...args: any[]) => any>(
  callback: T,
  deps: any[]
): T => {
  return useCallback(callback, deps);
};

// Performance monitoring utilities
export const performanceUtils = {
  // Mark performance points
  mark: (name: string) => {
    if (__DEV__) {
      console.time(name);
    }
  },

  // Measure performance
  measure: (name: string) => {
    if (__DEV__) {
      console.timeEnd(name);
    }
  },

  // Log render count for debugging
  useRenderCount: (componentName: string) => {
    const renderCount = useRef(0);
    renderCount.current += 1;

    if (__DEV__) {
      console.log(`${componentName} rendered ${renderCount.current} times`);
    }
  },

  // Check if object has changed (shallow comparison)
  hasChanged: (prev: any, next: any): boolean => {
    if (prev === next) return false;

    if (typeof prev !== 'object' || typeof next !== 'object') {
      return prev !== next;
    }

    if (prev === null || next === null) {
      return prev !== next;
    }

    const prevKeys = Object.keys(prev);
    const nextKeys = Object.keys(next);

    if (prevKeys.length !== nextKeys.length) return true;

    for (const key of prevKeys) {
      if (prev[key] !== next[key]) return true;
    }

    return false;
  },
};

// FlatList optimization utilities
export const flatListOptimizations = {
  // Standard performance props for FlatList
  getOptimizedProps: (itemHeight?: number) => ({
    removeClippedSubviews: true,
    maxToRenderPerBatch: 10,
    updateCellsBatchingPeriod: 50,
    initialNumToRender: 10,
    windowSize: 10,
    ...(itemHeight && {
      getItemLayout: (_: any, index: number) => ({
        length: itemHeight,
        offset: itemHeight * index,
        index,
      }),
    }),
  }),

  // Optimized key extractor
  createKeyExtractor: <T extends { _id?: string; id?: string }>(prefix = 'item') =>
    (item: T, index: number): string => {
      return item._id || item.id || `${prefix}-${index}`;
    },
};

// Memory optimization utilities
export const memoryUtils = {
  // Clear large objects from memory
  clearObject: (obj: any) => {
    if (obj && typeof obj === 'object') {
      Object.keys(obj).forEach(key => {
        delete obj[key];
      });
    }
  },

  // Optimize image loading
  getOptimizedImageProps: (uri: string, size: { width: number; height: number }) => ({
    source: { uri },
    style: size,
    resizeMode: 'cover' as const,
    cache: 'force-cache' as const,
  }),
};

export default {
  useDebounce,
  useThrottle,
  useStyles,
  useStableCallback,
  performanceUtils,
  flatListOptimizations,
  memoryUtils,
};