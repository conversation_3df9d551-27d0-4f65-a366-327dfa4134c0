import { Order } from "@components/orders/types";
import _ from "lodash";

/**
 * Utility functions for order operations
 * Optimized for performance and reusability
 */
export class OrderUtils {
    /**
     * Find order by ID with optimized search
     */
    static findOrderById(orders: Order[], orderId: string): Order | undefined {
        return orders.find(order => order._id === orderId);
    }

    /**
     * Find order by reference number
     */
    static findOrderByReference(orders: Order[], reference: number): Order | undefined {
        return orders.find(order => order.reference === reference);
    }

    /**
     * Find order index by ID
     */
    static findOrderIndexById(orders: Order[], orderId: string): number {
        return orders.findIndex(order => order._id === orderId);
    }

    /**
     * Find order index by reference
     */
    static findOrderIndexByReference(orders: Order[], reference: number): number {
        return orders.findIndex(order => order.reference === reference);
    }

    /**
     * Check if order exists in list
     */
    static orderExists(orders: Order[], orderId: string): boolean {
        return orders.some(order => order._id === orderId);
    }

    /**
     * Check if order with reference exists
     */
    static orderWithReferenceExists(orders: Order[], reference: number): boolean {
        return orders.some(order => order.reference === reference);
    }

    /**
     * Add order to list if it doesn't exist
     * Returns updated list and boolean indicating if order was added
     */
    static addOrderIfNotExists(orders: Order[], newOrder: Order): { 
        orders: Order[], 
        wasAdded: boolean 
    } {
        const exists = this.orderExists(orders, newOrder._id) || 
                      this.orderWithReferenceExists(orders, newOrder.reference);
        
        if (exists) {
            console.log(`Order ${newOrder.reference} already exists, not adding duplicate`);
            return { orders, wasAdded: false };
        }

        return { 
            orders: [newOrder, ...orders], 
            wasAdded: true 
        };
    }

    /**
     * Update order in list by reference
     * Returns updated list and boolean indicating if order was found and updated
     */
    static updateOrderByReference(
        orders: Order[], 
        reference: number, 
        updates: Partial<Order>
    ): { orders: Order[], wasUpdated: boolean } {
        const orderIndex = this.findOrderIndexByReference(orders, reference);
        
        if (orderIndex === -1) {
            console.log(`Order with reference ${reference} not found in list of ${orders.length} orders`);
            return { orders, wasUpdated: false };
        }

        const currentOrder = orders[orderIndex];
        const updatedOrder = { ...currentOrder, ...updates };

        // Only update if something actually changed
        if (_.isEqual(currentOrder, updatedOrder)) {
            console.log("No changes detected in order");
            return { orders, wasUpdated: false };
        }

        const newOrders = [...orders];
        newOrders[orderIndex] = updatedOrder;

        console.log(`Order ${reference} updated in list. New count: ${newOrders.length}`);
        return { orders: newOrders, wasUpdated: true };
    }

    /**
     * Remove order from list by ID
     * Returns updated list and boolean indicating if order was found and removed
     */
    static removeOrderById(orders: Order[], orderId: string): { 
        orders: Order[], 
        wasRemoved: boolean,
        removedOrder?: Order 
    } {
        const orderToRemove = this.findOrderById(orders, orderId);
        
        if (!orderToRemove) {
            console.log(`Order with ID ${orderId} not found in list of ${orders.length} orders`);
            return { orders, wasRemoved: false };
        }

        const newOrders = orders.filter(order => order._id !== orderId);
        
        console.log(`Order ${orderToRemove.reference} removed from list. New count: ${newOrders.length}`);
        return { 
            orders: newOrders, 
            wasRemoved: true,
            removedOrder: orderToRemove
        };
    }

    /**
     * Update order status in list
     */
    static updateOrderStatus(
        orders: Order[], 
        orderId: string, 
        status: string
    ): { orders: Order[], wasUpdated: boolean } {
        const orderIndex = this.findOrderIndexById(orders, orderId);
        
        if (orderIndex === -1) {
            return { orders, wasUpdated: false };
        }

        const newOrders = [...orders];
        newOrders[orderIndex] = { 
            ...newOrders[orderIndex], 
            status: status as any 
        };

        return { orders: newOrders, wasUpdated: true };
    }

    /**
     * Insert order at correct position based on reference (descending order)
     */
    static insertOrderAtCorrectPosition(orders: Order[], newOrder: Order): Order[] {
        if (orders.length === 0) {
            return [newOrder];
        }

        const highestRef = orders[0].reference;
        const lowestRef = orders[orders.length - 1].reference;

        // If order reference is higher than the highest, add at beginning
        if (newOrder.reference > highestRef) {
            return [newOrder, ...orders];
        }

        // If order reference is lower than the lowest, add at end
        if (newOrder.reference < lowestRef) {
            return [...orders, newOrder];
        }

        // Find correct position to insert
        let insertIndex = 0;
        while (
            insertIndex < orders.length &&
            orders[insertIndex].reference > newOrder.reference
        ) {
            insertIndex++;
        }

        const newOrders = [...orders];
        newOrders.splice(insertIndex, 0, newOrder);
        
        return newOrders;
    }

    /**
     * Check if order is within reference range of current list
     */
    static isOrderInRange(orders: Order[], orderReference: number): boolean {
        if (orders.length === 0) return true;
        
        const highestRef = orders[0].reference;
        const lowestRef = orders[orders.length - 1].reference;
        
        return orderReference <= highestRef && orderReference >= lowestRef;
    }

    /**
     * Merge order updates into existing list
     */
    static mergeOrderUpdates(orders: Order[], updatedOrders: Order[]): Order[] {
        return orders.map(order => {
            const updatedOrder = updatedOrders.find(
                updated => updated.reference === order.reference
            );
            return updatedOrder ? { ...order, ...updatedOrder } : order;
        });
    }

    /**
     * Validate order data
     */
    static validateOrder(order: Partial<Order>): { isValid: boolean, errors: string[] } {
        const errors: string[] = [];

        if (!order._id) {
            errors.push("Order ID is required");
        }

        if (!order.reference) {
            errors.push("Order reference is required");
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
