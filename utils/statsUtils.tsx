import api from "@api/api";
import {
    ApiStatsProps,
    FormatedProductProps,
    FormatedDeliveryProps,
    FormatedAdminsProps,
} from "../store/statsStoreProps";
import { router } from "expo-router";

export function formatDateToYYYYMMDD(date: Date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based, so we add 1.
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
}
export function getPeriod(nDays: number): { from: string; to: string } {
    const toDate = new Date();
    const fromDate = new Date(toDate.getTime() - (nDays - 1) * 24 * 60 * 60 * 1000);

    const formatDate = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
    };

    return {
        from: formatDate(fromDate),
        to: formatDate(toDate),
    };
}

export const fetchStats = async ({ from, to }: { from: string; to: string }) => {
    try {
        console.log("fetching stats...", { from, to });
        const response: ApiStatsProps = await api.get(`/statistics?from=${from}&to=${to}`);
        console.log(response.stats);
        if (!response?.stats) {
            router.navigate("/network-error");
            throw new Error("Invalid response format");
        }

        const { stats } = response;

        // Initialize default structures
        const defaultStats = {
            products: { store: {} },
            delivery: { store: {} },
            admins: { store: {} },
        };

        const adminStatsEntries = stats.map((entry) => ({
            date: entry.date,
            values: entry.adminStats || {},
        }));

        const deliveryStatsEntries = stats.map((entry) => ({
            date: entry.date,
            values: entry.deliveryStats || {},
        }));
        // console.log("deliveryStatsEntries=>", JSON.stringify(deliveryStatsEntries));

        const productsStatsEntries = stats.map((entry) => ({
            date: entry.date,
            values: entry.productStats || {},
        }));

        const getAllProducts = () => {
            const productNames = new Set<string>();
            productsStatsEntries.forEach((entry) => {
                Object.keys(entry.values).forEach((name) => productNames.add(name));
            });
            return Array.from(productNames);
        };

        const getAllDeliveryCompanyies = () => {
            const companies = new Set<string>();
            deliveryStatsEntries?.forEach((entry) => {
                Object.keys(entry.values).forEach((name) => companies.add(name));
            });
            return Array.from(companies);
        };

        const getAllAdmins = () => {
            const admins = new Set<string>();
            adminStatsEntries.forEach((entry) => {
                Object.keys(entry.values).forEach((name) => admins.add(name));
            });
            return Array.from(admins);
        };

        const currentDate = new Date();
        const defaultDate = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;

        const defaultProductStats = {
            deposit: 0,
            depositValue: 0,
            depositProfit: 0,
            transit: 0,
            transitValue: 0,
            transitProfit: 0,
            delivered: 0,
            deliveredValue: 0,
            deliveredProfit: 0,
            returned: 0,
            returnedCost: 0,
            unverified: 0,
            total: 0,
            totalValue: 0,
            totalProfit: 0,
        };

        const defaultDeliveryStats = {
            deliveredOrders: 0,
            deliveredOrdersValue: 0,
            deliveredOrdersPayment: 0,
            deliveredOrdersProfit: 0,
            returnedOrders: 0,
            returnedOrdersCost: 0,
            depositOrders: 0,
            depositOrdersValue: 0,
            depositOrdersPayment: 0,
            depositOrdersProfit: 0,
            inTransitOrders: 0,
            inTransitOrdersValue: 0,
            inTransitOrdersPayment: 0,
            inTransitOrdersProfit: 0,
            unverifiedOrders: 0,
            avgDeliveryTime: 0,
        };

        const defaultAdminStats = {
            confirmedOrders: 0,
            rejectedOrders: 0,
            createdOrders: 0,
            deletedOrders: 0,
            receivedOrders: 0,
            packedOrders: 0,
            attempts: 0,
            firstAttempts: 0,
            avgConfirmationTime: 0,
            avgTimeToFirstAttempt: 0,
            rejectionReasons: {},
        };

        const filterProductsStatsByName = (filters: string[]) => {
            const res: FormatedProductProps = { store: {} };
            filters.forEach((filter) => {
                res[filter] = {};
                productsStatsEntries.forEach((statEntry) => {
                    res[filter][statEntry.date] = statEntry.values[filter] || { ...defaultProductStats };
                });
            });

            if (filters.length === 0) {
                res.store[defaultDate] = { ...defaultProductStats };
            }
            return res;
        };

        const fliterDeliveryByCompany = (filters: string[]) => {
            const res: FormatedDeliveryProps = { store: {} };
            filters?.forEach((filter) => {
                res[filter] = {};
                deliveryStatsEntries?.forEach((statEntry) => {
                    res[filter][statEntry.date] = statEntry.values[filter] || { ...defaultDeliveryStats };
                });
            });

            if (filters.length === 0) {
                res.store[defaultDate] = { ...defaultDeliveryStats };
            }
            return res;
        };

        const filterAdminDatabyAdmin = (filters: string[]) => {
            const res: FormatedAdminsProps = { store: {} };
            filters.forEach((filter) => {
                res[filter] = {};
                adminStatsEntries.forEach((statEntry) => {
                    res[filter][statEntry.date] = statEntry.values[filter] || { ...defaultAdminStats };
                });
            });

            if (filters.length === 0) {
                res.store[defaultDate] = { ...defaultAdminStats };
            }
            return res;
        };

        return {
            products: filterProductsStatsByName(getAllProducts()),
            delivery: fliterDeliveryByCompany(getAllDeliveryCompanyies()),
            admins: filterAdminDatabyAdmin(getAllAdmins()),
        };
    } catch (error) {
        console.error("Stats fetch error:", error);
        router.navigate("/network-error");
        return {
            products: { store: {} },
            delivery: { store: {} },
            admins: { store: {} },
        };
    }
};

export function getDaysSinceStartOfWeek(): number {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 (Sun), 1 (Mon), ..., 6 (Sat)
    const daysSinceMonday = (dayOfWeek + 6) % 7; // Adjust so that Monday is 0
    return daysSinceMonday + 1; // Add 1 to count today
}

export function getDaysSinceStartOfMonth(): number {
    const today = new Date();
    const dayOfMonth = today.getDate(); // 1 for the first day of the month, ..., 31 for the last day of some months
    return dayOfMonth; // Return the current day of the month
}

//! we don't talk about whath happened on November 13
export function getDaysSinceNovember13(): number {
    const today = new Date();
    const currentYear = today.getFullYear();
    const november13 = new Date(2023, 10, 13); // Month is 0-indexed, so 10 represents November

    const timeDifference = today.getTime() - november13.getTime();
    const dayDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));

    return dayDifference;
}
