{"expo": {"name": "Converty Shop", "slug": "converty", "version": "2.3.0", "platforms": ["ios", "android"], "orientation": "portrait", "icon": "./assets/splash/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "newArchEnabled": true, "owner": "converty", "extra": {"eas": {"projectId": "b7738d99-23a6-477c-86ec-35369168bb33"}}, "scheme": "converty", "assetBundlePatterns": ["**/*"], "ios": {"requireFullScreen": true, "buildNumber": "7", "supportsTablet": true, "incrementBuildNumber": true, "bundleIdentifier": "com.converty.app", "googleServicesFile": "./FirebaseCloudMessaging/GoogleService-Info.plist", "associatedDomains": ["webcredentials:partner.converty.shop", "applinks:partner.converty.shop?mode=developer"], "infoPlist": {"UIViewControllerBasedStatusBarAppearance": true, "ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/splash/adaptive-icon.png", "backgroundColor": "#ffffff"}, "incrementVersionCode": true, "package": "com.converty.app", "googleServicesFile": "./FirebaseCloudMessaging/google-services.json", "versionCode": 23, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"], "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "partner.converty.shop", "pathPrefix": "/"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"favicon": "./assets/favicon.png"}, "experiments": {"typedRoutes": true}, "plugins": ["expo-router", ["expo-build-properties", {"android": {"minSdkVersion": 24, "compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0"}}], ["expo-screen-orientation", {"initialOrientation": "DEFAULT"}], ["expo-font", {"fonts": ["./assets/fonts/Inter/Inter-Black.ttf", "./assets/fonts/Inter/Inter-Bold.ttf", "./assets/fonts/Inter/Inter-ExtraBold.ttf", "./assets/fonts/Inter/Inter-ExtraLight.ttf", "./assets/fonts/Inter/Inter-Light.ttf", "./assets/fonts/Inter/Inter-Medium.ttf", "./assets/fonts/Inter/Inter-Regular.ttf", "./assets/fonts/Inter/Inter-SemiBold.ttf", "./assets/fonts/Inter/Inter-Thin.ttf", "./assets/fonts/Libre-Barcode-39/LibreBarcode39Text-Regular.ttf"]}], ["expo-camera", {"cameraPermission": "$(PRODUCT_NAME) uses your camera to scan order QR codes, which can be found on order labels exported from Converty.", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], "expo-secure-store", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you update your profile picture."}], ["expo-notifications", {"icon": "./assets/splash/ic_notification.png", "color": "#682eb0", "sounds": ["./assets/sounds/ahmed_mohsen.wav", "./assets/sounds/chaching.wav", "./assets/sounds/siuu.wav"]}]]}}