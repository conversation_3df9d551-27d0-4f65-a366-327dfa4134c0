import { io, Socket } from "socket.io-client";
import URL from "@api/url";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Toast from "react-native-toast-message";
import { Order } from "@components/orders/types";
import { Store } from "../types/Store";
import { Platform } from "react-native";

export type SocketInfo = {
    eventId?: string;
    eventTimestamp?: number;
};

export type SocketActionPayload = (info: SocketInfo, order: Order, sender?: any) => any;

class SocketManager {
    private static instance: SocketManager;
    private socket: Socket;
    private listeners: Map<string, Function[]> = new Map();
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 10;
    private reconnectInterval: number = 5000; // 5 seconds
    private reconnectTimer: NodeJS.Timeout | null = null;
    private isConnected: boolean = false;

    private constructor() {
        const socketOptions = {
            withCredentials: true,
            transports: ["websocket"], //Platform.OS === 'ios' ? ['websocket', 'polling'] : ['websocket'], // Allow fallback for iOS
            reconnection: true,
            reconnectionAttempts: this.maxReconnectAttempts,
            reconnectionDelay: this.reconnectInterval,
            timeout: Platform.OS === "ios" ? 30000 : 20000, // Longer timeout for iOS production
            forceNew: false,
            upgrade: true,
            rememberUpgrade: true,
        };

        this.socket = io(URL, socketOptions);
        this.setupSocketListeners();
    }

    public static getInstance(): SocketManager {
        if (!SocketManager.instance) {
            SocketManager.instance = new SocketManager();
        }
        return SocketManager.instance;
    }

    private setupSocketListeners(): void {
        // Connection events - handled by socket manager
        this.socket.on("connect", this.handleConnect.bind(this));
        this.socket.on("disconnect", this.handleDisconnect.bind(this));
        this.socket.on("connect_error", this.handleConnectError.bind(this));
        this.socket.io.on("reconnect_attempt", this.handleReconnectAttempt.bind(this));
        this.socket.io.on("reconnect_failed", this.handleReconnectFailed.bind(this));

        // Order and store events will be handled by custom hook
    }

    private async handleConnect(): Promise<void> {
        console.log("Socket connected");
        this.isConnected = true;
        this.reconnectAttempts = 0;

        // Join room with user image
        const userImage = await AsyncStorage.getItem("user-image");
        this.socket.emit("joinRoom", userImage);

        // Notify listeners
        this.notifyListeners("connect");
    }

    private handleDisconnect(): void {
        console.log("Socket disconnected");
        this.isConnected = false;
        this.notifyListeners("disconnect");

        // // Enhanced reconnection logic for iOS production builds
        // if (Platform.OS === 'ios') {
        // Add a small delay before attempting reconnection in iOS
        setTimeout(() => {
            this.attemptReconnect();
        }, 1000);
        // }
    }

    private handleConnectError(error: Error): void {
        console.log("Socket connection error:", error.message);

        // Set connection state to false
        this.isConnected = false;

        // Trigger reconnection for both iOS and Android
        if (Platform.OS === "ios") {
            // More aggressive reconnection with exponential backoff for iOS
            const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 10000);
            setTimeout(() => {
                this.attemptReconnect();
            }, delay);
        } else {
            // Standard reconnection for Android with shorter delay
            const delay = 2000; // 2 second delay for Android
            setTimeout(() => {
                this.attemptReconnect();
            }, delay);
        }
    }

    private handleReconnectAttempt(attempt: number): void {
        console.log(`Socket reconnection attempt ${attempt}`);
    }

    private handleReconnectFailed(): void {
        console.log("Socket reconnection failed");
        Toast.show({
            type: "error",
            text1: "Connection Lost",
            text2: "Unable to connect to server. Please check your internet connection.",
        });
    }

    // Order and store event handlers will be managed by custom hook

    private attemptReconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }

        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(
                `Attempting to reconnect ; ${Platform.OS} (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`
            );

            // Use exponential backoff for iOS production builds
            const delay =
                Platform.OS === "ios"
                    ? Math.min(this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1), 30000)
                    : this.reconnectInterval;

            this.reconnectTimer = setTimeout(() => {
                if (!this.isConnected) {
                    try {
                        // Force disconnect before reconnecting for iOS
                        if (Platform.OS === "ios") {
                            this.socket.disconnect();
                            setTimeout(() => {
                                this.socket.connect();
                            }, 500);
                        } else {
                            this.socket.connect();
                        }
                    } catch (error) {
                        console.error("Reconnection error:", error);
                    }
                }
            }, delay);
        } else {
            console.log("Max reconnection attempts reached");
            // Reset attempts after a longer delay for iOS
            if (Platform.OS === "ios") {
                setTimeout(() => {
                    this.reconnectAttempts = 0;
                }, 60000); // Reset after 1 minute
            }
        }
    }

    public on(event: string, callback: (...args: any[]) => void): void {
        // Register with socket.io
        this.socket.on(event, callback);

        // Store in our listeners map for management
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event)?.push(callback);
    }

    public off(event: string, callback?: Function): void {
        if (callback) {
            // Remove specific callback
            this.socket.off(event, callback as any);

            // Update our listeners map
            const callbacks = this.listeners.get(event) || [];
            const index = callbacks.indexOf(callback);
            if (index !== -1) {
                callbacks.splice(index, 1);
                this.listeners.set(event, callbacks);
            }
        } else {
            // Remove all callbacks for this event
            this.socket.off(event);
            this.listeners.delete(event);
        }
    }

    public emit(event: string, ...args: any[]): void {
        this.socket.emit(event, ...args);
    }

    private notifyListeners(event: string): void {
        const callbacks = this.listeners.get(event) || [];
        callbacks.forEach((callback) => {
            try {
                callback();
            } catch (error) {
                console.error(`Error in ${event} listener:`, error);
            }
        });
    }

    public isSocketConnected(): boolean {
        return this.isConnected;
    }

    // Public methods for custom hook to register order and store listeners
    public onOrderCreated(callback: SocketActionPayload): void {
        this.on("orderCreated", callback);
    }

    public onOrderUpdated(callback: SocketActionPayload): void {
        this.on("orderUpdated", callback);
    }

    public onOrderDeleted(callback: SocketActionPayload): void {
        this.on("orderDeleted", callback);
    }

    public onOrderRestored(callback: SocketActionPayload): void {
        this.on("orderRestored", callback);
    }

    public onOrdersUpdated(callback: (info: SocketInfo, orders: Order[], sender: any) => any): void {
        this.on("ordersUpdated", callback);
    }

    public onStoreUpdated(callback: (info: SocketInfo, data: Partial<Store>) => any): void {
        this.on("storeUpdated", callback);
    }

    // Cleanup all listeners
    public cleanup(): void {
        this.listeners.forEach((_, event) => {
            this.socket.off(event);
        });
        this.listeners.clear();
    }
}

export default SocketManager;
