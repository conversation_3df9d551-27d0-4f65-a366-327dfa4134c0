// api.ts

import { loadCookie, useAuthStore } from "../store/authStore";
import URL from "./url";
import * as SecureStore from "expo-secure-store";

const baseUrl = `${URL}/api/v1`;

const api = {
    // TODO: Handle the return types of these methods and the errors
    get: async (endpoint: string, headers = {}) => {
        // try {
        const authCookie = SecureStore.getItem("auth-cookie");

        const response = await fetch(`${baseUrl}${endpoint}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                // Cookie: useAuthStore.getState().cookie,
                ...headers,
                "set-cookie": authCookie,
            },
            credentials: "include",
        });

        let result = await response.json();

        if (!response.ok) {
            throw { message: "get:" + endpoint + " " + result.message, status: response.status };
        }

        return { ...result, headers: response.headers };
        // } catch (error: any) {
        //     console.log(error);
        //     throw error.message;
        // }
    },

    post: async (endpoint: string, body = {}, headers = {}) => {
        const authCookie = await loadCookie();

        const response = await fetch(`${baseUrl}${endpoint}`, {
            method: "POST",
            headers: {
                ...headers,
                "Content-Type": "application/json",
                "set-cookie": authCookie,
                // Cookie: useAuthStore.getState().cookie,
            },
            body: JSON.stringify(body),
            credentials: "include",
        });

        let result = await response.json();

        if (!response.ok) {
            throw { message: "post:" + endpoint + " " + result.message, status: response.status };
        }

        return { ...result, headers: response.headers };
    },

    patch: async (endpoint = "string", body = {}, headers = {}) => {
        const authCookie = await loadCookie();

        const response = await fetch(`${baseUrl}${endpoint}`, {
            method: "PATCH",
            headers: {
                ...headers,
                "Content-Type": "application/json",
                "set-cookie": await loadCookie(),
            },
            body: JSON.stringify(body),
            credentials: "include",
        });

        let result = await response.json();

        if (!response.ok) {
            throw { message: "patch:" + endpoint + " " + result.message, status: response.status };
        }

        return { ...result, headers: response.headers };
    },

    delete: async (endpoint: string, body = {}, headers = {}) => {
        // const authCookie = await loadCookie();

        const response = await fetch(`${baseUrl}${endpoint}`, {
            method: "DELETE",
            headers: {
                ...headers,
                "Content-Type": "application/json",
                "set-cookie": await loadCookie(),
            },
            body: JSON.stringify(body),
            credentials: "include",
        });

        let result = await response.json();

        if (!response.ok) {
            throw { message: "delete:" + endpoint + " " + result.message, status: response.status };
        }

        return { ...result, headers: response.headers };
    },
};

export default api;
