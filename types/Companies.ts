const CDN = "https://cdn.converty.shop/";

const algerianCitites = [
    "Adrar",
    "Chlef",
    "Laghouat",
    "Oum El Bouaghi",
    "Batna",
    "Bejaia",
    "Biskra",
    "Bechar",
    "Blida",
    "Bouira",
    "Tamanrasset",
    "Tebessa",
    "Tlemcen",
    "Tiaret",
    "Tizi Ouzou",
    "Alger",
    "Djelfa",
    "Jijel",
    "Setif",
    "Saida",
    "Skikda",
    "Sidi Bel Abbes",
    "Annaba",
    "Guelma",
    "Constantine",
    "Medea",
    "Mostaganem",
    "M'Sila",
    "Mascara",
    "Ouargla",
    "Oran",
    "El Bayadh",
    "Illizi",
    "Bordj Bou Arreridj",
    "Boumerdes",
    "El Tarf",
    "Tindouf",
    "Tissemsilt",
    "El Oued",
    "Khenchela",
    "Souk Ahras",
    "Tipaza",
    "Mila",
    "Ain Defla",
    "Naama",
    "Ain Temouchent",
    "Ghardaia",
    "Relizane",
    "<PERSON><PERSON>un",
    "Bordj Badji Mokhtar",
    "Ouled Djellal",
    "Beni Abbes",
    "In Salah",
    "In Guezzam",
    "Touggourt",
    "Djanet",
    "El M ghaier",
    "El Meniaa",
];

export const companies = [
    {
        actionName: "Integrate",
        name: "Personal Delivery",
        ref: "personal",
        allowedCountries: ["TN", "DZ"],
        shortDescription: "Print your own shipping Labels and deliver your orders 🚚",
        logo: CDN + "assets/integrations/personal-delivery-v2.webp",
    },
    {
        actionName: "Integrate",
        name: "First Delivery",
        ref: "first",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/first.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "FirstDeliveryKey",
                type: "textarea",
                placeholder: "First Delivery Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name (For First Delivery)",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone (For First Delivery)",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address (For First Delivery)",
                parent: "Label",
            },
            {
                name: "storeCity",
                type: "select",
                placeholder: "Store City (For First Delivery)",
                options: [
                    "Ariana",
                    "Beja",
                    "Ben Arous",
                    "Bizerte",
                    "Gabes",
                    "Gafsa",
                    "Jendouba",
                    "Kasserine",
                    "Kef",
                    "Mahdia",
                    "Manouba",
                    "Monastir",
                    "Nabeul",
                    "Sfax",
                    "Sidi Bouzid",
                    "Sousse",
                    "Siliana",
                    "Tataouine",
                    "Tozeur",
                    "Tunis",
                    "Zaghouan",
                    "Medenine",
                    "Kebili",
                    "Kairouan",
                ],
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Mylerz",
        ref: "mylerz",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/mylerz.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "MylerzKey",
                type: "textarea",
                placeholder: "Mylerz Key",
                required: true,
                parent: "Key",
            },
            {
                name: "warehouseName",
                type: "text",
                placeholder: "Warehouse Name (optional)",
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "specialNote",
                type: "textarea",
                placeholder: "Special Note...",
                parent: "Label",
            },
            {
                name: "allowToOpenPackage",
                type: "select",
                placeholder: "Allow to open package",
                options: ["Yes", "No"],
                parent: "Label",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name (For Mylerz)",
                parent: "Label",
            },
            {
                name: "storeCity",
                type: "select",
                placeholder: "Store City (For Mylerz)",
                parent: "Label",
                options: [
                    "Ariana",
                    "Beja",
                    "Ben Arous",
                    "Bizerte",
                    "Gabes",
                    "Gafsa",
                    "Jendouba",
                    "Kasserine",
                    "Kef",
                    "Mahdia",
                    "Manouba",
                    "Monastir",
                    "Nabeul",
                    "Sfax",
                    "Sidi Bouzid",
                    "Sousse",
                    "Siliana",
                    "Tataouine",
                    "Tozeur",
                    "Tunis",
                    "Zaghouan",
                    "Medenine",
                    "Kebili",
                    "Kairouan",
                ],
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Intigo",
        ref: "intigo",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/intigo.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "key",
                type: "textarea",
                placeholder: "Intigo Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "address",
                type: "text",
                placeholder: "Address",
                parent: "Label",
            },
            {
                name: "specialNote",
                type: "textarea",
                placeholder: "Special Note...",
                parent: "Label",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name (For Intigo Label)",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Jax",
        ref: "jax",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/jax.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "clientId",
                type: "text",
                placeholder: "Client ID",
                required: true,
                parent: "Key",
            },
            {
                name: "key",
                type: "textarea",
                placeholder: "Jax Key",
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Adex",
        ref: "adex",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/adex.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Adex Username",
                required: true,
                parent: "Credentials",
            },
            {
                name: "password",
                type: "text",
                placeholder: "Adex Password",
                required: true,
                parent: "Credentials",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "La Zajella",
        ref: "zajella",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/zajella.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Zajella Username",
                required: true,
                parent: "Key",
            },
            {
                name: "key",
                type: "textarea",
                placeholder: "Zajella Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Navex",
        ref: "navex",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/navex.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "key",
                type: "textarea",
                placeholder: "Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "openPackage",
                type: "select",
                placeholder: "Open package",
                options: ["Yes", "No"],
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Jetpack",
        ref: "jetpack",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/jetpack.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "key",
                type: "textarea",
                placeholder: "Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Best Delivery",
        ref: "bestdelivery",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/bestdelivery.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Best Delivery Username",
                required: true,
                parent: "Credentials",
            },
            {
                name: "password",
                type: "text",
                placeholder: "Best Delivery Password",
                required: true,
                parent: "Credentials",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name (For Best Delivery)",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone (For Best Delivery)",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address (For Best Delivery)",
                parent: "Label",
            },
            {
                name: "storeCity",
                type: "select",
                placeholder: "Store City (For Best Delivery)",
                options: [
                    "Ariana",
                    "Beja",
                    "Ben Arous",
                    "Bizerte",
                    "Gabes",
                    "Gafsa",
                    "Jendouba",
                    "Kasserine",
                    "Kef",
                    "Mahdia",
                    "Manouba",
                    "Monastir",
                    "Nabeul",
                    "Sfax",
                    "Sidi Bouzid",
                    "Sousse",
                    "Siliana",
                    "Tataouine",
                    "Tozeur",
                    "Tunis",
                    "Zaghouan",
                    "Medenine",
                    "Kebili",
                    "Kairouan",
                ],
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Zed Delivery",
        ref: "zed",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/zed.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Zed Username",
                required: true,
                parent: "Key",
            },
            {
                name: "key",
                type: "textarea",
                placeholder: "Zed Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Axess Delivery",
        ref: "axess",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅  Labels",
        logo: CDN + "assets/integrations/axess.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "token",
                type: "textarea",
                placeholder: "Token",
                required: true,
                parent: "Key",
            },
            {
                name: "idEntrepot",
                type: "number",
                placeholder: "Id Entrepot",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Sellmax",
        ref: "sellmax",
        shortDescription: `✅ Add Order | ✅ Add Lead | ❌ Tracking`,
        logo: CDN + "assets/integrations/sellmax.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN", "SA"],
    },
    {
        actionName: "Integrate",
        name: "Shipper",
        ref: "shipper",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅  Labels",
        logo: CDN + "assets/integrations/shipper.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
    },
    {
        actionName: "Integrate",
        name: "Droppex",
        ref: "droppex",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/droppex.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "code",
                type: "text",
                placeholder: "Code API",
                required: true,
                parent: "Key",
            },
            {
                name: "cle",
                type: "text",
                placeholder: "Cle API",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone ",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address ",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Colissimo",
        ref: "colissimo",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/colissimo.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Colissimo Username",
                required: true,
                parent: "Key",
            },
            {
                name: "key",
                type: "text",
                placeholder: "Colissimo Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone ",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address ",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Safexpress",
        ref: "safexpress",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/safexpress.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Safexpress Username",
                required: true,
                parent: "Key",
            },
            {
                name: "key",
                type: "textarea",
                placeholder: "Safexpress Key",
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Colis Express",
        ref: "colisexpress",
        shortDescription: "✅ Add Order | ❌ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/colisexpress.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "id",
                type: "text",
                placeholder: "ID",
                required: true,
                parent: "Key",
            },
            {
                name: "cle",
                type: "text",
                placeholder: "Cle API",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone ",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address ",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Bestway Delivery",
        ref: "bestway",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/bestway.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Bestway Username",
                required: true,
                parent: "Credentials",
            },
            {
                name: "password",
                type: "text",
                placeholder: "Bestway Password",
                required: true,
                parent: "Credentials",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "allowToOpenPackage",
                type: "select",
                placeholder: "Allow to open package",
                options: ["Yes", "No"],
                parent: "Label",
            },
            {
                name: "fragile",
                type: "select",
                placeholder: "Fragile",
                options: ["Yes", "No"],
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Storelinkers",
        ref: "storelinkers",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/storelinkers.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Storelinkers Username",
                required: true,
                parent: "Credentials",
            },
            {
                name: "password",
                type: "text",
                placeholder: "Storelinkers Password",
                required: true,
                parent: "Credentials",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Yalidine",
        ref: "yalidine",
        shortDescription: "✅ Add Order | ✅ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/yalidine.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["DZ"],
        fields: [
            {
                name: "id",
                type: "text",
                placeholder: "Yalidine ID",
                required: true,
                parent: "Key",
            },
            {
                name: "token",
                type: "text",
                placeholder: "Yalidine Token",
                required: true,
                parent: "Key",
            },
            {
                name: "costPreset",
                type: "dz-wilaya-preset-selector",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "wilaya",
                type: "select",
                placeholder: "Wilaya",
                options: algerianCitites,
                parent: "Extra",
            },
            {
                name: "insurance",
                type: "select",
                placeholder: "Yes or No",
                options: ["yes", "no"],
                parent: "Extra",
            },
        ],
        // guide: `<div style={{ color: "var(--black)" }}><h1>How can you integrate Yalidine?</h1><p>To integrate Yalidine, simply copy the API ID and token provided and paste them where required.</p></div>`,
    },
    {
        actionName: "Integrate",
        name: "Guepex",
        ref: "guepex",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/gupex.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["DZ"],
        fields: [
            {
                name: "id",
                type: "text",
                placeholder: "Guepex ID",
                required: true,
                parent: "Key",
            },
            {
                name: "token",
                type: "text",
                placeholder: "Guepex Token",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
        // guide: `<div style={{ color: "var(--black)" }}><h1>How can you integrate Gupex?</h1><p>To integrate Gupex, simply copy the API ID and token provided and paste them where required.</p></div>`,
    },
    {
        actionName: "Integrate",
        name: "Colicom",
        ref: "colicom",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/colicom.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "key",
                type: "textarea",
                placeholder: "API Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name ",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone ",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "storeCity",
                type: "select",
                placeholder: "Store City",
                options: [
                    "Ariana",
                    "Beja",
                    "Ben Arous",
                    "Bizerte",
                    "Gabes",
                    "Gafsa",
                    "Jendouba",
                    "Kasserine",
                    "Kef",
                    "Mahdia",
                    "Manouba",
                    "Monastir",
                    "Nabeul",
                    "Sfax",
                    "Sidi Bouzid",
                    "Sousse",
                    "Siliana",
                    "Tataouine",
                    "Tozeur",
                    "Tunis",
                    "Zaghouan",
                    "Medenine",
                    "Kebili",
                    "Kairouan",
                ],
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Aramex",
        ref: "aramex",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/aramex.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Username",
                required: true,
                parent: "Key",
            },
            {
                name: "password",
                type: "text",
                placeholder: "Password",
                required: true,
                parent: "Key",
            },
            {
                name: "accountPin",
                type: "text",
                placeholder: "Account Pin",
                required: true,
                parent: "Key",
            },
            {
                name: "accountNumber",
                type: "text",
                placeholder: "Account Number",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name (For Aramex)",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone (For Aramex)",
                parent: "Label",
            },
            {
                name: "storeCity",
                type: "select",
                placeholder: "Store City (For Aramex)",
                parent: "Label",
                options: [
                    "Ariana",
                    "Beja",
                    "Ben Arous",
                    "Bizerte",
                    "Gabes",
                    "Gafsa",
                    "Jendouba",
                    "Kasserine",
                    "Kef",
                    "Mahdia",
                    "Manouba",
                    "Monastir",
                    "Nabeul",
                    "Sfax",
                    "Sidi Bouzid",
                    "Sousse",
                    "Siliana",
                    "Tataouine",
                    "Tozeur",
                    "Tunis",
                    "Zaghouan",
                    "Medenine",
                    "Kebili",
                    "Kairouan",
                ],
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "ZRexpress",
        ref: "ZRexpress",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/ZRexpress.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["DZ"],
        fields: [
            {
                name: "token",
                type: "textarea",
                placeholder: "ZRexpress Token",
                required: true,
                parent: "Key",
            },
            {
                name: "key",
                type: "text",
                placeholder: "ZRexpress KEY",
                required: true,
                parent: "Key",
            },
            {
                name: "costPreset",
                type: "dz-wilaya-preset-selector",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },

            {
                name: "wilaya",
                type: "select",
                placeholder: "Wilaya",
                options: algerianCitites,
                parent: "Label",
            },
            {
                name: "commune",
                type: "text",
                placeholder: "Store Commune",

                parent: "Label",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name ",
                parent: "Label",
            },
        ],
        // guide: `<div style={{ color: "var(--black)" }}><h1>How can you integrate ZRexpress?</h1><p>To integrate ZRexpress, simply copy the API ID and token provided and paste them where required.</p></div>`,
    },
    {
        actionName: "Integrate",
        name: "Trust Delivery",
        ref: "trustdelivery",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅  Labels",
        logo: CDN + "assets/integrations/trustdelivery.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Trust Username",
                required: true,
                parent: "Key",
            },
            {
                name: "key",
                type: "textarea",
                placeholder: "Trust Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "B2C Delivery",
        ref: "btocdelivery",
        shortDescription: "✅ Add Order | ✅ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/btocdelivery.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "token",
                type: "textarea",
                placeholder: "api token",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "NOEST Express",
        ref: "noest",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/noest.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["DZ"],
        fields: [
            {
                name: "api_token",
                type: "text",
                placeholder: "NOEST Api Token",
                required: true,
                parent: "Key",
            },
            {
                name: "user_guid",
                type: "text",
                placeholder: "NOEST User Guid",
                required: true,
                parent: "Key",
            },
            {
                name: "costPreset",
                type: "dz-wilaya-preset-selector",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "wilaya",
                type: "select",
                placeholder: "Wilaya",
                options: algerianCitites,
                parent: "Label",
            },
            {
                name: "commune",
                type: "text",
                placeholder: "Store Commune",

                parent: "Label",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name ",
                parent: "Label",
            },
        ],
        // guide: `<div style={{ color: "var(--black)" }}><h1>How can you integrate ZRexpress?</h1><p>To integrate ZRexpress, simply copy the API ID and token provided and paste them where required.</p></div>`,
    },
    {
        actionName: "Integrate",
        name: "Maystro",
        ref: "maystro",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅  Labels",
        logo: CDN + "assets/integrations/maystro.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["DZ"],
        fields: [
            {
                name: "token",
                type: "text",
                placeholder: "Maystro Api Token",
                required: true,
                parent: "Key",
            },
            {
                name: "costPreset",
                type: "dz-wilaya-preset-selector",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "Wilaya",
                type: "select",
                placeholder: "Wilaya",
                options: algerianCitites,
                parent: "Label",
            },
            {
                name: "Commune",
                type: "text",
                placeholder: "Store Commune",

                parent: "Label",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name ",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "OClock Delivery",
        ref: "oclock",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/oclock.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "OClock Username",
                required: true,
                parent: "Key",
            },
            {
                name: "key",
                type: "textarea",
                placeholder: "OClock Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            // {
            //     name: "storeName",
            //     type: "text",
            //     placeholder: "Store Name",
            //     parent: "Label",
            // },
            // {
            //     name: "storePhone",
            //     type: "text",
            //     placeholder: "Store Phone",
            //     parent: "Label",
            // },
            // {
            //     name: "storeAddress",
            //     type: "text",
            //     placeholder: "Store Address",
            //     parent: "Label",
            // },
            // {
            //     name: "vatNumber",
            //     type: "text",
            //     placeholder: "Store Matricule Fiscale",
            //     parent: "Label",
            // },
        ],
    },
    {
        actionName: "Integrate",
        name: "Goodex",
        ref: "goodex",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/goodex.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "id",
                type: "text",
                placeholder: "ID",
                required: true,
                parent: "Key",
            },
            {
                name: "token",
                type: "text",
                placeholder: "Token",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name ",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone ",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Afex",
        ref: "afex",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/afex.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "api_key",
                type: "text",
                placeholder: "API KEY",
                required: true,
                parent: "Key",
            },

            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Onesta",
        ref: "onesta",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/onesta.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "token",
                type: "text",
                placeholder: "Token",
                required: true,
                parent: "Key",
            },
            {
                name: "passwork",
                type: "text",
                placeholder: "PassworK",
                required: true,
                parent: "Key",
            },
            {
                name: "fournisseur",
                type: "text",
                placeholder: "Fournisseur",
                required: true,
                parent: "Key",
            },

            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },

    {
        actionName: "Integrate",
        name: "Big Boss Delivery",
        ref: "bigboss",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/bigboss.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "token",
                type: "text",
                placeholder: "client_token",
                required: true,
                parent: "Key",
            },

            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "High Delivery",
        ref: "highdelivery",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/highdelivery.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "code_api",
                type: "text",
                placeholder: "Code API",
                required: true,
                parent: "Key",
            },
            {
                name: "cle_api",
                type: "textarea",
                placeholder: "Cle API",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Macropost",
        ref: "macropost",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/macropost.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "client_id",
                type: "text",
                placeholder: "Client ID",
                required: true,
                parent: "Key",
            },
            {
                name: "api_key",
                type: "textarea",
                placeholder: "API Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Calirex Delivery",
        ref: "calirex",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/calirex.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Calirex Username",
                required: true,
                parent: "Key",
            },
            {
                name: "key",
                type: "textarea",
                placeholder: "Calirex Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Massar Delivery",
        ref: "massar",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/massar.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Massar Username",
                required: true,
                parent: "Credentials",
            },
            {
                name: "password",
                type: "text",
                placeholder: "Massar Password",
                required: true,
                parent: "Credentials",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "allowToOpenPackage",
                type: "select",
                placeholder: "Allow to open package",
                options: ["Yes", "No"],
                parent: "Label",
            },
            {
                name: "fragile",
                type: "select",
                placeholder: "Fragile",
                options: ["Yes", "No"],
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "QwikPak",
        ref: "qwikpak",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/qwikpak.webp",
        allowedCountries: ["TN"],
        allowedStores: [
            "best-store",
            "tungadgets",
            "go-shoes",
            "yabalech",
            "9alb-for-you",
            "ri9ek-tn",
            "hand-shopp",
            "petit-ingenieur",
            "store-17",
            "gold-store",
            "reflex-ball",
            "nice-price",
            "vos-besoins",
            "baby-needs",
        ],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "key",
                type: "textarea",
                placeholder: "API Key",
                required: true,
                parent: "Key",
            },
            {
                name: "allowToOpenPackage",
                type: "select",
                placeholder: "Allow to open package",
                options: ["Yes", "No"],
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },

            // {
            //     name: "fragile",
            //     type: "select",
            //     placeholder: "Fragile",
            //     options: ["Yes", "No"],
            //     parent: "Label",
            // },
        ],
    },
    {
        actionName: "Integrate",
        name: "Cosmos",
        ref: "cosmos",
        shortDescription: "✅ Add Order | ✅ Tracking | ✅ Labels",
        logo: CDN + "assets/integrations/cosmos.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "key",
                type: "textarea",
                placeholder: "API Key",
                required: true,
                parent: "Key",
            },
            {
                name: "allowToOpenPackage",
                type: "select",
                placeholder: "Allow to open package",
                options: ["Yes", "No"],
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Kamatcho",
        ref: "kamatcho",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/kamatcho.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "id",
                type: "text",
                placeholder: "ID",
                required: true,
                parent: "Key",
            },
            {
                name: "token",
                type: "textarea",
                placeholder: "Token",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
            {
                name: "vatNumber",
                type: "text",
                placeholder: "Store Matricule Fiscale",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Bigboss Delivery",
        ref: "bigbossv2",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/bigboss.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Bigboss Username",
                required: true,
                parent: "Key",
            },
            {
                name: "key",
                type: "textarea",
                placeholder: "Bigboss Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Tiktak",
        ref: "tiktak",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/tiktak.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "id",
                type: "text",
                placeholder: "ID",
                required: true,
                parent: "Key",
            },
            {
                name: "token",
                type: "text",
                placeholder: "Token",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },

    {
        actionName: "Integrate",
        name: "Mz Logistic",
        ref: "mzlogistic",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/mzlogistic.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "token",
                type: "textarea",
                placeholder: "Token",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Send Ex",
        ref: "sendex",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/sendex.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "token",
                type: "textarea",
                placeholder: "Token",
                required: true,
                parent: "Key",
            },
            {
                name: "Trackingtoken",
                type: "textarea",
                placeholder: "Tracking Token",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "LogIn",
        ref: "login",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/login.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "key",
                type: "textarea",
                placeholder: "Key",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },

            {
                name: "validation",
                type: "select",
                placeholder: "Do you want validate the orders",
                options: ["Yes", "No"],
                parent: "Label",
            },

            {
                name: "allowToOpenPackage",
                type: "select",
                placeholder: "Allow to open package",
                options: ["Yes", "No"],
                parent: "Label",
            },

            {
                name: "fragile",
                type: "select",
                placeholder: "Fragile",
                options: ["Yes", "No"],
                parent: "Label",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name",
                parent: "Label",
            },
            {
                name: "storePhone",
                type: "text",
                placeholder: "Store Phone",
                parent: "Label",
            },
            {
                name: "storeAddress",
                type: "text",
                placeholder: "Store Address",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "Insta Delivery",
        ref: "instadelivery",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/instadelivery.webp",
        allowedCountries: ["TN"],
        inputs: ["name", "phone", "city", "address"],
        fields: [
            {
                name: "username",
                type: "text",
                placeholder: "Insta Username",
                required: true,
                parent: "Credentials",
            },
            {
                name: "password",
                type: "text",
                placeholder: "Insta Password",
                required: true,
                parent: "Credentials",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
            {
                name: "allowToOpenPackage",
                type: "select",
                placeholder: "Allow to open package",
                options: ["Yes", "No"],
                parent: "Label",
            },
            {
                name: "fragile",
                type: "select",
                placeholder: "Fragile",
                options: ["Yes", "No"],
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",

        name: "Abm Delivery",

        ref: "abm",

        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",

        logo: CDN + "assets/integrations/abm.webp",

        allowedCountries: ["TN"],

        inputs: ["name", "phone", "city", "address"],

        fields: [
            {
                name: "AbmDeliveryKey",

                type: "textarea",

                placeholder: "Abm Delivery Key",

                required: true,

                parent: "Key",
            },

            {
                name: "deliveryCost",

                type: "number",

                placeholder: "Delivery Cost",

                parent: "Cost",
            },

            {
                name: "returnCost",

                type: "number",

                placeholder: "Return Cost",

                parent: "Cost",
            },

            {
                name: "storeName",

                type: "text",

                placeholder: "Store Name (For Abm Delivery)",

                parent: "Label",
            },

            {
                name: "storePhone",

                type: "text",

                placeholder: "Store Phone (For Abm Delivery)",

                parent: "Label",
            },

            {
                name: "storeAddress",

                type: "text",

                placeholder: "Store Address (For Abm Delivery)",

                parent: "Label",
            },

            {
                name: "storeCity",

                type: "select",

                placeholder: "Store City (For Abm Delivery)",

                options: [
                    "Ariana",

                    "Beja",

                    "Ben Arous",

                    "Bizerte",

                    "Gabes",

                    "Gafsa",

                    "Jendouba",

                    "Kasserine",

                    "Kef",

                    "Mahdia",

                    "Manouba",

                    "Monastir",

                    "Nabeul",

                    "Sfax",

                    "Sidi Bouzid",

                    "Sousse",

                    "Siliana",

                    "Tataouine",

                    "Tozeur",

                    "Tunis",

                    "Zaghouan",

                    "Medenine",

                    "Kebili",

                    "Kairouan",
                ],

                parent: "Label",
            },
        ],
    },

    {
        actionName: "Integrate",
        name: "Ecotrack",
        ref: "ecotrack",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/ecotrack.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["DZ"],
        fields: [
            {
                name: "token",
                type: "textarea",
                placeholder: "Token",
                required: true,
                parent: "Key",
            },
            {
                name: "costPreset",
                type: "dz-wilaya-preset-selector",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },

            {
                name: "wilaya",
                type: "select",
                placeholder: "Wilaya",
                options: algerianCitites,
                parent: "Label",
            },
            {
                name: "commune",
                type: "text",
                placeholder: "Store Commune",

                parent: "Label",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name ",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "WordExpress",
        ref: "wordexpress",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/ecotrack.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["DZ"],
        fields: [
            {
                name: "token",
                type: "textarea",
                placeholder: "Token",
                required: true,
                parent: "Key",
            },
            {
                name: "costPreset",
                type: "dz-wilaya-preset-selector",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },

            {
                name: "wilaya",
                type: "select",
                placeholder: "Wilaya",
                options: algerianCitites,
                parent: "Label",
            },
            {
                name: "commune",
                type: "text",
                placeholder: "Store Commune",

                parent: "Label",
            },
            {
                name: "storeName",
                type: "text",
                placeholder: "Store Name ",
                parent: "Label",
            },
        ],
    },
    {
        actionName: "Integrate",
        name: "SendEx",
        ref: "sendex",
        shortDescription: "✅ Add Order | ❌ Tracking | ❌ Labels",
        logo: CDN + "assets/integrations/sendex.webp",
        inputs: ["name", "phone", "city", "address"],
        allowedCountries: ["TN"],
        fields: [
            {
                name: "token",
                type: "textarea",
                placeholder: "Token",
                required: true,
                parent: "Key",
            },
            {
                name: "deliveryCost",
                type: "number",
                placeholder: "Delivery Cost",
                parent: "Cost",
            },
            {
                name: "returnCost",
                type: "number",
                placeholder: "Return Cost",
                parent: "Cost",
            },
        ],
    },
];
