import { Product, Variant } from "./Product";

export type Order = {
    _id: string;
    reference: number;
    customer: any;
    note?: string;
    cart?: Cart;
    status?: Status;
    attempt?: number;
    total?: {
        deliveryPrice?: number;
        deliveryCost?: number;
        basePrice?: number;
        totalPrice?: number;
    };
    deliveryCompany?: string; //"none" | "aramex" | "adex" | "intigo" | "first" | "mylerz"
    deliveryTrackable?: boolean;
    label?: Object;
    barcode?: string;
    history?: History[];
    store?: string;
    archived: boolean;
    duplicated: boolean;
    isDeleted?: boolean;
    expiryDate?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    __v?: number;
    count?: number;
    paymentStatus?: string;
    billedAmount?: number;
    refunded?: boolean;
    isTest?: boolean;
};

export type Cart = {
    product: Partial<Product>;
    quantity: number;
    selectedVariants: {
        name: string;
        type: Variant["type"];
        value: string;
    }[];
    pricePerUnit: number;
}[];

export type History = {
    status?: Status;
    timestamp?: Date;
    actionTaker?: string;
    attempt?: number;
    rejectedReason?: string;
};

export type Status =
    | "abandoned"
    | "pending"
    | "attempt"
    | "confirmed"
    | "uploaded"
    | "exchange"
    | "rejected"
    | "picked"
    | "in transit"
    | "in transit2"
    | "rescheduled"
    | "cancelled"
    | "delivered"
    | "paid"
    | "unverified"
    | "returned"
    | "received"
    | "deleted"
    | "restored";
