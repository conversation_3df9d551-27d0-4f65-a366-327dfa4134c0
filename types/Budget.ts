import { Store } from "./Store";

export type Budget = {
    _id: string;
    name: string;
    slug: string;
    reference: number;
    description?: string;
    startingBalance: number;
    balance?: number;
    expenses?: Expense[];
    revenues?: Revenue[];
    store?: string | Store;
    isDeleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
};

export type Expense = {
    name: string;
    subCategories: SubCategory[];
};

export type Revenue = {
    name: string;
    subCategories: SubCategory[];
};

export type SubCategory = {
    name: string;
    amount: number;
    createdAt: Date;
};
