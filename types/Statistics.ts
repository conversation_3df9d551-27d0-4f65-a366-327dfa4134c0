export type OrderSatistics = {
    dailyOrders: DashboardStatResult;
    thisWeekOrders: DashboardStatResult;
    thisMonthOrders: DashboardStatResult;
    totalOrders: DashboardStatResult;
    last7DaysDetailed: DashboardWeeklyDigest[];
    pendingOrdersTotal: number;
    abandonedOrdersTotal: number;
    totalDelivered: number;
    totalReturned: number;
};

type DashboardStatResult = {
    count: number;
    totalValue: number;
};

type DashboardWeeklyDigest = {
    date: Date;
    count: number;
    totalValue: number;
};

export type Stats = {
    stats: {
        date: string;
        adStats: Record<
            string,
            Record<
                string,
                {
                    rejectedValue: number;
                    rejectedCount: number;
                    pendingValue: number;
                    pendingCount: number;
                    confirmedValue: number;
                    confirmedCount: number;
                    deliveredValue: number;
                    deliveredCount: number;
                    deliveredProfit: number;
                    returnedValue: number;
                    returnedCount: number;
                    returnedCost: number;
                    inTransitValue: number;
                    inTransitCount: number;
                    deletedValue: number;
                    deletedCount: number;
                    totalValue: number;
                    totalCount: number;
                }
            >
        >;
        deliveryStats: Record<
            string,
            {
                deliveredOrders: number;
                deliveredOrdersValue: number;
                deliveredOrdersPayment: number;
                deliveredOrdersProfit: number;
                returnedOrders: number;
                returnedOrdersCost: number;
                depositOrders: number;
                depositOrdersValue: number;
                depositOrdersPayment: number;
                depositOrdersProfit: number;
                inTransitOrders: number;
                inTransitOrdersValue: number;
                inTransitOrdersPayment: number;
                inTransitOrdersProfit: number;
                unverifiedOrders: number;
                avgDeliveryTime: number;
            }
        >;
        adminStats: Record<
            string,
            {
                confirmedOrders: number;
                rejectedOrders: number;
                createdOrders: number;
                deletedOrders: number;
                receivedOrders: number;
                packedOrders: number;
                attempts: number;
                firstAttempts: number;
                avgConfirmationTime: number;
                avgTimeToFirstAttempt: number;
                rejectionReasons: Record<string, number>;
            }
        >;
        productStats: Record<
            string,
            {
                deposit: number;
                depositValue: number;
                depositProfit: number;
                transit: number;
                transitValue: number;
                transitProfit: number;
                delivered: number;
                deliveredValue: number;
                deliveredProfit: number;
                returned: number;
                returnedCost: number;
                unverified: number;
                total: number;
                totalValue: number;
                totalProfit: number;
            }
        >;
    }[];
    campaigns: Record<
        string,
        {
            name: string;
            adsets: Record<
                string,
                {
                    name: string;
                    ads: Record<
                        string,
                        {
                            id: string;
                            name: string;
                            spend: number;
                        }
                    >;
                }
            >;
        }
    >;
};
