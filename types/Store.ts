import { Image } from "./Image";

export type Store = {
    _id: string;
    user?: string | User; //{ _id: string; firstname: string; lastname: string; email: string; notificationTokens?: string[]; };
    staff?: string[];
    name?: string;
    slug?: string;
    domain?: string;
    logo?: Image;
    title?: string;
    seoDescription?: string;
    extraPages?: any[];
    templateValues?: Record<string, Record<string, any>>;
    preferences?: {
        staffLimit?: number;
        imageQuality?: string;
        maxOrderQuantity?: number | null;
        maxOrderAmount?: number | null;
        abandonedOrders?: boolean;
    };

    // new
    checkout?: {
        position?: string;
        type?: "express" | "cart" | "both";
        fields?: CheckoutField[];
    };
    checkoutFields?: CheckoutField[];

    // new
    contactInfo?: any;

    integrations?: {
        integration?: any; // to be defined later
        ref?: string;
        integrated?: boolean;
        lead?: string;
        fields?: any; // should be defined later
    }[];
    payment?: {
        amount?: number;
        month?: number;
        date?: Date;
        expirationDate?: Date;
    };
    receipt?: {
        name?: string;
        email?: string;
        phone?: string;
        address?: string;
        tax?: string;
        logo?: string;
        website?: string;
        specialNote?: string;
    };
    step?: number;
    createdAt?: Date;
    updatedAt?: Date;
    theme?: string;
    __v?: number;

    // new
    sharedValues?: {
        darkmode?: string;
        language?: string;
        header?: {
            text: string;
            sticky: string;
        };
        CTA?: {
            sticky: string; // "on" | "off" | "shaky"
            color: string;
            text: string;
            textColor: string;
        };
        search?: string;
        quantity?: string;
        stickyNavbar?: string;
        thankYouText?: string;
        css?: string;
        backgroundColor?: string;
    };
    country?: {
        code: string;
        name: string;
        flag: string;
        subdivisionsName: string;
        subdivisions: string[];
        phoneRegex: string;
    };
    currency?: {
        code: string;
        name: string;
        symbol: string;
        num: number;
        decimals: number;
    };
    // deprecated
    language?: string;
    thankYouMessage?: string;
    css?: string;
    invitationCodes?: string[];
    invitedBy?: string | { _id: string; slug: string };
    paymentInfo?: {
        balance: number;
        outstandingBalance: number;
        billingRate: number;
        minimumBillingAmount: number;
        maximumBillingAmount: number;
        billingCurrency?: {
            code: string;
            name: string;
            symbol: string;
            num: number;
            decimals: number;
        };
    };
};

export type CheckoutField = {
    name: string;
    label?: string;
    placeholder?: string;
    type: "text" | "email" | "number" | "select" | "textarea" | "checkbox" | "radio";
    required: boolean;
    options?: string[];
    regex?: string;
    invalidText?: string;
};

export type notificationToken = {
    timestamp: string;
    token: string;
    type: string;
};

export type User = {
    _id?: string;
    firstname?: string;
    lastname?: string;
    phone?: string;
    username?: string;
    email?: string;
    isVerified?: boolean;
    role?: string;
    store?: string;
    stores: string[] | Store[];
    preferences: any;
    notificationTokens?: notificationToken[];
    permissions:
        | {
              [key: string]: {
                  read: boolean;
                  create: boolean;
                  update: boolean;
                  delete: boolean;
              };
          }
        | "all";
};
