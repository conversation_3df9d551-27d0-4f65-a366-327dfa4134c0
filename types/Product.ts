import { Category } from "./Category";
import { Image } from "./Image";

export type ProductStatistics = {
    stockValue: number;
    totalRevenue: number;
    totalStock: number;
};

export type Combination = {
    stock?: number;
    quantity?: number;
    price?: number;
    comparePrice?: number;
    deliveryPrice?: number;
    image?: string;
    sku?: string;
    available?: boolean; // only for clientCombinations
};

export type Product = {
    _id: string;
    reference: number;
    name: string;
    slug: string;
    sku?: string;
    description: string;
    images: Image[];
    //
    price: number;
    comparePrice: number;
    cost: number;
    //
    deliveryPrice: number;
    deliveryCost?: number;
    //
    discounts: Discount[];
    coupons: Coupon[];
    //
    trackStock: boolean;
    stock: number;
    damagedStock: number;
    //
    fakeViews?: number;
    fakePeriod?: number;
    fakeStock?: number;
    //
    variants: Variant[];
    reviews?: Review[];
    upsell?: string;
    relatedProducts?: Partial<Product>[] | string[];
    categories: Category[] | string[];
    campaignIds?: string[];
    //
    createdAt?: Date;
    updatedAt?: Date;
    isDeleted?: boolean;
    expiryDate?: Date | null;
    store?: string;
    __v?: number;
    //

    templateValues?: Record<string, Record<string, any>>;
    integrationValues?: Record<string, Record<string, any>>;

    // new field
    combinations?: Record<string, Combination>;
    clientCombinations: Object;
    defaultCombination: [];
    status: string; // has three values for now: shown hidden and outOfStock

    //deprecated
    cpp?: number;
    campaignId?: string[];
    crossSell: CrossSell[];
    discount: Discount[];
    isHidden?: boolean;
};

export type Variant = {
    name?: string;
    type?: "text" | "color" | "image";
    values?: string[];
};

export type Review = {
    name: string;
    comment?: string;
    rating: number;
    // image?: Image;
    image?: string;
};

export type Upsell = {
    product: Partial<Product>;
    price: number;
};

export type CrossSell = {
    _id: string;
    id?: number;
    name?: string;
    image?: string;
};

export type Discount = {
    quantity: number;
    price: number;
};

export type Coupon = {
    code: string;
    discount: number;
    type: "percent" | "fixed" | "free_delivery" | "BOGO";
};

export type ProductList = {
    _id: string;
    slug: string;
    reference: number;
    name: string;
    price: number;
    variants: Variant[];
    deliveryPrice: number;
    images: Partial<Image>[];
};
