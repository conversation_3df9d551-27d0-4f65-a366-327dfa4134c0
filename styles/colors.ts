export type colorsProp = Record<
    | "white"
    | "black"
    | "primary"
    | "secondary"
    | "blue"
    | "green"
    | "red"
    | "orange"
    | "teal"
    | "cyan"
    | "pink"
    | "gray"
    | "blackAlpha"
    | "whiteAlpha",
    string | Record<50 | 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900, string>
>;
// ! applying the colors Prop to the colors object instantly crate a bug in typography
const colors = {
    white: "#ffffff",
    black: "#000000",
    primary: {
        50: "#f0eaf7",
        100: "#e1d5ef",
        200: "#c3abdf",
        300: "#a482d0",
        400: "#8658c0",
        500: "#682eb0",
        600: "#53258d",
        700: "#3e1c6a",
        800: "#2a1246",
        900: "#150923",
    },
    secondary: {
        50: "#fffbe6",
        100: "#fff7cc",
        200: "#ffef99",
        300: "#ffe766",
        400: "#ffdf33",
        500: "#ffd700",
        600: "#ccac00",
        700: "#998100",
        800: "#665600",
        900: "#332b00",
    },
    blue: {
        50: "#f0f2ff",
        100: "#e1e4ff",
        200: "#c3caff",
        300: "#a6afff",
        400: "#8895ff",
        500: "#6a7aff",
        600: "#5563d9",
        700: "#404cb3",
        800: "#2a358d",
        900: "#151e67",
    },
    green: {
        50: "#f0fff4",
        100: "#c6f6d5",
        200: "#9ae6b4",
        300: "#68d391",
        400: "#48bb78",
        500: "#38a169",
        600: "#25855a",
        700: "#276749",
        800: "#22543d",
        900: "#1c4532",
    },
    red: {
        50: "#fff5f5",
        100: "#fed7d7",
        200: "#feb2b2",
        300: "#fc8181",
        400: "#f56565",
        500: "#e53e3e",
        600: "#c53030",
        700: "#9b2c2c",
        800: "#822727",
        900: "#63171b",
    },

    orange: {
        50: "#fffaf0",
        100: "#feebc8",
        200: "#fbd38d",
        300: "#f6ad55",
        400: "#ed8936",
        500: "#dd6b20",
        600: "#c05621",
        700: "#9c4221",
        800: "#7b341e",
        900: "#652b19",
    },
    teal: {
        50: "#e6fffa",
        100: "#b2f5ea",
        200: "#81e6d9",
        300: "#4fd1c5",
        400: "#38b2ac",
        500: "#319795",
        600: "#2c7a7b",
        700: "#285e61",
        800: "#234e52",
        900: "#1d4044",
    },
    cyan: {
        50: "#edfdfd",
        100: "#c4f1f9",
        200: "#9decf9",
        300: "#76e4f7",
        400: "#0bc5ea",
        500: "#00b5d8",
        600: "#00a3c4",
        700: "#0987a0",
        800: "#086f83",
        900: "#065666",
    },
    pink: {
        50: "#fff5f7",
        100: "#fed7e2",
        200: "#fbb6ce",
        300: "#f687b3",
        400: "#ed64a6",
        500: "#d53f8c",
        600: "#b83280",
        700: "#97266d",
        800: "#702459",
        900: "#521b41",
    },
    gray: {
        50: "#f7fafc",
        100: "#edf2f7",
        200: "#e2e8f0",
        300: "#cbd5e0",
        400: "#a0aec0",
        500: "#718096",
        600: "#4a5568",
        700: "#2d3748",
        800: "#1a202c",
        900: "#171923",
    },
    blackAlpha: {
        50: "rgba(0, 0, 0, 0.04)",
        100: "rgba(0, 0, 0, 0.06)",
        200: "rgba(0, 0, 0, 0.08)",
        300: "rgba(0, 0, 0, 0.16)",
        400: "rgba(0, 0, 0, 0.24)",
        500: "rgba(0, 0, 0, 0.36)",
        600: "rgba(0, 0, 0, 0.48)",
        700: "rgba(0, 0, 0, 0.64)",
        800: "rgba(0, 0, 0, 0.8)",
        900: "rgba(0, 0, 0, 0.92)",
    },
    whiteAlpha: {
        50: "rgba(255, 255, 255, 0.04)",
        100: "rgba(255, 255, 255, 0.06)",
        200: "rgba(255, 255, 255, 0.08)",
        300: "rgba(255, 255, 255, 0.16)",
        400: "rgba(255, 255, 255, 0.24)",
        500: "rgba(255, 255, 255, 0.36)",
        600: "rgba(255, 255, 255, 0.48)",
        700: "rgba(255, 255, 255, 0.64)",
        800: "rgba(255, 255, 255, 0.8)",
        900: "rgba(255, 255, 255, 0.92)",
    },
};

export default colors;
