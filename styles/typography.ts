import { StyleSheet } from "react-native";

const FONT_FAMILY = "Inter-Regular";
export const fonts = {
    "LibreBarcode39Text-Regular": require("../assets/fonts/Libre-Barcode-39/LibreBarcode39Text-Regular.ttf"),
    "Inter-Black": require("../assets/fonts/Inter/Inter-Black.ttf"),
    "Inter-Bold": require("../assets/fonts/Inter/Inter-Bold.ttf"),
    "Inter-ExtraBold": require("../assets/fonts/Inter/Inter-ExtraBold.ttf"),
    "Inter-ExtraLight": require("../assets/fonts/Inter/Inter-ExtraLight.ttf"),
    "Inter-Light": require("../assets/fonts/Inter/Inter-Light.ttf"),
    "Inter-Medium": require("../assets/fonts/Inter/Inter-Medium.ttf"),
    "Inter-Regular": require("../assets/fonts/Inter/Inter-Regular.ttf"),
    "Inter-SemiBold": require("../assets/fonts/Inter/Inter-SemiBold.ttf"),
    "Inter-Thin": require("../assets/fonts/Inter/Inter-Thin.ttf"),
    "Poppins-Black": require("../assets/fonts/Poppins/Poppins-Black.ttf"),
    "Poppins-BlackItalic": require("../assets/fonts/Poppins/Poppins-BlackItalic.ttf"),
    "Poppins-Bold": require("../assets/fonts/Poppins/Poppins-Bold.ttf"),
    "Poppins-BoldItalic": require("../assets/fonts/Poppins/Poppins-BoldItalic.ttf"),
    "Poppins-ExtraBold": require("../assets/fonts/Poppins/Poppins-ExtraBold.ttf"),
    "Poppins-ExtraBoldItalic": require("../assets/fonts/Poppins/Poppins-ExtraBoldItalic.ttf"),
    "Poppins-ExtraLight": require("../assets/fonts/Poppins/Poppins-ExtraLight.ttf"),
    "Poppins-ExtraLightItalic": require("../assets/fonts/Poppins/Poppins-ExtraLightItalic.ttf"),
    "Poppins-Light": require("../assets/fonts/Poppins/Poppins-Light.ttf"),
    "Poppins-LightItalic": require("../assets/fonts/Poppins/Poppins-LightItalic.ttf"),
    "Poppins-Medium": require("../assets/fonts/Poppins/Poppins-Medium.ttf"),
    "Poppins-MediumItalic": require("../assets/fonts/Poppins/Poppins-MediumItalic.ttf"),
    "Poppins-Regular": require("../assets/fonts/Poppins/Poppins-Regular.ttf"),
    "Poppins-Italic": require("../assets/fonts/Poppins/Poppins-Italic.ttf"),
    "Poppins-SemiBold": require("../assets/fonts/Poppins/Poppins-SemiBold.ttf"),
    "Poppins-SemiBoldItalic": require("../assets/fonts/Poppins/Poppins-SemiBoldItalic.ttf"),
    "Poppins-Thin": require("../assets/fonts/Poppins/Poppins-Thin.ttf"),
    "Poppins-ThinItalic": require("../assets/fonts/Poppins/Poppins-ThinItalic.ttf"),
};
export const typography = StyleSheet.create({
    // Base styles
    baseText: {
        fontFamily: FONT_FAMILY,
        textAlign: "left",
    },

    altText: {
        fontFamily: "Poppins-Regular",
    },
    altTextMedium: {
        fontFamily: "Poppins-Medium",
    },
    altTextBold: {
        fontFamily: "Poppins-Bold",
    },
    altTextSemiBold: {
        fontFamily: "Poppins-SemiBold",
    },

    // Size styles
    xxs: {
        fontSize: 10,
    },
    xs: {
        fontSize: 12,
    },
    sm: {
        fontSize: 14,
    },
    md: {
        fontSize: 16,
    },
    lg: {
        fontSize: 18,
    },
    xl: {
        fontSize: 24,
    },
    xxl: {
        fontSize: 36,
    },
    xxxl: {
        fontSize: 48,
    },
    xxxxl: {
        fontSize: 60,
    },

    // Weight styles
    fontNormal: {
        fontFamily: "Inter-Regular",
    },
    fontMedium: {
        fontFamily: "Inter-Medium",
    },
    fontSemibold: {
        fontFamily: "Inter-SemiBold",
    },
    fontBold: {
        fontFamily: "Inter-Bold",
    },

    //lineHeight styles
    lineHeight4: {
        lineHeight: 16,
    },
    lineHeight5: {
        lineHeight: 20,
    },
    lineHeight8: {
        lineHeight: 32,
    },

    // Color styles
    black: {
        color: "#000000",
    },
    grey: {
        color: "#ffffff",
    },
});
