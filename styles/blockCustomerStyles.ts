import { StyleSheet } from "react-native";
import colors from "./colors";
import { typography } from "./typography";

export const blockCustomerStyles = StyleSheet.create({
    sheetBackground: {
        backgroundColor: colors.white,
    },
    handleIndicator: {
        backgroundColor: colors.gray[400],
        width: 40,
    },
    container: {
        flex: 1,
        paddingHorizontal: 20,
    },
    header: {
        gap: 16,
        paddingTop: 12,
    },
    totalOrders: {
        ...typography.lg,
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.gray[900],
    },
    ordersSection: {
        flex: 1,
        marginVertical: 16,
        width: "100%",
    },
    footer: {
        gap: 16,
        paddingBottom: 30,
        backgroundColor: colors.white,
    },
    switchContainer: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: colors.gray[100],
        padding: 12,
        borderRadius: 12,
        gap: 12,
    },
    switchText: {
        ...typography.sm,
        color: colors.gray[700],
        flex: 1,
    },
    buttonContainer: {
        flexDirection: "row",
        gap: 12,
    },
    cancelButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: "center",
        justifyContent: "center",
        borderWidth: 1,
        borderColor: colors.gray[300],
    },
    blockButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: colors.red[500],
    },
    cancelButtonText: {
        ...typography.sm,
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.gray[700],
    },
    blockButtonText: {
        ...typography.sm,
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.white,
    },
    totalOrdersContainer: {
        flexDirection: "row",
        alignItems: "center",
        gap: 8,
    },
    totalOrdersNumber: {
        ...typography.lg,
        fontFamily: typography.fontBold.fontFamily,
        color: colors.primary[600],
    },
});
